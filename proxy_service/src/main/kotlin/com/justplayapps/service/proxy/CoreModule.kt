package com.justplayapps.service.proxy

import com.google.inject.*
import com.google.inject.name.Named
import com.google.inject.name.Names
import com.justplayapps.service.proxy.paypal.PayPalVariant
import com.justplayapps.service.proxy.paypal.override.JustPlayPayPalHttpClient
import com.justplayapps.service.proxy.secret.SecretService
import com.justplayapps.service.proxy.util.BuildVariant
import com.justplayapps.service.proxy.util.IoCoroutineScope
import com.justplayapps.service.proxy.util.getDefaultJsonConverter
import io.ktor.application.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.runBlocking

class CoreModule(private val application: Application, private val buildVariant: BuildVariant) : AbstractModule() {

  companion object {
    const val COROUTINE_SCOPE_IO = "coroutine-scope-io"
    const val PAYPAL_HTTP_CLIENT = "paypal-http-client"
    const val PAYPAL_HTTP_CLIENT_JUSTPLAY = "paypal-http-client-justplay"
    const val PAYPAL_HTTP_CLIENT_USD = "paypal-http-client-usd"
    const val PAYPAL_HTTP_CLIENT_GMC = "paypal-http-client-gmc"

    private val gaeServiceVersion: String by lazy {
      System.getenv("GAE_VERSION") ?: "unknown"
    }
  }

  override fun configure() {
    if (buildVariant == BuildVariant.TEST && !gaeServiceVersion.startsWith("test")) {
      throw RuntimeException("Trying to deploy 'TEST' environment with not 'TEST' version")
    }
    if (buildVariant == BuildVariant.PRODUCTION && gaeServiceVersion.startsWith("test")) {
      throw RuntimeException("Trying to deploy 'PRODUCTION' environment with 'TEST' version")
    }
    bind(Application::class.java).toInstance(application)
    bind(IoCoroutineScope::class.java).toProvider { IoCoroutineScope() }
    bind(Key.get(CoroutineScope::class.java, Names.named(COROUTINE_SCOPE_IO))).toProvider(coroutineScopeIO).`in`(Scopes.NO_SCOPE)
  }

  private var coroutineScopeIO: Provider<out CoroutineScope> = Provider { CoroutineScope(Dispatchers.IO + SupervisorJob()) }

  @Provides
  fun buildVariant() = buildVariant

  @Provides
  fun json() = getDefaultJsonConverter()

  @Provides
  @Named(PAYPAL_HTTP_CLIENT)
  fun paypalHttpClient(secretService: SecretService) = runBlocking {
    JustPlayPayPalHttpClient(
      if (buildVariant == BuildVariant.PRODUCTION)
        PayPalVariant.LIVE.environment(secretService)
      else
        PayPalVariant.SANDBOX.environment(secretService)
    )
  }

  @Provides
  @Named(PAYPAL_HTTP_CLIENT_JUSTPLAY)
  fun paypalHttpClientJustPlay(secretService: SecretService) = runBlocking {
    JustPlayPayPalHttpClient(
      if (buildVariant == BuildVariant.PRODUCTION)
        PayPalVariant.JUSTPLAY_LIVE.environment(secretService)
      else
        PayPalVariant.JUSTPLAY_SANDBOX.environment(secretService)
    )
  }

  @Provides
  @Named(PAYPAL_HTTP_CLIENT_USD)
  fun paypalHttpClientUsd(secretService: SecretService) = runBlocking {
    JustPlayPayPalHttpClient(
      if (buildVariant == BuildVariant.PRODUCTION)
        PayPalVariant.USD_LIVE.environment(secretService)
      else
        PayPalVariant.USD_SANDBOX.environment(secretService)
    )
  }

  @Provides
  @Named(PAYPAL_HTTP_CLIENT_GMC)
  fun paypalHttpClientGmc(secretService: SecretService) = runBlocking {
    JustPlayPayPalHttpClient(
      if (buildVariant == BuildVariant.PRODUCTION)
        PayPalVariant.GMC_LIVE.environment(secretService)
      else
        PayPalVariant.GMC_SANDBOX.environment(secretService)
    )
  }
}
