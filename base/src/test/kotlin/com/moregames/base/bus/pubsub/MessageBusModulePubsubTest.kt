package com.moregames.base.bus.pubsub

import assertk.all
import assertk.assertThat
import assertk.assertions.isEqualTo
import com.google.api.gax.core.NoCredentialsProvider
import com.google.api.gax.grpc.GrpcTransportChannel
import com.google.api.gax.rpc.FixedTransportChannelProvider
import com.google.api.gax.rpc.TransportChannelProvider
import com.google.cloud.pubsub.v1.SubscriptionAdminClient
import com.google.cloud.pubsub.v1.SubscriptionAdminSettings
import com.google.cloud.pubsub.v1.TopicAdminClient
import com.google.cloud.pubsub.v1.TopicAdminSettings
import com.google.inject.AbstractModule
import com.google.inject.Guice
import com.google.inject.Injector
import com.google.protobuf.stringValue
import com.google.pubsub.v1.PushConfig
import com.google.pubsub.v1.SubscriptionName
import com.google.pubsub.v1.TopicName
import com.justplayapps.base.test.Test.TestResponse
import com.justplayapps.base.test.TestMessageBusOuterClass.TestMessageBus
import com.justplayapps.base.test.testResponse
import com.moregames.base.app.BuildVariant
import com.moregames.base.bus.Message
import com.moregames.base.bus.MessageBus
import com.moregames.base.bus.MessageBusModule
import com.moregames.base.bus.MessageHandler
import com.moregames.base.bus.config.PubsubTransport
import com.moregames.base.bus.pubsub.MessageBusModulePubsubTest.AcceptExampleMessageHook
import com.moregames.base.lifecycle.ServiceManagerModule
import com.moregames.base.lifecycle.getServiceManager
import com.moregames.base.messaging.DelayedMessagePublisher
import com.moregames.base.messaging.GenericMessagePublisher
import com.moregames.base.messaging.PublisherCreator
import com.moregames.base.messaging.auth.AuthService
import com.moregames.base.util.Constants.GOOGLE_CLOUD_PROJECT_ID
import com.moregames.base.util.TEST_IO_SCOPE
import com.moregames.base.util.TimeService
import io.grpc.ManagedChannel
import io.grpc.ManagedChannelBuilder
import io.ktor.application.*
import io.ktor.features.*
import io.ktor.serialization.*
import io.ktor.server.engine.*
import io.ktor.server.netty.*
import io.ktor.server.testing.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import kotlinx.serialization.json.Json
import org.awaitility.Awaitility.await
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import org.testcontainers.Testcontainers.exposeHostPorts
import org.testcontainers.containers.PubSubEmulatorContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import org.testcontainers.utility.DockerImageName
import java.time.Duration
import java.time.Instant
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

@Testcontainers
class MessageBusModulePubsubTest {
  @Container
  val pubSubEmulator = run {
    val dockerImage = if (System.getProperty("os.arch") == "aarch64") {
      DockerImageName.parse("europe-central2-docker.pkg.dev/playspot-server-dev/gcloud-emulators-arm/cloud-sdk:440.0.0-emulators")
        .asCompatibleSubstituteFor("gcr.io/google.com/cloudsdktool/cloud-sdk")
    } else {
      DockerImageName.parse("gcr.io/google.com/cloudsdktool/cloud-sdk:440.0.0-emulators")
    }
    PubSubEmulatorContainer(dockerImage).withAccessToHost(true)
  }

  private lateinit var channel: ManagedChannel
  private lateinit var channelProvider: TransportChannelProvider
  private lateinit var injector: Injector

  private val acceptExampleMessageHook = mock<AcceptExampleMessageHook>()
  private val timeService = mock<TimeService>()

  private val now = Instant.now()

  @BeforeEach
  fun before() {
    Dispatchers.setMain(StandardTestDispatcher())
    channel = ManagedChannelBuilder.forTarget(pubSubEmulator.emulatorEndpoint).usePlaintext().build()
    channelProvider = FixedTransportChannelProvider.create(GrpcTransportChannel.create(channel))

    channelProvider.createTopicAndPullSubscription(EXAMPLE_MESSAGE_TOPIC_NAME, SUBSCRIPTION_NAME)
    channelProvider.createTopicAndPullSubscription("second-topic", SUBSCRIPTION_NAME)
    channelProvider.createTopicAndPullSubscription("publish-only", SUBSCRIPTION_NAME)
  }

  @AfterEach
  fun tearDown() {
    injector.getServiceManager().stopAsync().awaitStopped()
    channel.shutdownNow().awaitTermination(5, TimeUnit.SECONDS)
  }

  private fun bootstrap(): Application.() -> Unit = {
    val app = this

    injector = Guice.createInjector(MessageBusModule {
      rootPackages("com.moregames.base.bus.pubsub")

      pullEndpoint(EXAMPLE_MESSAGE_TOPIC_NAME, default = true)
      val secondTopic by pullEndpoint("second-topic")
      val defaultPush by pushEndpoint("topic-for-push")
      val publishOnly by publishEndpoint("publish-only")

      routing {
        route<ExampleMessageOtherTopic>() to secondTopic
        route<ExampleMessagePushTopic>() to defaultPush
        route<ExampleMessagePublishOnly>() to publishOnly
        route<TestMessageBus>() to secondTopic
        route<TestResponse>() to publishOnly
      }

      pubsubTransport = PubsubTransport(channelProvider, CREDS)
    }, ServiceManagerModule(), object : AbstractModule() {
      override fun configure() {
        bind(AuthService::class.java).toInstance(mock {
          onBlocking { isTokenValid(anyOrNull()) } doReturn true
        })
        bind(Json::class.java).toInstance(Json)
        bind(DelayedMessagePublisher::class.java).toInstance(mock())
        bind(BuildVariant::class.java).toInstance(BuildVariant.LOCAL)
        bind(Application::class.java).toInstance(app)
        bind(AcceptExampleMessageHook::class.java).toInstance(acceptExampleMessageHook)
        bind(GenericMessagePublisher::class.java).toInstance(
          spy(
            GenericMessagePublisher(
              buildVariant = BuildVariant.LOCAL, jsonConverter = Json, publisherCreator = PublisherCreator(
                transportChannelProvider = channelProvider,
                credentialsProvider = CREDS,
              ), coroutineScope = { TEST_IO_SCOPE })
          )
        )
        bind(TimeService::class.java).toInstance(timeService)
      }
    })

    injector.getServiceManager().startAsync().awaitHealthy()
  }

  interface AcceptExampleMessageHook {
    fun accept(exampleMessage: ExampleMessage) {}
    fun accept(exampleMessage: ExampleMessageOtherTopic) {}
    fun accept(exampleMessage: ExampleMessagePushTopic) {}
    fun accept(exampleMessage: ExampleMessagePublishOnly) {}
    fun accept(message: TestMessageBus) {}
  }

  @Test
  fun `SHOULD send and receive message via pubsub to default endpoint`() = withTestApplication(bootstrap()) {
    val message = ExampleMessage("test")
    runTest {
      injector.getInstance(MessageBus::class.java).publish(message)
    }

    await().atMost(5.seconds.toJavaDuration()).untilAsserted {
      verify(acceptExampleMessageHook).accept(message)
    }
  }

  @Test
  fun `SHOULD send and receive protobuf message via pubsub to default endpoint`() = withTestApplication(bootstrap()) {
    val message = TestMessageBus.newBuilder().setRequiredMessage("123").setOptionalMessage(stringValue { value = "321" }).build()
    runTest {
      injector.getInstance(MessageBus::class.java).publish(message)
    }

    val captor = argumentCaptor<TestMessageBus>()
    await().atMost(5.seconds.toJavaDuration()).untilAsserted {
      verify(acceptExampleMessageHook).accept(captor.capture())
    }
    assertThat(captor.firstValue).all {
      transform { it.requiredMessage }.isEqualTo("123")
      transform { it.hasOptionalMessage() }.isEqualTo(true)
      transform { it.optionalMessage.value }.isEqualTo("321")
    }
  }

  @Test
  fun `SHOULD send delayed protobuf message via pubsub`() = withTestApplication(bootstrap()) {
    val message = TestMessageBus.newBuilder().setRequiredMessage("123").setOptionalMessage(stringValue { value = "321" }).build()

    val delay = Duration.ofMinutes(5)
    whenever(timeService.now()).thenReturn(now)

    runTest {
      injector.getInstance(MessageBus::class.java).publish(message, delay)
    }

    await().during(5.seconds.toJavaDuration()).untilAsserted {
      verifyNoInteractions(acceptExampleMessageHook)
    }
    val publisher = injector.getInstance(DelayedMessagePublisher::class.java)
    verifyBlocking(publisher) {
      publish(
        eq("second-topic"),
        eq("second-topic"),
        eq("""{"@class":"protobuf","protobufMessage":{"@type":"type.googleapis.com/com.justplayapps.base.test.TestMessageBus","requiredMessage":"123","optionalMessage":"321"}}"""),
        eq(now + delay)
      )
    }
  }

  @Test
  fun `SHOULD only send message via pubsub`() = withTestApplication(bootstrap()) {
    val message = ExampleMessagePublishOnly("test")
    runTest {
      injector.getInstance(MessageBus::class.java).publish(message)
    }

    await().during(5.seconds.toJavaDuration()).untilAsserted {
      verifyNoInteractions(acceptExampleMessageHook)
    }
    val publisher = injector.getInstance(GenericMessagePublisher::class.java)
    verifyBlocking(publisher) {
      publish(eq(listOf("""{"@class":"com.moregames.base.bus.pubsub.ExampleMessagePublishOnly","value":"test"}""")), eq("publish-only"))
    }
  }

  @Test
  fun `SHOULD send and receive message via pubsub to second endpoint`() = withTestApplication(bootstrap()) {
    val message = ExampleMessageOtherTopic("test")
    runTest {
      injector.getInstance(MessageBus::class.java).publish(message)
    }

    await().atMost(5.seconds.toJavaDuration()).untilAsserted {
      verify(acceptExampleMessageHook).accept(message)
    }
  }

  @Test
  fun `SHOULD send proto message via pubsub to publish endpoint`() = withTestApplication(bootstrap()) {
    val message = testResponse {
      requiredMessage = "123"
    }
    runTest {
      injector.getInstance(MessageBus::class.java).publish(message)
    }
  }

  @Test
  fun `SHOULD send and receive message via pubsub to push endpoint`() {
    val port = (10000..30000).random()
    val server = embeddedServer(Netty, port) {
      bootstrap().invoke(this)
      install(ContentNegotiation) {
        json(Json)
      }
    }

    server.start()
    exposeHostPorts(port) // needs to be called after server is started
    try {
      val message = ExampleMessagePushTopic("test")
      channelProvider.createTopicAndPushSubscription("topic-for-push", "http://host.testcontainers.internal:$port/message-bus/accept")

      runTest {
        injector.getInstance(MessageBus::class.java).publish(message)
      }

      await().atMost(5.seconds.toJavaDuration()).untilAsserted {
        verify(acceptExampleMessageHook).accept(message)
      }
    } finally {
      server.stop(1000, 1000)
    }
  }

  private fun TransportChannelProvider.createTopicAndPullSubscription(topicName: String, subscriptionPostfix: String) {
    val topicAdminSettings: TopicAdminSettings = TopicAdminSettings.newBuilder()
      .setTransportChannelProvider(this)
      .setCredentialsProvider(CREDS)
      .build()
    val subscriptionAdminSettings = SubscriptionAdminSettings.newBuilder()
      .setTransportChannelProvider(this)
      .setCredentialsProvider(CREDS)
      .build()

    val fullTopicName = TopicName.of(GOOGLE_CLOUD_PROJECT_ID, "$topicName-${BuildVariant.LOCAL.key}")
    TopicAdminClient.create(topicAdminSettings).use { topicAdminClient ->
      topicAdminClient.createTopic(fullTopicName)
    }
    SubscriptionAdminClient.create(subscriptionAdminSettings).use { subscriptionAdminClient ->
      subscriptionAdminClient.createSubscription(
        SubscriptionName.of(GOOGLE_CLOUD_PROJECT_ID, "$topicName-$subscriptionPostfix-${BuildVariant.LOCAL.key}"),
        fullTopicName, PushConfig.getDefaultInstance(), 60
      )
    }
  }

  private fun TransportChannelProvider.createTopicAndPushSubscription(topicName: String, pushEndpoint: String) {
    val topicAdminSettings: TopicAdminSettings = TopicAdminSettings.newBuilder()
      .setTransportChannelProvider(this)
      .setCredentialsProvider(CREDS)
      .build()
    val subscriptionAdminSettings = SubscriptionAdminSettings.newBuilder()
      .setTransportChannelProvider(this)
      .setCredentialsProvider(CREDS)
      .build()

    val fullTopicName = TopicName.of(GOOGLE_CLOUD_PROJECT_ID, "$topicName-${BuildVariant.LOCAL.key}")
    TopicAdminClient.create(topicAdminSettings).use { topicAdminClient ->
      topicAdminClient.createTopic(fullTopicName)
    }
    SubscriptionAdminClient.create(subscriptionAdminSettings).use { subscriptionAdminClient ->
      subscriptionAdminClient.createSubscription(
        SubscriptionName.of(GOOGLE_CLOUD_PROJECT_ID, "$topicName-${BuildVariant.LOCAL.key}"),
        fullTopicName,
        PushConfig.newBuilder()
          .setPushEndpoint(pushEndpoint)
          .setPubsubWrapper(PushConfig.PubsubWrapper.newBuilder().build())
          .build(),
        60
      )
    }
  }

  private companion object {
    const val SUBSCRIPTION_NAME = "default-pull"
    const val EXAMPLE_MESSAGE_TOPIC_NAME = "message-topic-name"
    val CREDS = NoCredentialsProvider.create()
  }
}

data class ExampleMessage(val value: String) : Message
data class ExampleMessageOtherTopic(val value: String) : Message
data class ExampleMessagePushTopic(val value: String) : Message
data class ExampleMessagePublishOnly(val value: String) : Message

class ExampleHandler @Inject constructor(
  private val hook: AcceptExampleMessageHook
) {
  @MessageHandler
  fun handleExample(exampleMessage: ExampleMessage) {
    hook.accept(exampleMessage)
  }

  @MessageHandler
  fun handleExample2(exampleMessage: ExampleMessageOtherTopic) {
    hook.accept(exampleMessage)
  }

  @MessageHandler
  fun handleExamplePush(exampleMessage: ExampleMessagePushTopic) {
    hook.accept(exampleMessage)
  }

  @MessageHandler
  fun handleExamplePublishOnly(exampleMessage: ExampleMessagePublishOnly) {
    hook.accept(exampleMessage)
  }

  @MessageHandler
  fun handleExamplePubsub(message: TestMessageBus) {
    hook.accept(message)
  }
}