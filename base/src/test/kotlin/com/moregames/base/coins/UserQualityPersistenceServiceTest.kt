package com.moregames.base.coins

import assertk.assertThat
import assertk.assertions.isEqualByComparingTo
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.moregames.base.coins.UserQualityPersistenceService.InterRevenueData
import com.moregames.base.table.*
import com.moregames.base.util.TimeService
import com.moregames.base.util.prepareUser
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant
import java.time.temporal.ChronoUnit

@ExtendWith(DatabaseExtension::class)
class UserQualityPersistenceServiceTest(
  private val database: Database
) {

  private val timeService: TimeService = mock()

  lateinit var service: UserQualityPersistenceService

  private val now = Instant.now()

  @BeforeEach
  fun before() {
    whenever(timeService.now()).thenReturn(now)

    service = UserQualityPersistenceService(database, timeService)
    transaction(database) {
      SchemaUtils.create(UserTable)
      SchemaUtils.create(UserLastEcpmRevenueTable)
      SchemaUtils.create(UserQualityTable)

      UserLastEcpmRevenueTable.deleteAll()
    }
  }

  @Test
  fun `SHOULD not return interRevenueData ON getInterRevenueData WHEN no data`() {
    val userId = database.prepareUser()

    runBlocking { service.getInterRevenueData(userId) }
      .let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD return interRevenueData ON getInterRevenueData`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsd1] = BigDecimal("12.345")
        it[countryCode] = "US"
      }
    }

    runBlocking { service.getInterRevenueData(userId) }
      .let {
        assertThat(it).isEqualTo(
          InterRevenueData(
            userId = userId,
            revenueUsdFirst5Avg = null,
            revenueUsdFirst10Avg = null,
          )
        )
      }
  }

  @Test
  fun `SHOULD return interRevenueData ON getInterRevenueData WHEN first 5 inters counted`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsdFirst5Avg] = BigDecimal("12.345")
        it[countryCode] = "US"
      }
    }

    runBlocking { service.getInterRevenueData(userId) }
      .let {
        assertThat(it).isEqualTo(
          InterRevenueData(
            userId = userId,
            revenueUsdFirst5Avg = BigDecimal("12.345000000000"),
            revenueUsdFirst10Avg = null,
          )
        )
      }
  }

  @Test
  fun `SHOULD return interRevenueData ON getInterRevenueData WHEN first 10 inters counted`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsdFirst5Avg] = BigDecimal("6.123")
        it[revenueUsdFirst10Avg] = BigDecimal("12.345")
        it[countryCode] = "US"
      }
    }

    runBlocking { service.getInterRevenueData(userId) }
      .let {
        assertThat(it).isEqualTo(
          InterRevenueData(
            userId = userId,
            revenueUsdFirst5Avg = BigDecimal("6.123000000000"),
            revenueUsdFirst10Avg = BigDecimal("12.345000000000"),
          )
        )
      }
  }

  @Test
  fun `SHOULD return null ON getCurrentToFirstEcpmRatio WHEN no entry for user`() {
    val userId = database.prepareUser()

    runBlocking { service.getCurrentToFirstEcpmRatio(userId) }
      .let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD return null ON getCurrentToFirstEcpmRatio WHEN first 10 revenue not computed for user`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsd2] = BigDecimal.ONE
        it[revenueUsd3] = BigDecimal.ONE
        it[revenueUsd4] = BigDecimal.ONE
        it[revenueUsd5] = BigDecimal.ONE
        it[revenueUsd6] = BigDecimal.ONE
        it[revenueUsd7] = BigDecimal.ONE
        it[revenueUsd8] = BigDecimal.ONE
        it[revenueUsd9] = BigDecimal.ONE
        it[revenueUsd10] = BigDecimal.ONE
        it[countryCode] = "US"
      }
    }

    runBlocking { service.getCurrentToFirstEcpmRatio(userId) }
      .let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD get currentToFirst ecpm ratio ON getCurrentToFirstEcpmRatio`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsd1] = BigDecimal("1.1")
        it[revenueUsd2] = BigDecimal("2.2")
        it[revenueUsd3] = BigDecimal("3.3")
        it[revenueUsd4] = BigDecimal("4.4")
        it[revenueUsd5] = BigDecimal("5.5")
        it[revenueUsd6] = BigDecimal("6.6")
        it[revenueUsd7] = BigDecimal("7.7")
        it[revenueUsd8] = BigDecimal("8.8")
        it[revenueUsd9] = BigDecimal("9.9")
        it[revenueUsd10] = BigDecimal("10")
        it[revenueUsdFirst10Avg] = BigDecimal("2.5")
        it[countryCode] = "US"
      }
    }

    runBlocking { service.getCurrentToFirstEcpmRatio(userId) }
      .let { assertThat(it!!).isEqualByComparingTo(BigDecimal("2.38")) }
  }

  @Test
  fun `SHOULD return null ON getLast5ToFirst5EcpmRatio WHEN first 5 revenue not computed for user`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsd7] = BigDecimal.ONE
        it[revenueUsd8] = BigDecimal.ONE
        it[revenueUsd9] = BigDecimal.ONE
        it[revenueUsd10] = BigDecimal.ONE
        it[countryCode] = "US"
      }
    }

    runBlocking { service.getLast5ToFirst5EcpmRatio(userId) }
      .let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD get currentToFirst ecpm ratio ON getLast5ToFirst5EcpmRatio`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsd6] = BigDecimal("6.6")
        it[revenueUsd7] = BigDecimal("7.7")
        it[revenueUsd8] = BigDecimal("8.8")
        it[revenueUsd9] = BigDecimal("9.9")
        it[revenueUsd10] = BigDecimal("10")
        it[revenueUsdFirst5Avg] = BigDecimal("3.44")
        it[countryCode] = "US"
      }
    }

    runBlocking { service.getLast5ToFirst5EcpmRatio(userId) }
      .let { assertThat(it!!).isEqualByComparingTo(BigDecimal("2.5")) }
  }

  @Test
  fun `SHOULD get 0 ON avgFirstInterRevenue WHEN no appropriate data`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()

    transaction(database) {
      // not enough inters
      UserLastEcpmRevenueTable.insert {
        it[userId] = userId1
        it[countryCode] = "US"
      }

      // too long ago
      UserLastEcpmRevenueTable.insert {
        it[userId] = userId2
        it[revenueUsdFirst10Avg] = BigDecimal("1.1")
        it[createdAt] = now.minus(2, ChronoUnit.DAYS)
        it[countryCode] = "US"
      }
    }

    runBlocking { service.avgFirstInterRevenue() }
      .let { assertThat(it).isEqualTo(BigDecimal.ZERO) }
  }

  @Test
  fun `SHOULD calculate country avg ON avgFirstInterRevenue`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val userId3 = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[userId] = userId1
        it[revenueUsdFirst10Avg] = BigDecimal("0.13")
        it[countryCode] = "US"
      }

      UserLastEcpmRevenueTable.insert {
        it[userId] = userId2
        it[revenueUsdFirst10Avg] = BigDecimal("0.1")
        it[countryCode] = "US"
      }

      UserLastEcpmRevenueTable.insert {
        it[userId] = userId3
        it[revenueUsdFirst10Avg] = BigDecimal("0.1")
        it[countryCode] = "US"
      }
    }

    runBlocking { service.avgFirstInterRevenue() }
      .let {
        assertThat(it.setScale(2, RoundingMode.HALF_UP)).isEqualByComparingTo(BigDecimal("0.11"))
      }
  }

  @Test
  fun `SHOULD not update data ON storeUserQuality WHEN when data exists`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserQualityTable.insert {
        it[UserQualityTable.userId] = userId
        it[userQuality] = BigDecimal("0.7")
        it[avgFirstIntersRevenue] = BigDecimal("0.00000000000002")
      }
    }

    runBlocking {
      service.storeUserQuality(
        userId,
        userQuality = BigDecimal("0.8"),
        avgFirstIntersRevenue = BigDecimal("0.00000000000001")
      )
    }

    transaction(database) {
      UserQualityTable.select { UserQualityTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserQualityTable.userQuality]).isEqualByComparingTo(BigDecimal("0.7"))
          assertThat(it[UserQualityTable.avgFirstIntersRevenue]!!)
            .isEqualByComparingTo(BigDecimal("0.00000000000002"))
        }
    }
  }

  @Test
  fun `SHOULD add row ON storeUserQuality`() {
    val userId = database.prepareUser()

    runBlocking {
      service.storeUserQuality(
        userId,
        userQuality = BigDecimal("0.8"),
        avgFirstIntersRevenue = BigDecimal("0.00000000000001")
      )
    }

    transaction(database) {
      UserQualityTable.select { UserQualityTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserQualityTable.userQuality]).isEqualByComparingTo(BigDecimal("0.8"))
          assertThat(it[UserQualityTable.avgFirstIntersRevenue]!!)
            .isEqualByComparingTo(BigDecimal("0.00000000000001"))
        }
    }
  }

  @Test
  fun `SHOULD return null ON getUserQuality WHEN no data`() {
    val userId = database.prepareUser()

    runBlocking { service.getUserQuality(userId) }
      .let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD return user quality ON getUserQuality`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserQualityTable.insert {
        it[UserQualityTable.userId] = userId
        it[userQuality] = BigDecimal("1.2")
        it[avgFirstIntersRevenue] = BigDecimal("0.001")
      }
    }

    runBlocking { service.getUserQuality(userId) }
      .let { assertThat(it!!).isEqualByComparingTo(BigDecimal("1.2")) }
  }

  @Test
  fun `SHOULD update user inter data ON updateEcpmRevenue WHEN 1st inter`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[countryCode] = "US"
      }
    }

    runBlocking { service.updateEcpmRevenue(userId, BigDecimal("1.23")) }

    transaction(database) {
      UserLastEcpmRevenueTable.select { UserLastEcpmRevenueTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd1]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd2]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd3]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd4]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd5]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd6]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd7]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd8]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd9]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd10]!!).isEqualByComparingTo(BigDecimal("1.23"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst5Avg]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst10Avg]).isNull()
        }
    }
  }

  @Test
  fun `SHOULD update user inter data ON updateEcpmRevenue WHEN 2nd inter`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsd10] = BigDecimal("1.1")
        it[countryCode] = "US"
      }
    }

    runBlocking { service.updateEcpmRevenue(userId, BigDecimal("2.2")) }

    transaction(database) {
      UserLastEcpmRevenueTable.select { UserLastEcpmRevenueTable.userId eq userId }
        .first()
        .let {

          assertThat(it[UserLastEcpmRevenueTable.revenueUsd1]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd2]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd3]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd4]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd5]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd6]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd7]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd8]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd9]!!).isEqualByComparingTo(BigDecimal("1.1"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd10]!!).isEqualByComparingTo(BigDecimal("2.2"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst5Avg]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst10Avg]).isNull()
        }
    }
  }

  @Test
  fun `SHOULD update user inter data ON updateEcpmRevenue WHEN 3rd inter`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsd9] = BigDecimal("1.1")
        it[revenueUsd10] = BigDecimal("2.2")
        it[countryCode] = "US"
      }
    }

    runBlocking { service.updateEcpmRevenue(userId, BigDecimal("3.3")) }

    transaction(database) {
      UserLastEcpmRevenueTable.select { UserLastEcpmRevenueTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd1]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd2]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd3]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd4]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd5]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd6]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd7]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd8]!!).isEqualByComparingTo(BigDecimal("1.1"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd9]!!).isEqualByComparingTo(BigDecimal("2.2"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd10]!!).isEqualByComparingTo(BigDecimal("3.3"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst5Avg]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst10Avg]).isNull()
        }
    }
  }

  @Test
  fun `SHOULD update user inter data ON updateEcpmRevenue WHEN 5th inter`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsd7] = BigDecimal("1.1")
        it[revenueUsd8] = BigDecimal("2.2")
        it[revenueUsd9] = BigDecimal("3.3")
        it[revenueUsd10] = BigDecimal("4.4")
        it[countryCode] = "US"
      }
    }

    runBlocking { service.updateEcpmRevenue(userId, BigDecimal("5.5")) }

    transaction(database) {
      UserLastEcpmRevenueTable.select { UserLastEcpmRevenueTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd1]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd2]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd3]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd4]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd5]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd6]!!).isEqualByComparingTo(BigDecimal("1.1"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd7]!!).isEqualByComparingTo(BigDecimal("2.2"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd8]!!).isEqualByComparingTo(BigDecimal("3.3"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd9]!!).isEqualByComparingTo(BigDecimal("4.4"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd10]!!).isEqualByComparingTo(BigDecimal("5.5"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst5Avg]!!).isEqualByComparingTo(BigDecimal("3.3"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst10Avg]).isNull()
        }
    }
  }

  @Test
  fun `SHOULD update user inter data ON updateEcpmRevenue WHEN 6th inter`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsd6] = BigDecimal("1.1")
        it[revenueUsd7] = BigDecimal("2.2")
        it[revenueUsd8] = BigDecimal("3.3")
        it[revenueUsd9] = BigDecimal("4.4")
        it[revenueUsd10] = BigDecimal("5.5")
        it[revenueUsdFirst5Avg] = BigDecimal("3.3")
        it[countryCode] = "US"
      }
    }

    runBlocking { service.updateEcpmRevenue(userId, BigDecimal("6.6")) }

    transaction(database) {
      UserLastEcpmRevenueTable.select { UserLastEcpmRevenueTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd1]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd2]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd3]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd4]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd5]!!).isEqualByComparingTo(BigDecimal("1.1"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd6]!!).isEqualByComparingTo(BigDecimal("2.2"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd7]!!).isEqualByComparingTo(BigDecimal("3.3"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd8]!!).isEqualByComparingTo(BigDecimal("4.4"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd9]!!).isEqualByComparingTo(BigDecimal("5.5"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd10]!!).isEqualByComparingTo(BigDecimal("6.6"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst5Avg]!!).isEqualByComparingTo(BigDecimal("3.3"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst10Avg]).isNull()
        }
    }
  }

  @Test
  fun `SHOULD update user inter data ON updateEcpmRevenue WHEN 6th inter AND revenueUsdFirst5Avg not filled`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsd6] = BigDecimal("1.1")
        it[revenueUsd7] = BigDecimal("2.2")
        it[revenueUsd8] = BigDecimal("3.3")
        it[revenueUsd9] = BigDecimal("4.4")
        it[revenueUsd10] = BigDecimal("5.5")
        it[countryCode] = "US"
      }
    }

    runBlocking { service.updateEcpmRevenue(userId, BigDecimal("6.6")) }

    transaction(database) {
      UserLastEcpmRevenueTable.select { UserLastEcpmRevenueTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd1]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd2]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd3]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd4]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd5]!!).isEqualByComparingTo(BigDecimal("1.1"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd6]!!).isEqualByComparingTo(BigDecimal("2.2"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd7]!!).isEqualByComparingTo(BigDecimal("3.3"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd8]!!).isEqualByComparingTo(BigDecimal("4.4"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd9]!!).isEqualByComparingTo(BigDecimal("5.5"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd10]!!).isEqualByComparingTo(BigDecimal("6.6"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst5Avg]).isNull()
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst10Avg]).isNull()
        }
    }
  }

  @Test
  fun `SHOULD update user inter data ON updateEcpmRevenue WHEN 10th inter`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsd2] = BigDecimal("1.1")
        it[revenueUsd3] = BigDecimal("2.2")
        it[revenueUsd4] = BigDecimal("3.3")
        it[revenueUsd5] = BigDecimal("4.4")
        it[revenueUsd6] = BigDecimal("5.5")
        it[revenueUsd7] = BigDecimal("6.6")
        it[revenueUsd8] = BigDecimal("7.7")
        it[revenueUsd9] = BigDecimal("8.8")
        it[revenueUsd10] = BigDecimal("9.9")
        it[revenueUsdFirst5Avg] = BigDecimal("3.3")
        it[countryCode] = "US"
      }
    }

    runBlocking { service.updateEcpmRevenue(userId, BigDecimal("10.1")) }

    transaction(database) {
      UserLastEcpmRevenueTable.select { UserLastEcpmRevenueTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd1]!!).isEqualByComparingTo(BigDecimal("1.1"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd2]!!).isEqualByComparingTo(BigDecimal("2.2"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd3]!!).isEqualByComparingTo(BigDecimal("3.3"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd4]!!).isEqualByComparingTo(BigDecimal("4.4"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd5]!!).isEqualByComparingTo(BigDecimal("5.5"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd6]!!).isEqualByComparingTo(BigDecimal("6.6"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd7]!!).isEqualByComparingTo(BigDecimal("7.7"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd8]!!).isEqualByComparingTo(BigDecimal("8.8"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd9]!!).isEqualByComparingTo(BigDecimal("9.9"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd10]!!).isEqualByComparingTo(BigDecimal("10.1"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst5Avg]!!).isEqualByComparingTo(BigDecimal("3.3"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst10Avg]!!).isEqualByComparingTo(BigDecimal("5.96"))
        }
    }
  }

  @Test
  fun `SHOULD update user inter data ON updateEcpmRevenue WHEN 11th inter`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserLastEcpmRevenueTable.insert {
        it[UserLastEcpmRevenueTable.userId] = userId
        it[revenueUsd1] = BigDecimal("1.1")
        it[revenueUsd2] = BigDecimal("2.2")
        it[revenueUsd3] = BigDecimal("3.3")
        it[revenueUsd4] = BigDecimal("4.4")
        it[revenueUsd5] = BigDecimal("5.5")
        it[revenueUsd6] = BigDecimal("6.6")
        it[revenueUsd7] = BigDecimal("7.7")
        it[revenueUsd8] = BigDecimal("8.8")
        it[revenueUsd9] = BigDecimal("9.9")
        it[revenueUsd10] = BigDecimal("10.1")
        it[revenueUsdFirst5Avg] = BigDecimal("3.3")
        it[revenueUsdFirst10Avg] = BigDecimal("5.96")
        it[countryCode] = "US"
      }
    }

    runBlocking { service.updateEcpmRevenue(userId, BigDecimal("11.11")) }

    transaction(database) {
      UserLastEcpmRevenueTable.select { UserLastEcpmRevenueTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd1]!!).isEqualByComparingTo(BigDecimal("2.2"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd2]!!).isEqualByComparingTo(BigDecimal("3.3"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd3]!!).isEqualByComparingTo(BigDecimal("4.4"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd4]!!).isEqualByComparingTo(BigDecimal("5.5"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd5]!!).isEqualByComparingTo(BigDecimal("6.6"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd6]!!).isEqualByComparingTo(BigDecimal("7.7"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd7]!!).isEqualByComparingTo(BigDecimal("8.8"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd8]!!).isEqualByComparingTo(BigDecimal("9.9"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd9]!!).isEqualByComparingTo(BigDecimal("10.1"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsd10]!!).isEqualByComparingTo(BigDecimal("11.11"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst5Avg]!!).isEqualByComparingTo(BigDecimal("3.3"))
          assertThat(it[UserLastEcpmRevenueTable.revenueUsdFirst10Avg]!!).isEqualByComparingTo(BigDecimal("5.96"))
        }
    }
  }
}
