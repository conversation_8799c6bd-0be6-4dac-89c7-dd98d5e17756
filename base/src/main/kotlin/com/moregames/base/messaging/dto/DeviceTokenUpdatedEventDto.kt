package com.moregames.base.messaging.dto

import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.ANDROID
import kotlinx.serialization.Serializable

@Serializable
data class DeviceTokenUpdatedEventDto(
  val userId: String,
  val deviceToken: String,
  val appPlatform: AppPlatform? = ANDROID
) : MessageDto {
  override fun defaultPubsubTopicName(): String = TOPIC_NAME

  companion object {
    const val TOPIC_NAME = "device-token-updated"
  }
}