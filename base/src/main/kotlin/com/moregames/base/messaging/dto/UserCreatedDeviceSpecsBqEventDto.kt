package com.moregames.base.messaging.dto

import com.moregames.base.bigquery.BqEvent
import com.moregames.base.util.BigDecimalAsString
import com.moregames.base.util.InstantAsString
import kotlinx.serialization.Serializable

@Serializable
data class UserCreatedDeviceSpecsBqEventDto(
  val userId: String,
  val createdAt: InstantAsString,
  val osVersion: String? = null,
  val modelName: String? = null,
  val ramSize: Int? = null,
  val fontScale: BigDecimalAsString? = null,
  val density: Int? = null,
  val densityScaleFactor: BigDecimalAsString? = null,
  val appPlatform: String
) : BqEvent {
  override val topicName: String = "user-created-device-specs"
}