package com.moregames.base.messaging.dto

import com.moregames.base.bigquery.BqEvent
import com.moregames.base.util.BigDecimalAsString
import com.moregames.base.util.InstantAsString
import kotlinx.serialization.Serializable

@Serializable
data class PaymentRejectedEventDto(
  val cashoutTransactionId: String,
  //null as default value for backwards compatibility
  val fee: BigDecimalAsString? = null,
  val feeUsd: BigDecimalAsString? = null,
  val operationalWithholdAmount: BigDecimalAsString? = null,
  val createdAt: InstantAsString,
  val account: String? = null
) : MessageDto, BqEvent {
  override val topicName: String = TOPIC_NAME
  override fun defaultPubsubTopicName(): String = TOPIC_NAME

  companion object {
    const val TOPIC_NAME = "payment-rejected"
  }
}