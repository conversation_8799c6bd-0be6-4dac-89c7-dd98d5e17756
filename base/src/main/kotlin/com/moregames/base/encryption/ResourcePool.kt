package com.moregames.base.encryption

import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.sync.withPermit

class ResourcePool<T>(vararg initialResources: T) {

  private val mutex = Mutex()
  private val semaphore = Semaphore(permits = initialResources.size)
  private val resources = initialResources.toMutableList()

  suspend operator fun <R> invoke(handler: suspend (T) -> R) =
    semaphore.withPermit {
      val borrowed = mutex.withLock { resources.removeLast() }
      try {
        handler(borrowed)
      } finally {
        mutex.withLock { resources.add(borrowed) }
      }
    }
}