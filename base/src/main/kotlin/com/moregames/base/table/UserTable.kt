package com.moregames.base.table

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.`java-time`.date
import org.jetbrains.exposed.sql.`java-time`.timestamp

/**
 * Main table to track user data
 *
 * - id - User identifier
 * - google_ad_id - Google Advertising ID. Value assignment is triggered via call from JustPlayApp
 * - device_token - device token is used to push notification from backend to user mobile device via Firebase
 * - consent_to_analytics_at - date of consent for analytics agreement. Value assignment is triggered via call from JustPlayApp
 * - consent_to_targeted_advertisement_at - date of consent for targeted advertisement agreement. Value assignment is triggered via call from JustPlayApp
 * - country_code - code of country. Created within user object creation from "X-AppEngine-Country" header provided by Google Cloud
 * - is_deleted - if flagged, prevent any API interactions for this particular client
 * - is_banned - if flagged, prevent users from earn of cashout real money
 * - app_version - version of the mobile app
 * - app_platform - either IOS or ANDROID
 * - last_active_at_day - last day when user was active (there were API interactions)
 * - current_tracking_id - main id for tracking from 3rd party systems (additionally to user_id)
 * - current_tracking_type - IDFA (aka Google Ad Id for Android) or IDFV (for iOS). See [TrackingDataType][com.moregames.base.dto.TrackingDataType]
 */
object UserTable : Table("playtime.users") {
  val id = varchar("id", 36)
  val googleAdId = varchar("google_ad_id", 100).nullable()
  val deviceToken = varchar("device_token", 200).nullable() // TODO: extract device token from user object (move to notifications service domain)
  val consentToAnalyticsAt = timestamp("consent_to_analytics_at").nullable()
  val consentToTargetedAdvertisementAt = timestamp("consent_to_targeted_advertisement_at").nullable()
  val countryCode = varchar("country_code", 2)
  val isDeleted = bool("is_deleted")
  val isBanned = bool("is_banned")
  val appVersion = integer("app_version").nullable()
  val appPlatform = varchar("app_platform", 36).nullable()
  val createdAt = timestamp("created_at")
  val createdAtDay = date("created_at_day")
  val lastActiveAtDay = date("last_active_at_day").nullable()
  val currentTrackingId = varchar("current_tracking_id", 36).nullable()
  val currentTrackingType = varchar("current_tracking_type", 36).nullable()
}
