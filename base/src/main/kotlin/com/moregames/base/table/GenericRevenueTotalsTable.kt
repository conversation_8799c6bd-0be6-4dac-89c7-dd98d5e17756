package com.moregames.base.table

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.`java-time`.timestamp
import java.math.BigDecimal
import java.time.Instant

object GenericRevenueTotalsTable : Table("playtime.generic_revenue_totals") {
  val userId = varchar("user_id", 36)
  val revenueAmount = decimal("revenue", 16, 12)
  val day2Revenue = decimal("day_2_revenue", 16, 12)
  val day0Revenue = decimal("day_0_revenue", 16, 12).clientDefault { BigDecimal.ZERO }
  val offerwallRevenue = decimal("offerwall_revenue", 16, 12)
  val createdAt = timestamp("created_at").clientDefault { Instant.now() }
  val updatedAt = timestamp("updated_at").clientDefault { Instant.now() }

  override val primaryKey = PrimaryKey(userId, name = "fk_user_generic_revenue_totals_user_id")
}