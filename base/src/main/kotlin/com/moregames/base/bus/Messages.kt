package com.moregames.base.bus

import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.databind.annotation.JsonTypeIdResolver
import com.moregames.base.bus.serialization.MessageTypeIdResolver

@JsonTypeInfo(use = JsonTypeInfo.Id.CUSTOM, include = JsonTypeInfo.As.PROPERTY, property = "@class")
@JsonTypeIdResolver(MessageTypeIdResolver::class)
interface Message

interface Effect

interface AsyncEffect