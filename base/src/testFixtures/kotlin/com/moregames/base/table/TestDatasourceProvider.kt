package com.moregames.base.table

import com.moregames.base.app.BuildVariant
import com.moregames.base.app.DataSourceProvider
import com.moregames.base.secret.Secret
import com.moregames.base.secret.SecretService
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import kotlinx.coroutines.runBlocking
import java.util.*

class TestDataSourceProvider(private val dbPropertiesFileName: String = "/database-local.properties") : DataSourceProvider {
  override fun getDataSource(
    username: String,
    password: Secret,
    maximumPoolSize: Int,
    secretService: SecretService,
    defaultSchema: String?,
    alias: String?,
    buildVariant: BuildVariant
  ): HikariDataSource = runBlocking {
    HikariDataSource(
      HikariConfig(getDatabaseProperties())
        .apply {
          this.username = DatabaseTestBase.dbUser
          this.password = DatabaseTestBase.DB_PASSWORD
          this.minimumIdle = 1
          this.maximumPoolSize = maximumPoolSize
          this.schema = DatabaseTestBase.PLAYTIME_SCHEMA
        })
  }

  private fun getDatabaseProperties(): Properties {
    val properties = Properties()
    val inputStream = this.javaClass.getResourceAsStream(dbPropertiesFileName)
    properties.load(inputStream)
    properties.setProperty("jdbcUrl", DatabaseTestBase.dbUrl)
    return properties
  }
}