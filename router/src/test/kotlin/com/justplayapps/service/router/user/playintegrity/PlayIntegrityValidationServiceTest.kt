package com.justplayapps.service.router.user.playintegrity

import assertk.all
import assertk.assertThat
import assertk.assertions.*
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.read.ListAppender
import com.google.api.client.http.LowLevelHttpRequest
import com.google.api.client.http.LowLevelHttpResponse
import com.google.api.client.json.Json
import com.google.api.client.testing.http.MockHttpTransport
import com.google.api.client.testing.http.MockLowLevelHttpRequest
import com.google.api.client.testing.http.MockLowLevelHttpResponse
import com.justplayapps.service.router.user.playintegrity.PlayIntegrityValidationService.ValidationResult
import com.justplayapps.service.router.util.PlayIntegrityClientProvider
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.secret.SecretService
import com.moregames.base.util.Constants.JUSTPLAY_APPLICATION_ID
import com.moregames.base.util.TimeService
import com.moregames.base.util.io
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.slf4j.LoggerFactory
import java.time.Instant

@OptIn(ExperimentalCoroutinesApi::class)
class PlayIntegrityValidationServiceTest {
  private val now = Instant.now()
  private val secretService = mock<SecretService> {
    onBlocking { secretValue(any()) } doReturn "secret"
  }
  private val timeService = mock<TimeService> {
    on { now() } doReturn now
  }
  private val testScope = TestScope()
  private val playIntegrityClientProvider = PlayIntegrityClientProvider(secretService, mockHttpTransport(JUSTPLAY_APPLICATION_ID), { mock() })
  private val bigQueryEventPublisher: BigQueryEventPublisher = mock()

  private val underTest = PlayIntegrityValidationService(playIntegrityClientProvider, { testScope.io() }, bigQueryEventPublisher, timeService)

  private val localLogger = LoggerFactory.getLogger(PlayIntegrityValidationService::class.java) as Logger
  private val logEntriesListAppender = ListAppender<ILoggingEvent>()

  @BeforeEach
  fun setUp() {
    logEntriesListAppender.start()
    localLogger.addAppender(logEntriesListAppender)
  }

  @AfterEach
  fun after() {
    localLogger.detachAppender(logEntriesListAppender)
    logEntriesListAppender.stop()
  }

  @Test
  fun `SHOULD pass validation successfully IF request hash is correct`() = testScope.runTest {
    underTest.validatePlayIntegrityToken(token, payload)

    assertThat(logEntriesListAppender.list).isEmpty()

    advanceUntilIdle()

    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PlayIntegrityCreateUserInfoEvent(
          appRecognitionVerdict = "PLAY_RECOGNIZED",
          appCertificateDigest = "6a6a1474b5cbbb2b1aa57e0bc3",
          packageName = "com.package.name",
          versionCode = "42",
          appLicensingVerdict = "LICENSED",
          deviceRecognitionVerdict = "MEETS_DEVICE_INTEGRITY",
          createdAt = now,
        )
      )
    }
  }

  @Test
  fun `SHOULD NOT pass validation successfully IF request hash is wrong`() = testScope.runTest {
    val result = underTest.validatePlayIntegrityToken(token, payload + "123")
    assertThat(result).isEqualTo(ValidationResult.INVALID_HASH)

    assertThat(logEntriesListAppender.list).all {
      hasSize(2)
      index(0).transform { it.message }.startsWith("PlayIntegrityCheck")
    }

    advanceUntilIdle()

    verifyBlocking(bigQueryEventPublisher) {
      publish(
        PlayIntegrityCreateUserInfoEvent(
          appRecognitionVerdict = "PLAY_RECOGNIZED",
          appCertificateDigest = "6a6a1474b5cbbb2b1aa57e0bc3",
          packageName = "com.package.name",
          versionCode = "42",
          appLicensingVerdict = "LICENSED",
          deviceRecognitionVerdict = "MEETS_DEVICE_INTEGRITY",
          createdAt = now,
        )
      )
    }
  }

  private fun mockHttpTransport(applicationId: String) = object : MockHttpTransport() {
    override fun buildRequest(method: String, url: String): LowLevelHttpRequest {
      assertThat(url).isEqualTo("https://playintegrity.googleapis.com/v1/$applicationId:decodeIntegrityToken")
      return object : MockLowLevelHttpRequest() {
        override fun execute(): LowLevelHttpResponse {
          val response = MockLowLevelHttpResponse()
          response.statusCode = 200
          response.contentType = Json.MEDIA_TYPE
          //language=JSON
          response.content = """
            {"tokenPayloadExternal":
              {
                "requestDetails":{
                  "requestPackageName":"com.package.name",
                  "requestHash":"239f59ed55e737c77147cf55ad0c1b030b6d7ee748a7426952f9b852d5a935e5",
                  "timestampMillis":"**********"
                },
                "appIntegrity":{
                  "appRecognitionVerdict":"PLAY_RECOGNIZED",
                  "packageName":"com.package.name",
                  "certificateSha256Digest":["6a6a1474b5cbbb2b1aa57e0bc3"],
                  "versionCode":"42"
                },
                "deviceIntegrity":{
                  "deviceRecognitionVerdict":["MEETS_DEVICE_INTEGRITY"]
                },
                "accountDetails":{
                  "appLicensingVerdict":"LICENSED"
                }
              }
            }
            """.trimIndent().byteInputStream()
          return response
        }
      }
    }
  }

  companion object {
    private val token = "token"
    private val payload = "payload"
  }
}
