package com.moregames.games.cron

import com.google.inject.Inject
import com.moregames.base.util.cronLogger
import com.moregames.games.sessions.GameSessionsService
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*
import javax.inject.Singleton

@Singleton
class CronController @Inject constructor(
  private val gameSessionsService: GameSessionsService,
) {

  fun startRouting(cronRoute: Route) {
    cronRoute.get("/checkStaledGameSessions") {
      cronLogger().info("Starting checkStaledGameSessions")
      gameSessionsService.checkStaledGameSessions()
      cronLogger().info("Finished checkStaledGameSessions")
      call.respond(HttpStatusCode.OK)
    }
  }

}
