package com.moregames.games.progress

import com.moregames.base.dto.AppPlatform
import java.math.BigDecimal
import java.time.Instant

data class UserBalanceUpdateResultDto(
  val userId: String,
  val gameId: Int,
  val lastPlayedAt: Instant?,
  val initialCoinsBalance: BigDecimal,
  val em2CoinsEarned: BigDecimal? = null,
  val coinsEarned: Int = 0,
  val isFirstCoinsForGame: Boolean,
  val isFirstCoinsEver: Boolean,
  val applicationId: String,
  val appPlatform: AppPlatform
)
