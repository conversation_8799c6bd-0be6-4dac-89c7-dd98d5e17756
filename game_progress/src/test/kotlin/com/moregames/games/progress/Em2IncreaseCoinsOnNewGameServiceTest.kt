package com.moregames.games.progress

import assertk.assertThat
import assertk.assertions.isEqualByComparingTo
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.AbTestingService.IsExperimentParticipant
import com.moregames.base.abtesting.Variations.EM2_BOILED_FROG_SOFT
import com.moregames.base.abtesting.Variations.EM2_INCREASE_COINS_ON_NEW_GAME
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.base.util.redis.SafeJedisClient
import com.moregames.games.progress.UserGameBalancePersistenceService.UserGameCoinsDataEm2
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.argThat
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit

class Em2IncreaseCoinsOnNewGameServiceTest {

  private val abTestingService: AbTestingService = mock()
  private val timeService: TimeService = mock()
  private val safeJedisClient: SafeJedisClient = mock()

  private val service = Em2IncreaseCoinsOnNewGameService(
    abTestingService = abTestingService,
    timeService = timeService,
    safeJedisClient = safeJedisClient,
  )

  companion object {
    val now: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    const val USER_ID = "userId"
    const val GAME_ID = 20020
    val gameCoinsData = UserGameCoinsDataEm2(
      coins = BigDecimal.TEN,
      calculatedCoins = 10,
      fiveMinIntervalCoins = 5,
      fiveMinIntervalCoinsTransactionsCount = 1
    )
  }

  @BeforeEach
  fun init() {
    timeService.mock({ now() }, now)
    abTestingService.mock({ getEm2Participation(USER_ID) }, IsExperimentParticipant.Yes(EM2_INCREASE_COINS_ON_NEW_GAME))
    safeJedisClient.mock({ exists("Em2IncreaseCoinsOnNewGame:userId###20020") }, false)
  }

  @Test
  fun `SHOULD return 1 ON getCoeff WHEN user is not em2 participant`() {
    abTestingService.mock({ getEm2Participation(USER_ID) }, IsExperimentParticipant.No)

    runBlocking {
      service.getCoeff(USER_ID, GAME_ID, gameCoinsData)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal.ONE) }
  }

  @Test
  fun `SHOULD return 1 ON getCoeff WHEN user is em2 but not the variation participant`() {
    abTestingService.mock({ getEm2Participation(USER_ID) }, IsExperimentParticipant.Yes(EM2_BOILED_FROG_SOFT))

    runBlocking {
      service.getCoeff(USER_ID, GAME_ID, gameCoinsData)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal.ONE) }
  }

  @Test
  fun `SHOULD return 1 ON getCoeff WHEN user played this game a lot`() {
    val gameCoinsData2 = gameCoinsData.copy(firstPlayedAt = now.minus(31, ChronoUnit.MINUTES))

    runBlocking {
      service.getCoeff(USER_ID, GAME_ID, gameCoinsData2)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal.ONE) }
  }

  @Test
  fun `SHOULD return 1p3 ON getCoeff WHEN user never played this game`() {
    val gameCoinsData2 = gameCoinsData.copy(firstPlayedAt = null)

    runBlocking {
      service.getCoeff(USER_ID, GAME_ID, gameCoinsData2)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal("1.3")) }
  }

  @Test
  fun `SHOULD return 1p3 ON getCoeff WHEN user started playing this game less than 30 minutes ago`() {
    val gameCoinsData2 = gameCoinsData.copy(firstPlayedAt = now.minus(29, ChronoUnit.MINUTES))

    runBlocking {
      service.getCoeff(USER_ID, GAME_ID, gameCoinsData2)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal("1.3")) }
  }

  @Test
  fun `SHOULD return 1 ON getCoeff WHEN user played only this game`() {
    val gameCoinsData2 = gameCoinsData.copy(
      firstPlayedAt = now.minus(31, ChronoUnit.MINUTES),
      lastPlayedAt = now.minus(31, ChronoUnit.MINUTES),
      otherGameLastPlayedAt = null
    )

    runBlocking {
      service.getCoeff(USER_ID, GAME_ID, gameCoinsData2)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal.ONE) }
  }

  @Test
  fun `SHOULD return 1p3 ON getCoeff WHEN user play this round after he recently played other game and played this game before `() {
    val gameCoinsData2 = gameCoinsData.copy(
      firstPlayedAt = now.minus(31, ChronoUnit.MINUTES),
      lastPlayedAt = now.minus(31, ChronoUnit.MINUTES),
      otherGameLastPlayedAt = now.minus(30, ChronoUnit.MINUTES),
    )

    runBlocking {
      service.getCoeff(USER_ID, GAME_ID, gameCoinsData2)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal("1.3")) }

    verifyBlocking(safeJedisClient) {
      set(eq("Em2IncreaseCoinsOnNewGame:userId###20020"), eq("stub"), argThat { this.toString() == "[ex, 1800]" })
    }
  }

  @Test
  fun `SHOULD return 1p3 ON getCoeff WHEN user play next round`() {
    val gameCoinsData2 = gameCoinsData.copy(
      firstPlayedAt = now.minus(31, ChronoUnit.MINUTES),
      lastPlayedAt = now.minus(1, ChronoUnit.MINUTES),
      otherGameLastPlayedAt = now.minus(30, ChronoUnit.MINUTES),
    )
    safeJedisClient.mock({ exists("Em2IncreaseCoinsOnNewGame:userId###20020") }, true)

    runBlocking {
      service.getCoeff(USER_ID, GAME_ID, gameCoinsData2)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal("1.3")) }

    verifyBlocking(safeJedisClient) { exists("Em2IncreaseCoinsOnNewGame:userId###20020") }
  }

  @Test
  fun `SHOULD return 1 ON getCoeff WHEN user play more than 30 minutes after the bonus for changing game started`() {
    val gameCoinsData2 = gameCoinsData.copy(
      firstPlayedAt = now.minus(61, ChronoUnit.MINUTES),
      lastPlayedAt = now.minus(1, ChronoUnit.MINUTES),
      otherGameLastPlayedAt = now.minus(60, ChronoUnit.MINUTES),
    )

    runBlocking {
      service.getCoeff(USER_ID, GAME_ID, gameCoinsData2)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal.ONE) }

    verifyBlocking(safeJedisClient) { exists("Em2IncreaseCoinsOnNewGame:userId###20020") }
  }
}