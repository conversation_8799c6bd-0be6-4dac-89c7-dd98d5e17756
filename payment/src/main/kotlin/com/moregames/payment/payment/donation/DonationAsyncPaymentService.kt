package com.moregames.payment.payment.donation

import com.google.inject.Inject
import com.justplayapps.proxybase.PaymentAccounts
import com.moregames.base.dto.Payment
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.mail.MailService
import com.moregames.base.mail.MailTemplates
import com.moregames.base.util.format
import com.moregames.base.util.logger
import com.moregames.payment.payment.AsyncPaymentProviderService
import com.moregames.payment.payment.AsyncPaymentProviderService.PaymentStatusCheckResult
import java.math.BigDecimal
import java.util.*

class DonationAsyncPaymentService @Inject constructor(
  private val mailService: MailService,
  private val encryptionService: EncryptionService,
) : AsyncPaymentProviderService {

  override fun providerName(): String = "Donation"

  override suspend fun orderPayment(payment: Payment): PaymentAccounts? {
    val name = encryptionService.decryptOrEmpty(payment.encryptedRecipientName)
    val email = encryptionService.decryptOrEmpty(payment.encryptedRecipientEmail)
    val paymentProvider = DonationPaymentProviders.byType(payment.provider)
    if (email.isNotEmpty()) {
      sendEmail(
        email,
        name,
        Currency.getInstance(payment.currencyCode),
        payment.amount,
        paymentProvider
      )
    } else {
      logger().warn("Donation payment without recipient email!. Payment cashout transaction id: ${payment.cashoutTransactionId}")
    }
    return payment.account
  }

  override suspend fun checkPaymentStatus(payment: Payment): PaymentStatusCheckResult =
    PaymentStatusCheckResult(Payment.PaymentStatus.COMPLETED)

  private suspend fun sendEmail(
    email: String,
    name: String,
    currency: Currency,
    amount: BigDecimal,
    paymentProvider: DonationPaymentProviders
  ) {
    val substitutions = mapOf(
      "full_name" to name,
      "amount" to amount.format(currency),
      "cause_name" to paymentProvider.displayName,
      "cause_description" to (paymentProvider.description ?: ""),
      "cause_link" to paymentProvider.url
    )
    mailService.sendTemplateMail(
      "<EMAIL>",
      "JustPlay Donation",
      email,
      name,
      MailTemplates.DONATION_CONFIRMATION,
      substitutions,
      // 1. no app link in this template. 2. can be added when there will be "appPlatform" in Payment
      appPlatform = null,
    )
  }
}