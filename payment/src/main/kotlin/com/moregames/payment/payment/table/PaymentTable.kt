package com.moregames.payment.payment.table

import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IdTable
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.`java-time`.timestamp
import java.time.Instant

object PaymentTable : IdTable<String>("payment.payments") {
  val cashoutTransactionId = varchar("cashout_transaction_id", 36)
  val userId = varchar("user_id", 36)
  val countryCode = varchar("country_code", 2)
  val currencyCode = varchar("currency_code", 3)
  val amount = decimal("amount", 10, 2) // what exactly was paid to user (switched to precision 10 for asia)
  val operationalWithholdAmount = decimal("operational_withhold_amount", 6, 2) // diff between cashout amount and payment amount
  val fee = decimal("fee", 6, 2).nullable() // fee that was paid by us for payment processing
  val provider = varchar("provider", 45)
  val encryptedRecipientName = text("encrypted_recipient_name").nullable()
  val encryptedRecipientEmail = text("encrypted_recipient_email").nullable()
  val status = varchar("status", 30)
  val encryptedAddress = text("encrypted_address").nullable()
  val createdAt = timestamp("created_at").clientDefault { Instant.now() }
  val account = varchar("account", 30).nullable()
  val recipientHandle = varchar("recipient_handle", 30).nullable()
  val platform = varchar("platform", 30).nullable()

  override val id: Column<EntityID<String>> = cashoutTransactionId.entityId()

  override val primaryKey: PrimaryKey = PrimaryKey(cashoutTransactionId)
}