{"message": "Success.", "success": true, "proxy": true, "ISP": "Mediacom Cable", "organization": "Mediacom Cable", "ASN": 30036, "host": "171-33-111-143.client.mchsi.com", "country_code": "US", "city": "Houston", "region": "Texas", "is_crawler": true, "connection_type": "Residential", "latitude": 29.7079, "longitude": -95.401, "zip_code": "77001", "timezone": "America Matamoros", "vpn": true, "tor": true, "active_vpn": true, "active_tor": true, "recent_abuse": true, "abuse_velocity": "medium", "bot_status": true, "mobile": true, "fraud_score": 25, "operating_system": "Mac 10.6", "browser": "Chrome 62.0", "device_model": "iPad", "device_brand": "Apple", "transaction_details": {"valid_billing_address": true, "valid_shipping_address": true, "valid_billing_email": true, "valid_shipping_email": true, "risky_billing_phone": true, "risky_shipping_phone": true, "billing_phone_carrier": "AT&T", "shipping_phone_carrier": "Rogers Canada", "billing_phone_line_type": "Wireless", "shipping_phone_line_type": "Landline", "billing_phone_country_code": "1", "shipping_phone_country_code": "1", "fraudulent_behavior": true, "bin_country": "US", "risk_score": 17, "risk_factors": ["Billing phone number is inactive.", "Billing email is associated with recent chargebacks."], "is_prepaid_card": true, "risky_username": true, "valid_billing_phone": true, "valid_shipping_phone": true, "leaked_billing_email": true, "leaked_shipping_email": true, "leaked_user_data": true, "phone_name_identity_match": "Match", "phone_email_identity_match": "Mismatch", "phone_address_identity_match": "No Match", "email_name_identity_match": "Unknown", "name_address_identity_match": "Match", "address_email_identity_match": "Match"}, "request_id": "0w8WYS"}