package com.moregames.playtime._liquibase

import assertk.assertThat
import assertk.assertions.*
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.table.GamesTable
import com.moregames.playtime.translations.AppTranslationTable
import com.moregames.playtime.translations.StringResourceTranslationTable
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Order
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import kotlin.test.assertEquals

@Order(-1)
@ExtendWith(DatabaseExtension::class)
class TranslationsTest(private val database: Database) {

  @Test
  fun `SHOULD have all translations in place`() {
    transaction(database) {
      StringResourceTranslationTable
        .selectAll()
        .groupBy { it[StringResourceTranslationTable.language] }
        .map { it.value.size }
        .zipWithNext()
        .all { it.first == it.second }
        .let { assertThat(it).isTrue() }
    }
  }

  @Test
  fun `SHOULD have all translations in place detailed`() {
    transaction(database) {
      StringResourceTranslationTable
        .selectAll()
        .groupBy { it[StringResourceTranslationTable.language] }
        .mapValues { (_, translationsRowsList) -> translationsRowsList.map { it[StringResourceTranslationTable.resourceName] }.toSet() }
        .toList()
        .zipWithNext()
        .map { adjacentElements ->
          val firstLanguageData = adjacentElements.first
          val secondLanguageDate = adjacentElements.second
          "${firstLanguageData.first}:${secondLanguageDate.first}" to
            (firstLanguageData.second - secondLanguageDate.second) + (secondLanguageDate.second - firstLanguageData.second)
        }
        .onEach { println(it) }
        .all { it.second.isEmpty() }
        .let { assertThat(it).isTrue() }
    }
  }

  @Disabled
  @Test
  fun `SHOULD have all new added translations`() {
    transaction(database) {
      StringResourceTranslationTable
        .select {
          (StringResourceTranslationTable.language eq "pl") and
            (StringResourceTranslationTable.resourceName inList listOf(
              "notifications_permission_fullscreen_title",
              "notifications_permission_fullscreen_description",
              "notifications_permission_fullscreen_button_text"
            ))
        }
        .onEach { println(it) }
    }
  }

  @Disabled
  @Test
  fun `SHOULD replace texts with template names for games`() {
    val appList = listOf(
      "com.gimica.hexapuzzlefun", "com.gimica.ballbounce", "com.relaxingbraintraining.mousekeeper",
      "com.relaxingbraintraining.ballrush", "com.relaxingbraintraining.dunk", "com.gimica.emojiclickers",
      "com.gimica.hexmatch", "com.gimica.madsmash", "com.gimica.mergeblast",
      "com.relaxingbraintraining.numbermerge", "com.gimica.mixblox",
      "com.relaxingbraintraining.onelineadvanced", "com.relaxingbraintraining.popslice",
      "com.gimica.puzzlepopblaster", "com.relaxingbraintraining.rollthatball",
      "com.relaxingbraintraining.six", "com.relaxingbraintraining.snakeclash",
      "com.gimica.solitaireverse", "com.gimica.sugarmatch",
      "com.gimica.treasuremaster", "com.gimica.triviamadness"
    )
    transaction(database) {
      val sources = GamesTable
        .slice(
          GamesTable.applicationId, GamesTable.description,
          GamesTable.infoTextInstallTop, GamesTable.infoTextInstallBottom
        )
        .select {
          (GamesTable.applicationId inList appList) and (GamesTable.platform eq "android")
        }
        .map {
          listOf(
            it[GamesTable.description],
            it[GamesTable.infoTextInstallTop],
            it[GamesTable.infoTextInstallBottom]
          )
        }.flatten()

      StringResourceTranslationTable
        .select {
          (StringResourceTranslationTable.resourceName inList sources) and
            (StringResourceTranslationTable.language eq "en")
        }
        .map { println(it[StringResourceTranslationTable.resourceName]) }
        .count()
        .let { assertThat(it).isEqualTo(105) } // 21 * 5
    }
  }

  // https://app.asana.com/0/1155692811605665/1205182995196236/f
  // should be synced with business if translations became different
  @Test
  fun `SHOULD have same translations for how_offerwall_works_text AND info_dialog_offerwall_text`() {
    transaction(database) {
      val howOfferwallWorksTranslations = StringResourceTranslationTable
        .slice(StringResourceTranslationTable.language, StringResourceTranslationTable.translation)
        .select { StringResourceTranslationTable.resourceName eq "how_offerwall_works_text" }
        .associate { row -> row[StringResourceTranslationTable.language] to row[StringResourceTranslationTable.translation] }

      val infoDialogOfferwallTranslations = StringResourceTranslationTable
        .slice(StringResourceTranslationTable.language, StringResourceTranslationTable.translation)
        .select { StringResourceTranslationTable.resourceName eq "info_dialog_offerwall_text" }
        .associate { row -> row[StringResourceTranslationTable.language] to row[StringResourceTranslationTable.translation] }

      assertThat(howOfferwallWorksTranslations).isEqualTo(infoDialogOfferwallTranslations)
    }
  }

  @Test
  fun `Android app translation consistency`() {
    transaction(database) {
      val actual =
        AppTranslationTable
          .select {
            AppTranslationTable.appPlatform eq ANDROID.name
          }
          .groupBy { it[AppTranslationTable.language] }
          .mapValues { (_, row) -> row.map { it[AppTranslationTable.translation] } }

      assertEquals(setOf("en", "de", "es", "es-mx", "fr", "it", "ja", "ko", "nl", "pl", "pt", "pt-br", "zh-hk", "zh-tw"), actual.keys)
      assertThat(actual.values).each { it.hasSize(504) }
    }
  }

  @Test
  fun `iOS app translation consistency`() {
    transaction(database) {
      val actual =
        AppTranslationTable
          .select {
            AppTranslationTable.appPlatform eq IOS.name
          }
          .groupBy { it[AppTranslationTable.language] }
          .mapValues { (_, row) -> row.map { it[AppTranslationTable.translation] } }

      assertEquals(setOf("en", "ja"), actual.keys)
      assertThat(actual.values).each { it.hasSize(293) }
    }
  }

  @Test
  fun `Android app translation doesn't have XML escaping`() {
    transaction(database) {
      val translations =
        AppTranslationTable
          .select {
            AppTranslationTable.appPlatform eq ANDROID.name
          }
          .map { it[AppTranslationTable.translation] }

      translations.forEach { translation ->
        assertThat(translation).doesNotContain("<![CDATA[")
      }
    }
  }

  @Test
  fun `iOS app Webgl PastRewards NoResults Subtitle translation consistency check`() {
    transaction(database) {
      val actual =
        AppTranslationTable
          .select {
            (AppTranslationTable.appPlatform eq IOS.name) and
              (AppTranslationTable.resourceName eq "Webgl.PastRewards.NoResults.Subtitle") and
              (AppTranslationTable.language eq "en")
          }
          .first()

      assertThat(actual[AppTranslationTable.translation])
        .isEqualTo("You haven't redeemed any rewards yet. Check back here to see your reward history.")
    }
  }
}