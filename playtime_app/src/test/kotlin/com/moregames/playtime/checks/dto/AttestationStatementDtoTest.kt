package com.moregames.playtime.checks.dto

import assertk.assertThat
import assertk.assertions.isEqualTo
import org.junit.jupiter.api.Test

class AttestationStatementDtoTest {

  @Test
  fun `SHOULD override fields ON copy`() {
    val statement = AttestationStatementDto(
      timestampMs = 1636897470147,
      nonce = "bm9uY2U=",
      apkPackageName = "apkPackageName",
      apkCertificateDigestSha256 = arrayOf("dGVzdA==", "dGVzdA=="),
      apkDigestSha256 = "dGVzdA==",
      ctsProfileMatch = true,
      basicIntegrity = true,
      evaluationType = "HARDWARE_BACKED",
      advice = "no advice",
      error = "no error",
      versionCode = 3,
      appLicensingVerdict = "licencingVerdict",
      appRecognitionVerdict = "recognitionVerdict",
      deviceRecognitionVerdict = listOf("verdict1", "verdict2"),
    )

    val actual = statement.copy(
      timestampMs = 1000,
      nonce = "new nonce"
    )

    actual.apply {
      assertThat(this.timestampMs).isEqualTo(1000)
      assertThat(this.nonce).isEqualTo("new nonce")
      //
      assertThat(this.error).isEqualTo(statement.error)
      assertThat(this.apkPackageName).isEqualTo(statement.apkPackageName)
      assertThat(this.apkCertificateDigestSha256).isEqualTo(statement.apkCertificateDigestSha256)
      assertThat(this.apkDigestSha256).isEqualTo(statement.apkDigestSha256)
      assertThat(this.ctsProfileMatch).isEqualTo(statement.ctsProfileMatch)
      assertThat(this.basicIntegrity).isEqualTo(statement.basicIntegrity)
      assertThat(this.evaluationType).isEqualTo(statement.evaluationType)
      assertThat(this.advice).isEqualTo(statement.advice)
      assertThat(this.versionCode).isEqualTo(statement.versionCode)
      assertThat(this.appLicensingVerdict).isEqualTo(statement.appLicensingVerdict)
      assertThat(this.appRecognitionVerdict).isEqualTo(statement.appRecognitionVerdict)
      assertThat(this.deviceRecognitionVerdict).isEqualTo(statement.deviceRecognitionVerdict)
    }
  }
}