package com.moregames.playtime.subscribers

import com.moregames.base.messaging.dto.CoinGoalReachedEventDto
import com.moregames.base.util.mock
import com.moregames.base.util.redis.SafeJedisClient
import com.moregames.playtime.cashstreak.CashStreakEngine
import com.moregames.playtime.user.UserService
import kotlinx.coroutines.runBlocking
import org.mockito.kotlin.*
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.test.Test

class CoinGoalReachedEventPushSubscriberTest {
  private val cashStreakEngine: CashStreakEngine = mock()
  private val jedisClient: SafeJedisClient = mock()
  private val userService: UserService = mock {
    onBlocking { userExists("userId") } doReturn true
  }

  private val service = CoinGoalReachedEventPushSubscriber(cashStreakEngine, jedisClient, userService)

  companion object {
    const val ID = "uuid"
    private val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    const val CACHE_KEY = "CoinGoalReachedEventProcessed:userId##12"
    val event = CoinGoalReachedEventDto(
      id = ID,
      userId = "userId",
      coinGoal = 25,
      counter = 12,
      periodEnd = now,
      createdAt = now
    )
  }

  @Test
  fun `SHOULD skip processing ON incoming event WHEN user is not exists`() {
    userService.mock({ userExists("userId") }, false)

    runBlocking { service.handle(event) }

    verifyNoInteractions(jedisClient)
    verifyNoInteractions(cashStreakEngine)
  }

  @Test
  fun `SHOULD trigger cash streak accepted ON incoming event`() {
    jedisClient.mock({ get(CACHE_KEY) }, ID)

    runBlocking { service.handle(event) }

    verifyBlocking(jedisClient) { set(eq(CACHE_KEY), eq(event.id), argThat { this.toString() == "[ex, 14400, nx]" }) }
    verifyBlocking(jedisClient) { get(CACHE_KEY) }
    verifyBlocking(cashStreakEngine) { acceptCashStreak("userId") }

  }

  @Test
  fun `SHOULD not trigger cash streak accepted ON incoming event WHEN previous similar event was already processed`() {
    jedisClient.mock({ get(CACHE_KEY) }, UUID.randomUUID().toString())

    runBlocking { service.handle(event) }

    verifyBlocking(jedisClient) { set(eq(CACHE_KEY), eq(event.id), argThat { this.toString() == "[ex, 14400, nx]" }) }
    verifyBlocking(jedisClient) { get(CACHE_KEY) }
    verifyNoInteractions(cashStreakEngine)
  }
}