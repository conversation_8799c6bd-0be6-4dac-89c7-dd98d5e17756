package com.moregames.playtime.buseffects

import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.junit.MockExtension
import com.moregames.playtime.app.ga.GoogleAnalyticsService
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.verify

@ExtendWith(MockExtension::class)
class ForceAssignGaUserPropertiesMessageHandlerTest(
  private val googleAnalyticsService: GoogleAnalyticsService,
  private val messageBus: MessageBus,
) {

  private val underTest = ForceAssignGaUserPropertiesMessageHandler(
    googleAnalyticsService = googleAnalyticsService,
    messageBus = messageBus,
  )

  @Test
  fun `SHOULD properly process handleForceAssignGaUserPropertiesEffect`() = runTest {
    underTest.handleForceAssignGaUserPropertiesEffect(
      ForceAssignGaUserPropertiesEffect(
        userId = USER_ID,
        firebaseAppId = FIREBASE_APP_ID,
        platform = platform,
      )
    )

    verify(messageBus).publish(
      ForceAssignGaUserPropertiesMessage(
        userId = USER_ID,
        firebaseAppId = FIREBASE_APP_ID,
        platform = platform,
      )
    )
  }

  @Test
  fun `SHOULD properly process handleForceAssignGaUserPropertiesMessage`() = runTest {
    underTest.handleForceAssignGaUserPropertiesMessage(
      ForceAssignGaUserPropertiesMessage(
        userId = USER_ID,
        firebaseAppId = FIREBASE_APP_ID,
        platform = platform,
      )
    )

    verify(googleAnalyticsService).sendForceAssignedUserPropertiesEvent(
      userId = USER_ID,
      firebaseAppId = FIREBASE_APP_ID,
      platform = platform,
    )
  }

  private companion object {
    private const val USER_ID = "userId"
    private const val FIREBASE_APP_ID = "firebaseAppId"
    private val platform = AppPlatform.ANDROID
  }
}