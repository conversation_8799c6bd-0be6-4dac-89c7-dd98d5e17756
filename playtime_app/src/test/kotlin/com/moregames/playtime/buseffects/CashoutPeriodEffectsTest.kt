package com.moregames.playtime.buseffects

import com.justplayapps.service.rewarding.earnings.UserCurrentCoinsBalance
import com.justplayapps.service.rewarding.earnings.dto.Earnings
import com.justplayapps.service.rewarding.earnings.dto.EarningsCalculationResult
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.EarningsCappedEventDto
import com.moregames.base.util.TimeService
import com.moregames.base.util.io
import com.moregames.base.util.mock
import com.moregames.playtime.earnings.dto.UsedQuota
import com.moregames.playtime.earnings.dto.UserEarningsQuotasDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.onboarding.progressbar.OnboardingProgressBarService
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal
import java.time.Instant

@OptIn(ExperimentalCoroutinesApi::class)
class CashoutPeriodEffectsTest {
  private val now = Instant.now()

  private val userService: UserService = mock()
  private val fraudScoreService: FraudScoreService = mock()
  private val timeService: TimeService = mock {
    on { now() } doReturn now
  }
  private val bigQueryEventPublisher: BigQueryEventPublisher = mock()
  private val scope = TestScope()
  private val abTestingService: AbTestingService = mock()
  private val onboardingProgressBarService: OnboardingProgressBarService = mock()
  private val messageBus: MessageBus = mock()

  private val underTest = CashoutPeriodEffects(
    userService = userService,
    fraudScoreService = fraudScoreService,
    timeService = timeService,
    bigQueryEventPublisher = bigQueryEventPublisher,
    coroutineScope = { scope.io() },
    abTestingService = abTestingService,
    onboardingProgressBarService = onboardingProgressBarService,
    messageBus = messageBus,
  )

  @BeforeEach
  fun setUp() {
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, ClientExperiment.SPECIAL_CASHOUT_OFFERS) }, false)
    abTestingService.mock({ isEm2Participant(any()) }, false)
  }

  @Test
  fun `SHOULD call cashout offer service WHEN special cashout offer participant`() {
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(appPlatform = AppPlatform.IOS))
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, ClientExperiment.SPECIAL_CASHOUT_OFFERS) }, true)

    runTest { underTest.handleCashoutPeriodEndedEffect(CashoutPeriodEndedEffect(USER_ID, em2EarningsComputationData)) }

    scope.advanceUntilIdle()

    verifyBlocking(fraudScoreService) { checkUserApplovinRevenueFrequencyAndBlockFraudsters(USER_ID) }
    verifyBlocking(fraudScoreService) { blockUserOnGameAutomation(USER_ID) }
    verifyBlocking(messageBus) { publish(CreateCashoutOfferSetEffect(USER_ID)) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        listOf(
          EarningsCappedEventDto(
            metaId = 123,
            userId = USER_ID,
            cappedAmount = BigDecimal("15.230000"),
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.GAME_REAL_REVENUE_CUT,
          ),
          EarningsCappedEventDto(
            metaId = 123,
            userId = USER_ID,
            cappedAmount = BigDecimal("3.770000"),
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.QUOTA_REVENUE_CUT,
          ),
        )
      )
    }
  }

  @Test
  fun `SHOULD call onboarding progress bar WHEN onboarding progress bar participant`() {
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(appPlatform = AppPlatform.IOS))
    userService.mock({ getUserLastGameCoinsDate(USER_ID) }, Instant.now())
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, ClientExperiment.ANDROID_ONBOARDING_PROGRESS_BAR) }, true)

    runTest { underTest.handleCashoutPeriodEndedEffect(CashoutPeriodEndedEffect(USER_ID, em2EarningsComputationData)) }

    scope.advanceUntilIdle()

    verifyBlocking(onboardingProgressBarService) {
      activateRouteToCashout(USER_ID)
      completePlayFirstGame(USER_ID)
    }
    verifyBlocking(fraudScoreService) { checkUserApplovinRevenueFrequencyAndBlockFraudsters(USER_ID) }
    verifyBlocking(fraudScoreService) { blockUserOnGameAutomation(USER_ID) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        listOf(
          EarningsCappedEventDto(
            metaId = 123,
            userId = USER_ID,
            cappedAmount = BigDecimal("15.230000"),
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.GAME_REAL_REVENUE_CUT,
          ),
          EarningsCappedEventDto(
            metaId = 123,
            userId = USER_ID,
            cappedAmount = BigDecimal("3.770000"),
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.QUOTA_REVENUE_CUT,
          ),
        )
      )
    }
  }

  companion object {
    private const val USER_ID = "userId"

    val em2EarningsComputationData = EarningsCalculationResult.Em2(
      simpleCalculationResult = EarningsCalculationResult.Simple(
        metaId = 123,
        amount = BigDecimal.ONE,
        amountNoRounding = BigDecimal.TEN
      ),
      noEarnings = false,
      realRevenue = BigDecimal("3.0"),
      realGameRevenue = BigDecimal("4.0"),
      em2CoinsBalance = UserCurrentCoinsBalance(
        gameCoins = BigDecimal("600.0"),
        offerCoins = BigDecimal("700.0"),
        bonusCoins = BigDecimal("800.0")
      ),
      coinsForOneDollar = BigDecimal("100.0"),
      em2Revenue = BigDecimal("5.77"),
      em2GameRevenue = BigDecimal("5.0"),
      earnings =
        Earnings(
          userId = USER_ID,
          earningsSum = BigDecimal("1.0"),
          quotasDto = UserEarningsQuotasDto(
            userId = USER_ID,
            quotas = listOf("quotas"),
            periodEnd = Instant.now(),
            values = listOf(BigDecimal("0.5"))
          ),
          usedQuota = UsedQuota(BigDecimal("0.5"))
        ),
    )
  }
}