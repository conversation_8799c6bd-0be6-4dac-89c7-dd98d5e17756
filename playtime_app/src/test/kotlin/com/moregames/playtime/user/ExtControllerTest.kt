package com.moregames.playtime.user

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.secret.SecretService
import com.moregames.base.user.dto.EarlyInterstitial
import com.moregames.base.util.Constants.AUTHENTICATION_TOKEN_PARAM_NAME
import com.moregames.base.util.base64Encoded
import com.moregames.base.util.mock
import com.moregames.base.util.trimToOneLineString
import com.moregames.base.vpn.SanityCheckRequestDto
import com.moregames.base.vpn.SanityCheckResponseApiDto
import com.moregames.playtime.app.PlaytimeSecrets
import com.moregames.playtime.general.GamesSetupService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.dto.GdprStateDto
import com.moregames.playtime.user.rampid.RampIdController
import com.moregames.playtime.user.unifiedid.UnifiedIdController
import com.moregames.playtime.user.verification.VerificationService
import com.moregames.playtime.user.verification.VerificationService.VerificationStatusExt.LIVENESS_CHECK_FAILED
import com.moregames.playtime.util.installDefaultContentNegotiation
import com.moregames.playtime.utils.Json.defaultJsonConverter
import com.moregames.playtime.utils.adUnitIdsApiDtoStub
import com.moregames.playtime.utils.userDtoStub
import com.moregames.playtime.vpn.SanityCheckService
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.routing.*
import io.ktor.server.testing.*
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*

@ExperimentalSerializationApi
class ExtControllerTest {
  private val userPersistenceService: UserPersistenceService = mock()
  private val rewardingFacade: RewardingFacade = mock()
  private val gamesSetupService: GamesSetupService = mock()
  private val secretService: SecretService = mock()
  private val marketService: MarketService = mock()
  private val verificationService: VerificationService = mock()
  private val userService: UserService = mock {
    onBlocking { getUser(USER_ID) } doReturn userData
  }
  private val androidGamesController: AndroidGamesController = mock()
  private val iosExtGamesController: IosExtGamesController = mock()
  private val sanityCheckService: SanityCheckService = mock()
  private val json: Json = defaultJsonConverter
  private val unifiedIdController = mock<UnifiedIdController>()
  private val rampIdController = mock<RampIdController>()
  private val abTestingService: AbTestingService = mock()

  private fun controller(): Application.() -> Unit = {
    install(IgnoreTrailingSlash)
    installDefaultContentNegotiation()
    routing {
      ExtController(
        userPersistenceService = userPersistenceService,
        rewardingFacade = rewardingFacade,
        gamesSetupService = gamesSetupService,
        secretService = secretService,
        marketService = marketService,
        verificationService = verificationService,
        userService = userService,
        androidGamesController = androidGamesController,
        iosExtGamesController = iosExtGamesController,
        sanityCheckService = sanityCheckService,
        unifiedIdController = unifiedIdController,
        rampIdController = rampIdController,
        json = json,
      ).startRouting(this)
    }
  }

  private companion object {
    const val USER_ID = "userId"
    const val PACKAGE_ID = "packageId"
    const val AUTH_TOKEN = "authTokenValue"
    val now: Instant = Instant.now()
    const val ORCHESTRATOR_AUTH_TOKEN_SALT = "authenticationToken"
    const val AUTHENTICATION_TOKEN_VALUE = "8ce25f6323d3fd58d5e5e580d2d485ae"
    const val WRONG_AUTHENTICATION_TOKEN_VALUE = "_8ce25f6323d3fd58d5e5e580d2d485ae_"
    const val SANITY_CHECK_BASE64 =
      "eyJpcCI6ImlwIiwiY291bnRyeUNvZGUiOiJjb3VudHJ5Q29kZSIsInZwbkRldGVjdGVkIjpmYWxzZSwic2tpcENhY2hlIjp0cnVlLCJwYWNrYWdlSWQiOiJwYWNrYWdlSWQiLCJnb29nbGVBZElkIjoiZ29vZ2xlQWRJZCIsImlkZnYiOiJpZGZ2IiwidXNlcklkIjoidXNlcklkIn0="

    val sanityCheckRequestDto = SanityCheckRequestDto(
      ip = "ip",
      countryCode = "countryCode",
      vpnDetected = false,
      skipCache = true,
      packageId = "packageId",
      googleAdId = "googleAdId",
      idfv = "idfv",
      userId = "userId"
    )

    val userData = userDtoStub.copy(id = USER_ID, createdAt = now, appVersion = 31, isBanned = true, trackingData = null)
  }

  @BeforeEach
  fun before() {
    secretService.mock({ secretValue(PlaytimeSecrets.ORCHESTRATOR_AUTH_TOKEN) }, AUTH_TOKEN)
    secretService.mock({ secretValue(PlaytimeSecrets.ORCHESTRATOR_AUTH_TOKEN_SALT) }, ORCHESTRATOR_AUTH_TOKEN_SALT)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return banned status ON banStatus call`(isBanned: Boolean) = withTestApplication(controller()) {
    userService.mock({ getUser(USER_ID) }, userData.copy(isBanned = isBanned))

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/ext/banStatus?userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    ) {
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(isBanned.toString())
  }

  @ParameterizedTest
  @ValueSource(strings = ["null", "", "************************************************************", "tooLongEmail"])
  fun `SHOULD return user status ON status call`(option: String) = withTestApplication(controller()) {
    userService.mock({ getUser(USER_ID) }, userData.copy(locale = Locale.forLanguageTag("it")))
    userPersistenceService.mock({ getUserFirstAppVersion(USER_ID) }, 30)
    marketService.mock({ getJPMarket() }, "us-test")
    verificationService.mock({ getFaceVerificationStatus(USER_ID) }, LIVENESS_CHECK_FAILED)
    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(USER_ID, userData.appPlatform, activate = false) }, false)

    val additionalParameter = when (option) {
      "null" -> ""
      "tooLongEmail" -> "&userEmailBase64=" +
        "${"iiiiiiiiii".replace("i", "iiiiiiiiiiii")}@gmail.com".base64Encoded()

      else -> "&userEmailBase64=$option"
    }

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/ext/status?userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE${additionalParameter}"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content)
      .isEqualTo(
        """
        |{"isBanned":true,
        |"firstAppVersion":30,
        |"appVersion":31,
        |"market":"us-test",
        |"faceVerificationStatus":"LIVENESS_CHECK_FAILED",
        |"deviceLocale":"it",
        |"countryCode":"US"}""".trimToOneLineString()
      )
    if (option == "************************************************************")
      verifyBlocking(userPersistenceService) { saveUserEmail(USER_ID, "Shayla McWilliams <<EMAIL>>") }
    else
      verifyBlocking(userPersistenceService, never()) { saveUserEmail(any(), any()) }
  }

  @Test
  fun `SHOULD return user status with lastLowEarningsCashoutPeriodEnd ON status call`() = withTestApplication(controller()) {
    val someTime = Instant.now().truncatedTo(ChronoUnit.SECONDS).minusSeconds(10500)
    userService.mock({ getUser(USER_ID) }, userData.copy(locale = Locale.forLanguageTag("it")))
    rewardingFacade.mock({ getLastLowEarningsCashoutPeriodEnd(USER_ID) }, someTime)
    userPersistenceService.mock({ getUserFirstAppVersion(USER_ID) }, 30)
    marketService.mock({ getJPMarket() }, "us-test")
    verificationService.mock({ getFaceVerificationStatus(USER_ID) }, LIVENESS_CHECK_FAILED)
    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(USER_ID, userData.appPlatform, activate = false) }, false)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/ext/status?userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content)
      .isEqualTo(
        """
        |{"isBanned":true,
        |"firstAppVersion":30,
        |"appVersion":31,
        |"market":"us-test",
        |"faceVerificationStatus":"LIVENESS_CHECK_FAILED",
        |"deviceLocale":"it",
        |"countryCode":"US",
        |"lastLowEarningsCashoutPeriodEnd":"$someTime"}""".trimToOneLineString()
      )
  }

  @Test
  fun `SHOULD return user status with inappBalanceNotificationsEnabled ON status call`() = withTestApplication(controller()) {
    val someTime = Instant.now().truncatedTo(ChronoUnit.SECONDS).minusSeconds(10500)
    userService.mock({ getUser(USER_ID) }, userData.copy(locale = Locale.forLanguageTag("it")))
    rewardingFacade.mock({ getLastLowEarningsCashoutPeriodEnd(USER_ID) }, someTime)
    userPersistenceService.mock({ getUserFirstAppVersion(USER_ID) }, 30)
    marketService.mock({ getJPMarket() }, "us-test")
    verificationService.mock({ getFaceVerificationStatus(USER_ID) }, LIVENESS_CHECK_FAILED)
    abTestingService.mock({ isGameBalanceUpdateNotificationParticipant(USER_ID, userData.appPlatform, activate = false) }, true)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/ext/status?userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content)
      .isEqualTo(
        """
        |{"isBanned":true,
        |"firstAppVersion":30,
        |"appVersion":31,
        |"market":"us-test",
        |"faceVerificationStatus":"LIVENESS_CHECK_FAILED",
        |"deviceLocale":"it",
        |"countryCode":"US",
        |"lastLowEarningsCashoutPeriodEnd":"$someTime"}""".trimToOneLineString()
      )
  }

  @Test
  fun `SHOULD get result from games setup service ON get adUnitIdsByGoogleAdId WHEN package is NOT treasuremaster`() = withTestApplication(controller()) {
    gamesSetupService.mock({ loadGameAdUnitIds(PACKAGE_ID, USER_ID) }, adUnitIdsApiDtoStub)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/ext/adUnitIds?packageId=$PACKAGE_ID&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(defaultJsonConverter.encodeToString(adUnitIdsApiDtoStub))
  }

  @Test
  fun `SHOULD return Unauthorized ON any ext method call WHEN auth token is not provided`() = withTestApplication(controller()) {
    listOf(
      "/ext/banStatus?userId=$USER_ID",
      "/ext/status?userId=$USER_ID",
      "/ext/adUnitIds?packageId=$PACKAGE_ID&userId=$USER_ID",
      "/ext/shouldShowEarlyInterstitial?userId=$USER_ID",
    ).forEach { uri ->
      val response = handleRequest(
        method = HttpMethod.Get,
        uri = uri
      )

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.Unauthorized)
    }
  }

  @Test
  fun `SHOULD return Unauthorized ON any ext method call WHEN authentication token is not correct`() = withTestApplication(controller()) {
    listOf(
      "/ext/banStatus?userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$WRONG_AUTHENTICATION_TOKEN_VALUE",
      "/ext/status?userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$WRONG_AUTHENTICATION_TOKEN_VALUE",
      "/ext/adUnitIds?packageId=$PACKAGE_ID&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$WRONG_AUTHENTICATION_TOKEN_VALUE",
    ).forEach { uri ->
      val response = handleRequest(
        method = HttpMethod.Get,
        uri = uri
      )

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.Unauthorized)
    }
  }

  @Test
  fun `SHOULD return true ON shouldShowEarlyInterstitial`() = withTestApplication(controller()) {
    handleRequest(
      method = HttpMethod.Get,
      uri = "/ext/shouldShowEarlyInterstitial?userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    )
      .let {
        assertThat(it.response.status()).isEqualTo(HttpStatusCode.OK)
        assertThat(it.response.content).isEqualTo(defaultJsonConverter.encodeToString(EarlyInterstitial(true)))
      }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return correct GdprState ON gdprState endpoint call`(booleans: Boolean) = withTestApplication(controller()) {
    userService.mock({ getGdprState(USER_ID) }, GdprStateDto(booleans))

    val actual = handleRequest(
      method = HttpMethod.Get,
      uri = "/ext/gdprState?userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    ) {
    }

    assertThat(defaultJsonConverter.decodeFromString<GdprStateDto>(actual.response.content!!)).isEqualTo(GdprStateDto(booleans))
  }

  @Test
  fun `SHOULD return sanity check response ON endpoint call`() = withTestApplication(controller()) {
    val checkResponseApiDto = SanityCheckResponseApiDto(
      isValidUser = false,
      isFromAllowedCountry = true,
      isUsingVpn = true,
      fraudScore = 0
    )
    sanityCheckService.mock(
      { checkRequest(USER_ID, sanityCheckRequestDto) },
      checkResponseApiDto
    )

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/ext/sanityCheck?userId=$USER_ID&sanityCheckRequest=$SANITY_CHECK_BASE64&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    )

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(response.response.content).isEqualTo(defaultJsonConverter.encodeToString(checkResponseApiDto))
  }
}
