package com.moregames.playtime.user

import assertk.assertThat
import assertk.assertions.*
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.read.ListAppender
import com.google.inject.Provider
import com.justplayapps.playtime.proto.userCreatedEvent
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.app.BuildVariant
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.dto.TrackingDataType
import com.moregames.base.dto.TrackingDataType.IDFA
import com.moregames.base.dto.TrackingDataType.IDFV
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.exceptions.CountryIsNotAllowedException
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.ipregistry.IpData
import com.moregames.base.mail.MailAddress
import com.moregames.base.mail.MailService
import com.moregames.base.messaging.dto.*
import com.moregames.base.user.RevenueTotals
import com.moregames.base.user.dto.CreateUserData
import com.moregames.base.user.dto.CreateUserRequestDto
import com.moregames.base.user.dto.UserRequestMetadata
import com.moregames.base.util.*
import com.moregames.base.util.ClientVersionsSupport.getHardUpdateVersion
import com.moregames.base.util.ClientVersionsSupport.getUserCreationMinAppVersion
import com.moregames.playtime.administration.user.PersonalDataService
import com.moregames.playtime.app.PlaytimeFeatureFlags
import com.moregames.playtime.buseffects.*
import com.moregames.playtime.buseffects.FraudEffect.*
import com.moregames.playtime.checks.IpService
import com.moregames.playtime.checks.dto.SanityValue
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.notifications.NotificationType
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.PopupMessageReason.LIMITED_AD_TRACKING
import com.moregames.playtime.user.UserEcpmGroupsService.DefineUsersEcpmGroupEffect
import com.moregames.playtime.user.UserPersistenceService.GamePlayStatusDto
import com.moregames.playtime.user.UserPersistenceService.UserIdAndTrackingId
import com.moregames.playtime.user.cashout.CashoutPeriodsPersistenceService
import com.moregames.playtime.user.cashout.CashoutPersistenceService
import com.moregames.playtime.user.cashout.dto.CashoutPeriodDto
import com.moregames.playtime.user.dto.*
import com.moregames.playtime.user.exception.IncompatibleVersionException
import com.moregames.playtime.user.tracking.TRACKING_ID_STUB
import com.moregames.playtime.user.tracking.TRACKING_ID_STUB_SHORT
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.user.tracking.TrackingService
import com.moregames.playtime.utils.*
import com.moregames.playtime.webhook.adjust.AdjustEventPersistenceService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.*
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.*
import org.slf4j.LoggerFactory
import redis.clients.jedis.Jedis
import redis.clients.jedis.JedisPool
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import kotlin.test.assertFalse
import kotlin.test.assertTrue


@ExperimentalCoroutinesApi
class UserServiceTest {

  private val abTestingService: AbTestingService = mock()
  private val userPersistenceService = mock<UserPersistenceService>()
  private val testScope = TestScope()
  private val marketService = mock<MarketService>()
  private val bigQueryEventPublisher: BigQueryEventPublisher = mock()
  private val ipService: IpService = mock()
  private val rewardingFacade: RewardingFacade = mock()
  private val timeService: TimeService = mock()
  private val localLogger = LoggerFactory.getLogger(kotlinx.coroutines.CoroutineScope::class.java) as Logger
  private val logEntriesListAppender = ListAppender<ILoggingEvent>()
  private val trackingService: TrackingService = mock()
  private val cashoutPeriodsPersistenceService: CashoutPeriodsPersistenceService = mock()
  private val jedis: Jedis = mock()
  private val jedisPool: JedisPool = mock()
  private val adjustEventPersistenceService: AdjustEventPersistenceService = mock()
  private val cashoutPersistenceService: CashoutPersistenceService = mock()
  private val personalDataService: PersonalDataService = mock()
  private val mailService: MailService = mock()
  private val buildVariantProvider: Provider<BuildVariant> = mock()
  private val applicationConfig: ApplicationConfig = mock {
    on { justplayMarket } doReturn "test-market"
  }
  private val encryptionService: EncryptionService = mock()
  private val featureFlagsFacade: FeatureFlagsFacade = mock()
  private val userDataCache: UserDataCache = mock()

  private val messageBus: MessageBus = mock()

  private val userService = UserService(
    abTestingService = abTestingService,
    userPersistenceService = userPersistenceService,
    coroutineScope = { testScope.default() },
    marketService = marketService,
    messageBus = messageBus,
    bigQueryEventPublisher = bigQueryEventPublisher,
    ipService = ipService,
    rewardingFacade = rewardingFacade,
    timeService = timeService,
    trackingService = trackingService,
    cashoutPeriodsPersistenceService = cashoutPeriodsPersistenceService,
    adjustEventPersistenceService = adjustEventPersistenceService,
    cashoutPersistenceService = cashoutPersistenceService,
    personalDataService = personalDataService,
    mailService = mailService,
    buildVariantProvider = buildVariantProvider,
    applicationConfig = applicationConfig,
    encryptionService = encryptionService,
    featureFlagsFacade = featureFlagsFacade,
    userDataCache = userDataCache,
  )

  init {
    whenever(jedisPool.resource).thenReturn(jedis)
  }

  val onUserSpecificRequestCallNoData: suspend (Locale?) -> Unit = {
    userService.onUserSpecificRequest(
      userId = USER_ID,
      userRequestMetadata = UserRequestMetadata(null, null, null, null, null),
      ipData = null,
      appVersion = appVersionAndroid,
      locale = it,
      market = "market",
    )
  }

  val onUserSpecificRequestCallWithData: suspend () -> Unit = {
    userService.onUserSpecificRequest(
      userId = USER_ID,
      userRequestMetadata = UserRequestMetadata(
        ip = IP,
        forwardedIp = null,
        countryCode = COUNTRY_CODE,
        loadBalancerCountryData = COUNTRY_CODE,
        forwardedIpRaw = null
      ),
      ipData = null,
      appVersion = appVersionAndroid,
      market = "market",
    )
  }

  private companion object {
    val now = Instant.now()!!.truncatedTo(ChronoUnit.SECONDS)!!
    const val USER_ID = "user1"
    const val IP = "123"
    const val COUNTRY_CODE = "CA"
    const val GAID = "8bdd3a8d-3b43-4303-9be0-329a242a26bd"
    const val IDFA_VALUE = "1dfa-1dfa"
    const val ADJUST_ID = "addd3a8d3b4343039be0329a242a26ad"
    const val FIREBASE_APP_ID = "firebase-app-id"
    val appVersionAndroid = AppVersionDto(ANDROID, getUserCreationMinAppVersion(ANDROID))
    val appVersionIos = AppVersionDto(IOS, 105)
    val emptyAppVersion = AppVersionDto(ANDROID, 0)
    val additionalData = CreateUserRequestDto(
      networkCountry = "us",
      networkOperatorName = "verizon",
      simCountry = "ca",
      simOperatorName = "Verizon Canada",
      deviceLocale = "en",
      deviceLanguageTag = "en-US",
      installedFromStore = true
    )
    private val userStub = User(
      userId = "userId",
      googleAdId = null,
      deviceToken = null,
      coinsGoal = 4200,
      createdAt = Instant.now(),
      appPlatform = ANDROID,
      appVersion = 0,
      countryCode = "US",
      isConsentedToAnalytics = false,
    )
  }

  @BeforeEach
  fun before() {
    Dispatchers.setMain(StandardTestDispatcher())
    trackingService.mock({ isGoogleAdIdWhitelisted(any()) }, false)
    trackingService.mock({ isTrackingDataWhitelisted(any()) }, false)
    trackingService.mock({ isStubGoogleAdId(TRACKING_ID_STUB) }, true)
    trackingService.mock({ isStubGoogleAdId(TRACKING_ID_STUB_SHORT) }, true)
    trackingService.mock({ isStubTrackingId(TRACKING_ID_STUB) }, true)
    trackingService.mock({ isStubTrackingId(TRACKING_ID_STUB_SHORT) }, true)
    marketService.mock({ isUserFromAllowedCountry(any()) }, true)
    marketService.mock({ getAllowedCountries() }, setOf("US", "CA"))
    ipService.mock(
      { extractIpData(any()) }, IpData.Limited(ip = IP, countryCode = COUNTRY_CODE)
    )
    timeService.mock({ now() }, now)
    abTestingService.mock({ isEm2Participant(USER_ID) }, false)
    logEntriesListAppender.start()
    localLogger.addAppender(logEntriesListAppender)
    userPersistenceService.mock({ countUsersWithSameGoogleAdId(USER_ID) }, 0)
    userPersistenceService.mock({ isUserWhitelisted(USER_ID) }, false)
    userPersistenceService.mock({ findGpsLocationCountry(USER_ID) }, "CA")
    userPersistenceService.mock({ userExists(USER_ID) }, true)
    userPersistenceService.mock({ markUserAsDeleted(USER_ID) }, 1)
    userDataCache.mock({ getUserData(USER_ID) }, userDtoStub)
  }

  @AfterEach
  fun after() {
    localLogger.detachAppender(logEntriesListAppender)
    logEntriesListAppender.stop()
  }

  @Test
  fun `SHOULD throw exception ON user specific request WHEN NOT user exists`() {
    userDataCache.throwException({ getUserData(USER_ID) }, UserRecordNotFoundException(USER_ID))
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    assertThrows<UserRecordNotFoundException> {
      testScope.runTest { onUserSpecificRequestCallNoData(EN_LOCALE) }
    }
  }

  @Test
  fun `SHOULD NOT throw exception ON user specific request WHEN user exists`() {
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    testScope.runTest { onUserSpecificRequestCallNoData(EN_LOCALE) }

  }

  @ParameterizedTest
  @ValueSource(strings = ["first activity", "not first activity"])
  fun `SHOULD trigger related effect WHEN it is the first user activity this day`(option: String) {
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)
    val user = userDtoStub.copy(
      lastActiveAtDay = LocalDate.now().minusDays(2).takeIf { option == "first activity" } ?: LocalDate.now(),
      appVersion = 0
    )
    userDataCache.mock({ getUserData(USER_ID) }, user)

    testScope.runTest { onUserSpecificRequestCallNoData(ES_LOCALE) }

    if (option == "first activity")
      verifyBlocking(messageBus) { publish(OnFirstLaunchOfAppTodayEffect(user, ES_LOCALE)) }
    else
      verifyBlocking(messageBus, never()) { publish(OnFirstLaunchOfAppTodayEffect(user, ES_LOCALE)) }
  }

  @Test
  fun `SHOULD trigger related effect WHEN it is the first user activity this day plus write to log if the user was inactive`() {
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)
    val user = userDtoStub.copy(
      lastActiveAtDay = LocalDate.now().minusDays(181),
      appVersion = 0
    )
    userDataCache.mock({ getUserData(USER_ID) }, user)

    testScope.runTest { onUserSpecificRequestCallNoData(ES_LOCALE) }

    verifyBlocking(messageBus) { publish(OnFirstLaunchOfAppTodayEffect(user, ES_LOCALE)) }
  }

  @Test
  fun `SHOULD check ip ON user specific request WHEN user ip is new`() {
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, true)

    testScope.runTest { onUserSpecificRequestCallWithData() }

    verifyBlocking(messageBus) { publish(NewUserIp(USER_ID, IP, COUNTRY_CODE)) }
  }

  @Test
  fun `SHOULD NOT check ip ON user specific request WHEN NOT user ip is new`() {
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    testScope.runTest { onUserSpecificRequestCallWithData() }

    verifyBlocking(messageBus, never()) { publish(NewUserIp(USER_ID, IP, COUNTRY_CODE)) }
  }

  @Test
  fun `SHOULD update user google ad id AND remove LAT popup ON updateGoogleAdId`() {
    trackingService.mock({ isValidGoogleAdId(GAID) }, true)

    testScope.runTest {
      userService.updateGoogleAdId(USER_ID, GAID, AppVersionDto(ANDROID, 0))
    }

    verifyBlocking(trackingService) { updateGoogleAdId(USER_ID, GAID) }
    verifyBlocking(messageBus) { publishAsync(NewGoogleAdId(USER_ID, GAID, 0)) }
    verifyBlocking(messageBus) {
      publish(TrackingDataUpdatedEventDto(USER_ID, GAID, IDFA.name, ANDROID.name, ANDROID.name, "test-market", now))
    }
    verifyBlocking(messageBus) { publishAsync(DeletePopupMessageEffect(USER_ID, LIMITED_AD_TRACKING)) }
  }

  @Test
  fun `SHOULD do nothing ON updateGoogleAdId WHEN new googleAdId equals current googleAdId`() {
    userPersistenceService.mock({ fetchGoogleAdId(USER_ID) }, GAID)

    testScope.runTest {
      userService.updateGoogleAdId(USER_ID, GAID, AppVersionDto(ANDROID, 0))
    }

    verifyNoInteractions(trackingService)
    verifyBlocking(messageBus, never()) { publishAsync(NewGoogleAdId(USER_ID, GAID, 0)) }
    verifyNoInteractions(messageBus)
    verifyBlocking(messageBus, never()) { publishAsync(DeletePopupMessageEffect(USER_ID, LIMITED_AD_TRACKING)) }
  }

  @ParameterizedTest
  @ValueSource(strings = [TRACKING_ID_STUB, TRACKING_ID_STUB_SHORT])
  fun `SHOULD set LAT and add LAT popup ON updateGoogleAdId when provided id is a stub`(gaid: String) {

    testScope.runTest {
      userService.updateGoogleAdId(USER_ID, gaid, AppVersionDto(ANDROID, 0))
    }

    verifyBlocking(userPersistenceService) { setLimitedTrackingUser(USER_ID) }
    verifyBlocking(messageBus, never()) { publishAsync(CreateLatPopupMessageEffect(USER_ID, EN_LOCALE)) }
  }

  @Test
  fun `SHOULD set LAT and skip LAT popup ON updateGoogleAdId with stub id when user on android version that support GAID offer`() {
    testScope.runTest {
      userService.updateGoogleAdId(USER_ID, TRACKING_ID_STUB, AppVersionDto(ANDROID, 53))
    }

    verifyBlocking(userPersistenceService) { setLimitedTrackingUser(USER_ID) }
    verifyBlocking(messageBus, never()) { publishAsync(CreateLatPopupMessageEffect(USER_ID, EN_LOCALE)) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD update user adjustId AND generate event ON updateAdjustId`() {
    val adjustId = "adjustId"
    testScope.runTest {
      userService.updateAdjustId(USER_ID, adjustId)
    }

    verifyBlocking(userPersistenceService) { updateAdjustId(USER_ID, adjustId) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(AdjustIdTrackedEventDto(userId = USER_ID, adjustId = adjustId, createdAt = now))
    }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD trigger fraud score service ON updateAdjustId WHEN last install event found by adjustId has different userId`() {
    val adjustId = "adjustId"
    adjustEventPersistenceService.mock(
      { loadLastAdjustInstallationByAdjustId(adjustId) },
      adjustInstallationStub.copy(adjustId = adjustId, userId = "someUserId")
    )
    testScope.runTest {
      userService.updateAdjustId(USER_ID, adjustId)
    }

    verifyBlocking(userPersistenceService) { updateAdjustId(USER_ID, adjustId) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(AdjustIdTrackedEventDto(userId = USER_ID, adjustId = adjustId, createdAt = now))
    }
    verifyBlocking(messageBus) {
      publishAsync(
        AdjustDataReceived(
          userId = USER_ID,
          adjustInstallation = adjustInstallationStub.copy(
            adjustId = adjustId,
            userId = USER_ID,
            googleAdId = null,
            idfv = null
          )
        )
      )
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["the same user", "no install event"])
  fun `SHOULD NOT trigger fraud score service ON updateAdjustId WHEN no tracked install event or it is the same user`(option: String) {
    val adjustId = "adjustId"
    if (option == "no install event")
      adjustEventPersistenceService.mock({ loadLastAdjustInstallationByAdjustId(adjustId) }, null)
    else
      adjustEventPersistenceService.mock({ loadLastAdjustInstallationByAdjustId(adjustId) }, adjustInstallationStub.copy(adjustId = adjustId, userId = USER_ID))
    testScope.runTest {
      userService.updateAdjustId(USER_ID, adjustId)
    }

    verifyBlocking(userPersistenceService) { updateAdjustId(USER_ID, adjustId) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(AdjustIdTrackedEventDto(userId = USER_ID, adjustId = adjustId, createdAt = now))
    }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD return adjustId ON getAdjustId`() {
    val adjustId = "adjustId"
    userPersistenceService.mock({ getAdjustId(USER_ID) }, adjustId)

    runBlocking {
      userService.getAdjustId(USER_ID)
    }.let { assertThat(it).isEqualTo(adjustId) }

    verifyBlocking(userPersistenceService) { getAdjustId(USER_ID) }
  }

  @Test
  fun `SHOULD call persistence ON addOrUpdateFirebaseAppInstanceId`() {
    val firebaseAppId = "adjustId"
    testScope.runTest {
      userService.addOrUpdateFirebaseAppInstanceId(USER_ID, firebaseAppId, ANDROID)
    }

    verifyBlocking(userPersistenceService) { addOrUpdateFirebaseAppInstanceId(USER_ID, firebaseAppId, ANDROID) }
  }

  @Test
  fun `SHOULD update app version ON user specific request WHEN old app version is 0`() {
    userDataCache.mock({ getUserData(USER_ID) }, userDtoStub.copy(lastActiveAtDay = LocalDate.now(), appVersion = 0))
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    testScope.runTest { onUserSpecificRequestCallWithData() }

    verifyBlocking(userPersistenceService) { updateUserAppVersion(USER_ID, appVersionAndroid) }
  }

  @Test
  fun `SHOULD do nothing ON user specific request WHEN platform are equal and versions are equal`() {
    userDataCache.mock({ getUserData(USER_ID) }, userDtoStub.copy(lastActiveAtDay = LocalDate.now()))
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    val appVersion = AppVersionDto(ANDROID, appVersionAndroid.version)
    testScope.runTest {
      userService.onUserSpecificRequest(
        userId = USER_ID,
        userRequestMetadata = UserRequestMetadata(
          ip = IP,
          forwardedIp = null,
          countryCode = COUNTRY_CODE,
          loadBalancerCountryData = COUNTRY_CODE,
          forwardedIpRaw = null
        ),
        ipData = null,
        appVersion = appVersion,
        market = "market",
      )
    }

    assertEquals(0, logEntriesListAppender.list.size)

    verifyBlocking(userPersistenceService, times(0)) { updateUserAppVersion(USER_ID, appVersion) }
    verifyBlocking(userPersistenceService, times(0)) { logUserAppVersionChange(USER_ID, appVersion) }
    verifyBlocking(messageBus, never()) { publish(AppVersionChanged(USER_ID, appVersion, 5)) }
  }

  @ParameterizedTest
  @CsvSource(
    "allowed,allowed,true,IOS",
    "ALLOWED,not_allowed,false,IOS",
    "not_allowed,ALLOWED,false,IOS",
    "not_allowed,not_allowed,false,IOS",
    "allowed,allowed,true,IOS_WEB",
    "ALLOWED,not_allowed,false,IOS_WEB",
    "not_allowed,ALLOWED,false,IOS_WEB",
    "not_allowed,not_allowed,false,IOS_WEB",
  )
  fun `SHOULD consider ip and gps location ON country check in onUserSpecificRequest WHEN appPlatform is iOS`(
    ipCountryCode: String,
    gpsCountryCode: String,
    expected: String,
    platform: AppPlatform
  ) {
    userPersistenceService.mock({ findGpsLocationCountry(USER_ID) }, gpsCountryCode)
    marketService.mock({ getAllowedCountries() }, setOf("ALLOWED"))
    val userRequestMetadata = UserRequestMetadata(
      ip = IP,
      forwardedIp = null,
      countryCode = COUNTRY_CODE,
      loadBalancerCountryData = COUNTRY_CODE,
      forwardedIpRaw = null
    )
    ipService.mock(
      { extractIpData(userRequestMetadata) },
      IpData.Limited(ip = IP, countryCode = ipCountryCode)
    )

    if (expected.toBoolean()) {
      runBlocking {
        userService.onUserSpecificRequest(
          userId = USER_ID,
          userRequestMetadata = userRequestMetadata,
          appVersion = appVersionIos.copy(platform = platform),
          market = "market",
        )
      }
    } else {
      assertThrows<CountryIsNotAllowedException> {
        runBlocking {
          userService.onUserSpecificRequest(
            userId = USER_ID,
            userRequestMetadata = userRequestMetadata,
            appVersion = appVersionIos.copy(platform = platform),
            market = "market",
          )
        }
      }
    }
  }

  @Test
  fun `SHOULD check country ON onUserSpecificRequest WHEN tier3 forbidden countries is empty`() {
    val userRequestMetadata = UserRequestMetadata(
      ip = IP,
      forwardedIp = null,
      countryCode = COUNTRY_CODE,
      loadBalancerCountryData = COUNTRY_CODE,
      forwardedIpRaw = null
    )
    ipService.mock(
      { extractIpData(userRequestMetadata) },
      IpData.Limited(ip = IP, countryCode = "DE")
    )
    marketService.mock({ getForbiddenCountries() }, emptySet())

    runBlocking {
      userService.onUserSpecificRequest(
        userId = USER_ID,
        userRequestMetadata = userRequestMetadata,
        appVersion = appVersionAndroid,
        market = "market",
      )
    }

    verifyBlocking(marketService) { getForbiddenCountries() }
  }

  @Test
  fun `SHOULD check country ON onUserSpecificRequest WHEN NOT country in tier3 forbidden countries`() {
    val userRequestMetadata = UserRequestMetadata(
      ip = IP,
      forwardedIp = null,
      countryCode = COUNTRY_CODE,
      loadBalancerCountryData = COUNTRY_CODE,
      forwardedIpRaw = null
    )
    ipService.mock(
      { extractIpData(userRequestMetadata) },
      IpData.Limited(ip = IP, countryCode = "DE")
    )
    marketService.mock({ getForbiddenCountries() }, setOf("US"))

    runBlocking {
      userService.onUserSpecificRequest(
        userId = USER_ID,
        userRequestMetadata = userRequestMetadata,
        appVersion = appVersionAndroid,
        market = "market",
      )
    }

    verifyBlocking(marketService) { getForbiddenCountries() }
  }

  @Test
  fun `SHOULD throw CountryIsNotAllowedException exception ON onUserSpecificRequest WHEN tier3 forbidden countries configured`() {
    val userRequestMetadata = UserRequestMetadata(
      ip = IP,
      forwardedIp = null,
      countryCode = COUNTRY_CODE,
      loadBalancerCountryData = COUNTRY_CODE,
      forwardedIpRaw = null
    )
    ipService.mock(
      { extractIpData(userRequestMetadata) },
      IpData.Limited(ip = IP, countryCode = "DE")
    )
    marketService.mock({ getForbiddenCountries() }, setOf("DE"))

    testScope.runTest {
      assertThrows<CountryIsNotAllowedException> {
        runBlocking {
          userService.onUserSpecificRequest(
            userId = USER_ID,
            userRequestMetadata = userRequestMetadata,
            appVersion = appVersionAndroid,
            market = "market",
          )
        }
      }
      advanceUntilIdle()
    }

    verifyBlocking(bigQueryEventPublisher) {
      publish(
        Tier3UserAccessPreventedDto(USER_ID, "DE", now)
      )
    }
  }

  @ParameterizedTest
  @CsvSource(
    "allowed,true",
    "not_allowed,false",
  )
  fun `SHOULD ignore gps location ON country check in onUserSpecificRequest WHEN appPlatform is iOS and gps location is null`(
    ipCountryCode: String,
    expected: String
  ) {
    userPersistenceService.mock({ findGpsLocationCountry(USER_ID) }, null)
    marketService.mock({ getAllowedCountries() }, setOf("ALLOWED"))
    val userRequestMetadata = UserRequestMetadata(
      ip = IP,
      forwardedIp = null,
      countryCode = COUNTRY_CODE,
      loadBalancerCountryData = COUNTRY_CODE,
      forwardedIpRaw = null
    )
    ipService.mock(
      { extractIpData(userRequestMetadata) },
      IpData.Limited(ip = IP, countryCode = ipCountryCode)
    )

    if (expected.toBoolean()) {
      runBlocking {
        userService.onUserSpecificRequest(
          userId = USER_ID,
          userRequestMetadata = userRequestMetadata,
          appVersion = appVersionIos,
          market = "market",
        )
      }
    } else {
      assertThrows<CountryIsNotAllowedException> {
        runBlocking {
          userService.onUserSpecificRequest(
            userId = USER_ID,
            userRequestMetadata = userRequestMetadata,
            appVersion = appVersionIos,
            market = "market",
          )
        }
      }
    }
  }

  @Test
  fun `SHOULD pass country check ON country check in onUserSpecificRequest WHEN appPlatform is iOS country is not allowed but user whitelisted`() {
    userPersistenceService.mock({ isUserWhitelisted(USER_ID) }, true)
    userPersistenceService.mock({ findGpsLocationCountry(USER_ID) }, "BAD_COUNTRY")
    marketService.mock({ getAllowedCountries() }, setOf("GOOD_COUNTRY"))
    val userRequestMetadata = UserRequestMetadata(
      ip = IP,
      forwardedIp = null,
      countryCode = "BAD_COUNTRY",
      loadBalancerCountryData = "BAD_COUNTRY",
      forwardedIpRaw = null
    )
    ipService.mock(
      { extractIpData(userRequestMetadata) },
      IpData.Limited(ip = IP, countryCode = "BAD_COUNTRY")
    )

    //no exceptions
    runBlocking {
      userService.onUserSpecificRequest(
        userId = USER_ID,
        userRequestMetadata = userRequestMetadata,
        appVersion = appVersionIos,
        market = "market",
      )
    }
  }

  @Test
  fun `SHOULD update appVersion and call fs_blockUserOnPlatformChangeDetected ON user specific request WHEN platform and versions are unequal`() {
    userPersistenceService.mock({ loadUserAppVersionAndLastActivityDay(USER_ID) }, emptyAppVersion to LocalDate.now())
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    testScope.runTest {
      userService.onUserSpecificRequest(
        userId = USER_ID,
        userRequestMetadata = UserRequestMetadata(
          ip = IP,
          forwardedIp = null,
          countryCode = COUNTRY_CODE,
          loadBalancerCountryData = COUNTRY_CODE,
          forwardedIpRaw = null
        ),
        ipData = null,
        appVersion = appVersionIos,
        market = "market",
      )
    }

    assertThat(logEntriesListAppender.list.map { it.toString() })
      .contains("[WARN] Application platform change detected. UserId:$USER_ID, previousPlatform:ANDROID, newPlatform:IOS")

    verifyBlocking(userPersistenceService, times(1)) { updateUserAppVersion(USER_ID, appVersionIos) }
    verifyBlocking(userPersistenceService, times(1)) { logUserAppVersionChange(USER_ID, appVersionIos) }
    verifyBlocking(messageBus, never()) { publish(AppVersionChanged(USER_ID, appVersionIos, 0)) }
    verifyBlocking(messageBus, times(1)) { publish(PlatformChanged(USER_ID)) }
  }

  @Test
  fun `SHOULD update appVersion and call fs_onAppVersionChange ON user specific request WHEN platform are equal and versions are unequal`() {
    userDataCache.mock({ getUserData(USER_ID) }, userDtoStub.copy(lastActiveAtDay = LocalDate.now(), appVersion = 0))
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    val appVersion = AppVersionDto(ANDROID, appVersionAndroid.version)
    testScope.runTest {
      userService.onUserSpecificRequest(
        userId = USER_ID,
        userRequestMetadata = UserRequestMetadata(
          ip = IP,
          forwardedIp = null,
          countryCode = COUNTRY_CODE,
          loadBalancerCountryData = COUNTRY_CODE,
          forwardedIpRaw = null
        ),
        ipData = null,
        appVersion = appVersion,
        market = "market",
      )
    }

    assertEquals(0, logEntriesListAppender.list.size)

    verifyBlocking(userPersistenceService, times(1)) { updateUserAppVersion(USER_ID, appVersion) }
    verifyBlocking(userPersistenceService, times(1)) { logUserAppVersionChange(USER_ID, appVersion) }
    verifyBlocking(messageBus, times(1)) { publish(AppVersionChanged(USER_ID, appVersion, 0)) }
    verifyBlocking(messageBus, never()) { publish(PlatformChanged(USER_ID)) }

  }

  @Test
  fun `SHOULD update appVersion and call fs_blockUserOnPlatformChangeDetected ON user specific request WHEN platform are unequal and versions are equal`() {
    userDataCache.mock({ getUserData(USER_ID) }, userDtoStub.copy(lastActiveAtDay = LocalDate.now(), appVersion = 105))
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    testScope.runTest {
      userService.onUserSpecificRequest(
        userId = USER_ID,
        userRequestMetadata = UserRequestMetadata(
          ip = IP,
          forwardedIp = null,
          countryCode = COUNTRY_CODE,
          loadBalancerCountryData = COUNTRY_CODE,
          forwardedIpRaw = null
        ),
        ipData = null,
        appVersion = appVersionIos,
        market = "market",
      )
    }

    assertEquals(
      "[WARN] Application platform change detected. UserId:$USER_ID, previousPlatform:ANDROID, newPlatform:IOS",
      logEntriesListAppender.list[0].toString()
    )
    verifyBlocking(userPersistenceService, times(1)) { updateUserAppVersion(USER_ID, appVersionIos) }
    verifyBlocking(userPersistenceService, times(1)) { logUserAppVersionChange(USER_ID, appVersionIos) }
    verifyBlocking(messageBus, never()) { publish(AppVersionChanged(USER_ID, appVersionIos, 58)) }
    verifyBlocking(messageBus, times(1)) { publish(PlatformChanged(USER_ID)) }
  }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD throw IncompatibleVersionException ON user specific request WHEN versions is low`(appPlatform: AppPlatform) {
    val appVersion = AppVersionDto(appPlatform, getHardUpdateVersion(appPlatform) - 1)
    assertThrows<IncompatibleVersionException> {
      testScope.runTest {
        userService.onUserSpecificRequest(
          userId = USER_ID,
          userRequestMetadata = UserRequestMetadata(
            ip = IP,
            forwardedIp = null,
            countryCode = COUNTRY_CODE,
            loadBalancerCountryData = COUNTRY_CODE,
            forwardedIpRaw = null
          ),
          ipData = null,
          appVersion = appVersion,
          market = "market",
        )
      }
    }.let { exception ->
      assertThat(exception.internalMessage).isEqualTo(
        "Invalid app version. Expected: ${getHardUpdateVersion(appPlatform)}, actual: ${getHardUpdateVersion(appPlatform) - 1}"
      )
      assertThat(exception.externalMessage).isEqualTo("Invalid app version")
    }
  }

  @ParameterizedTest
  @ValueSource(ints = [1, -1])
  fun `SHOULD update app version ON user specific request WHEN old app version is differ from new app version`(versionShift: Int) {
    userDataCache.mock(
      { getUserData(USER_ID) },
      userDtoStub.copy(lastActiveAtDay = LocalDate.now(), appVersion = appVersionAndroid.version + versionShift)
    )
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    testScope.runTest { onUserSpecificRequestCallWithData() }

    verifyBlocking(userPersistenceService) { updateUserAppVersion(USER_ID, appVersionAndroid) }
  }

  @Test
  fun `SHOULD NOT update app version ON user specific request WHEN NOT old app version is lower than new app version`() {
    userPersistenceService.mock({ loadUserAppVersionAndLastActivityDay(USER_ID) }, (appVersionAndroid to LocalDate.now()))
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    testScope.runTest { onUserSpecificRequestCallWithData() }

    verifyBlocking(userPersistenceService, never()) { updateUserAppVersion(USER_ID, appVersionAndroid) }
    verifyBlocking(userPersistenceService, never()) { banUser(eq(USER_ID), any(), any()) }
  }

  @Test
  fun `SHOULD log app version change ON user specific request WHEN app version changed`() {
    userDataCache.mock({ getUserData(USER_ID) }, userDtoStub.copy(lastActiveAtDay = LocalDate.now(), appVersion = appVersionAndroid.version - 1))
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    testScope.runTest { onUserSpecificRequestCallWithData() }

    verifyBlocking(userPersistenceService) { logUserAppVersionChange(USER_ID, appVersionAndroid) }
  }

  @Test
  fun `SHOULD NOT log app version change ON user specific request WHEN app version did not change`() {
    userDataCache.mock({ getUserData(USER_ID) }, userDtoStub.copy(lastActiveAtDay = LocalDate.now()))
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    testScope.runTest { onUserSpecificRequestCallWithData() }

    verifyBlocking(userPersistenceService, never()) { logUserAppVersionChange(USER_ID, appVersionAndroid) }
  }

  @Test
  fun `SHOULD call fs_onAppVersionChange ON user specific request WHEN old app version higher than new app version`() {
    userDataCache.mock({ getUserData(USER_ID) }, userDtoStub.copy(lastActiveAtDay = LocalDate.now(), appVersion = appVersionAndroid.version + 1))
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    testScope.runTest { onUserSpecificRequestCallWithData() }

    verifyBlocking(messageBus) { publish(AppVersionChanged(USER_ID, appVersionAndroid, appVersionAndroid.version + 1)) }
  }

  @Test
  fun `SHOULD call fs_onAppVersionChange ON user specific request WHEN old app version lower than new app version`() {
    userDataCache.mock({ getUserData(USER_ID) }, userDtoStub.copy(lastActiveAtDay = LocalDate.now(), appVersion = appVersionAndroid.version - 1))
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    testScope.runTest { onUserSpecificRequestCallWithData() }

    verifyBlocking(messageBus) { publish(AppVersionChanged(USER_ID, appVersionAndroid, appVersionAndroid.version - 1)) }
  }

  @Test
  fun `SHOULD NOT call fs_onAppVersionChange ON user specific request WHEN app version is not changed`() {
    userPersistenceService.mock({ loadUserAppVersionAndLastActivityDay(USER_ID) }, (appVersionAndroid to LocalDate.now()))
    userPersistenceService.mock({ saveUserIpReturnNew(USER_ID, IP, COUNTRY_CODE) }, false)

    testScope.runTest { onUserSpecificRequestCallWithData() }

    verifyBlocking(messageBus, never()) { publish(AppVersionChanged(USER_ID, appVersionAndroid, appVersionAndroid.version)) }
  }

  @Test
  fun `SHOULD create user and return user data ON createUser`() = testScope.runTest {
    userPersistenceService.mock({ createUser(COUNTRY_CODE, createUserDataStub) }, USER_ID)

    val actual = userService.createUser(COUNTRY_CODE, createUserDataStub)


    assertThat(actual).isEqualTo(USER_ID)
    verifyBlocking(messageBus) {
      publish(
        UserCreatedEventDto(
          userId = USER_ID,
          market = "test-market",
          createdAt = now,
          countryCode = COUNTRY_CODE,
          platform = ANDROID
        )
      )
    }
    verifyBlocking(messageBus) {
      publish(
        userCreatedEvent {
          this.userId = USER_ID
        }
      )
    }
    verifyBlocking(messageBus) {
      publish(
        CrmMessage(
          CrmPayload.UserCreationDataDto(
            userId = USER_ID,
            market = "test-market",
            createdAt = now,
            countryCode = COUNTRY_CODE,
            platform = ANDROID
          )
        )
      )
    }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        UserCreatedDeviceSpecsBqEventDto(
          userId = USER_ID,
          createdAt = now,
          osVersion = "42",
          modelName = "Pixel 4 XL",
          ramSize = 6144,
          fontScale = BigDecimal.valueOf(12.121),
          density = 10,
          densityScaleFactor = BigDecimal.valueOf(15.151),
          appPlatform = ANDROID.name
        )
      )
    }
  }

  @Test
  fun `SHOULD create user and return user data ON createUser AND empty device specs`() = testScope.runTest {
    val createUserData = createUserDataStub.copy(
      userRequestDto = CreateUserRequestDto(
        networkCountry = "us",
        networkOperatorName = "verizon",
        simCountry = "ca",
        simOperatorName = "Verizon Canada",
        deviceLocale = "en",
        deviceLanguageTag = "en-US",
        installedFromStore = true,
        userPublicKey = "some user public key",
        timeZone = "Europe/Berlin",
        jailBreak = false,
        deviceSpecification = null
      )
    )
    userPersistenceService.mock({ createUser(COUNTRY_CODE, createUserData) }, USER_ID)

    val actual = userService.createUser(COUNTRY_CODE, createUserData)


    assertThat(actual).isEqualTo(USER_ID)
    verifyBlocking(messageBus) {
      publish(
        UserCreatedEventDto(
          userId = USER_ID,
          market = "test-market",
          createdAt = now,
          countryCode = COUNTRY_CODE,
          platform = ANDROID
        )
      )
    }
    verifyBlocking(messageBus) {
      publish(
        CrmMessage(
          CrmPayload.UserCreationDataDto(
            userId = USER_ID,
            market = "test-market",
            createdAt = now,
            countryCode = COUNTRY_CODE,
            platform = ANDROID
          )
        )
      )
    }

    org.mockito.kotlin.verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD create user, return userId and ON createUser`() = testScope.runTest {
    userPersistenceService.mock({ createUser(COUNTRY_CODE, createUserDataStub) }, USER_ID)

    val actual = userService.createUser(COUNTRY_CODE, createUserDataStub)

    assertThat(actual).isEqualTo(USER_ID)
    verifyBlocking(messageBus) {
      publish(
        UserCreatedEventDto(
          userId = USER_ID,
          market = "test-market",
          createdAt = now,
          countryCode = COUNTRY_CODE,
          platform = ANDROID
        )
      )
    }
    verifyBlocking(messageBus) {
      publish(
        CrmMessage(
          CrmPayload.UserCreationDataDto(
            userId = USER_ID,
            market = "test-market",
            createdAt = now,
            countryCode = COUNTRY_CODE,
            platform = ANDROID
          )
        )
      )
    }
  }

  @Test
  fun `SHOULD load coin goal user ON loadCoinGoalUser`() = testScope.runTest {
    userPersistenceService.mock({ loadCoinGoalUser(USER_ID) }, user)

    val actual = userService.loadCoinGoalUser(USER_ID)

    assertThat(actual).isEqualTo(user)

    verifyBlocking(userPersistenceService) { loadCoinGoalUser(USER_ID) }
    verifyNoMoreInteractions(userPersistenceService)
  }

  @Test
  fun `SHOULD NOT trigger effects ON onNewUserCreated WHEN request data is empty`() {
    userService.onNewUserCreated(
      userId = USER_ID,
      createUserData = CreateUserData(
        appVersion = appVersionAndroid,
        userRequestMetadata = UserRequestMetadata(null, null, null, null, null),
        userRequestDto = null,
        userRequestDtoSignature = null,
        definedExperimentVariations = null,
      )
    )

    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD trigger effects ON onNewUserCreated`() {
    val createUserData = CreateUserData(
      appVersion = appVersionAndroid,
      userRequestMetadata = UserRequestMetadata(null, null, null, null, null),
      userRequestDto = additionalData,
      userRequestDtoSignature = null,
      definedExperimentVariations = null,
    )

    userService.onNewUserCreated(
      userId = USER_ID,
      createUserData = createUserData
    )

    verifyBlocking(messageBus) { publishAsync(NewUserFraudEffect(USER_ID, createUserData)) }
    verifyBlocking(messageBus) { publishAsync(AfterXMinUserCreationEventEffect(USER_ID)) }
    verifyBlocking(messageBus) { publishAsync(InstallGamesWhenNoCoinsEffect(USER_ID)) }
  }

  @Test
  fun `SHOULD whitelist userId ON updateGoogleAdId WHEN gaid is whitelisted`() {
    trackingService.mock({ isGoogleAdIdWhitelisted(GAID) }, true)
    trackingService.mock({ isValidGoogleAdId(GAID) }, true)

    testScope.runTest {
      userService.updateGoogleAdId(USER_ID, GAID, AppVersionDto(ANDROID, 0))
    }

    verifyBlocking(userPersistenceService) { whitelistUser(USER_ID) }
    verifyBlocking(trackingService) { updateGoogleAdId(USER_ID, GAID) }
  }


  @Test
  fun `SHOULD trigger fraudScoreService_onUsingVPN AND mark user as connected via vpn ON onUserSanityCheck WHEN check result is IS_USING_VPN`() {
    testScope.runTest {
      userService.onUserSanityCheck(USER_ID, SanityValue.IS_USING_VPN)
    }
    verifyBlocking(userPersistenceService) { markUserAsConnectedViaVpn(USER_ID) }
    verifyBlocking(messageBus) { publishAsync(VpnUsage(USER_ID)) }
  }

  @Test
  fun `SHOULD NOT trigger fraudScoreService_onUsingVPN ON onUserSanityCheck WHEN check result is NOT IS_USING_VPN`() {
    testScope.runTest {
      userService.onUserSanityCheck(USER_ID, SanityValue.IS_SANE)
    }
    verifyNoInteractions(userPersistenceService)
    verifyNoInteractions(messageBus)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD check userExists ON userExists`(existStatus: Boolean) {
    userPersistenceService.mock({ userExists(USER_ID) }, existStatus)

    testScope.runTest {
      val actual = userService.userExists(USER_ID)

      assertThat(actual).isEqualTo(existStatus)
    }
  }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD update device token AND emit event ON updateDeviceToken`(appPlatform: AppPlatform) {
    val deviceToken = "deviceToken"

    testScope.runTest {
      userService.updateDeviceToken(USER_ID, deviceToken, appPlatform)
    }

    verifyBlocking(userPersistenceService) {
      updateDeviceToken(USER_ID, deviceToken)
    }
    verifyBlocking(messageBus) {
      publish(DeviceTokenUpdatedEventDto(userId = USER_ID, deviceToken = deviceToken, appPlatform = appPlatform))
    }
  }

  @Test
  fun `SHOULD ignored BLACKLISTED device token AND emit event ON updateDeviceToken`() {
    val deviceToken = "BLACKLISTED"

    testScope.runTest { userService.updateDeviceToken(USER_ID, deviceToken, ANDROID) }

    verifyBlocking(userPersistenceService) { updateDeviceToken(USER_ID, "") }
    verifyBlocking(messageBus) {
      publish(DeviceTokenUpdatedEventDto(userId = USER_ID, deviceToken = "", appPlatform = ANDROID))
    }
  }

  @Test
  fun `SHOULD load user ON getUser`() {
    testScope.runTest {
      userService.getUser(USER_ID)
    }

    verifyBlocking(userDataCache) {
      getUserData(USER_ID, false)
    }
  }

  @Test
  fun `SHOULD load user ON getUser WHEN includingDeleted is true`() {
    testScope.runTest {
      userService.getUser(USER_ID, true)
    }

    verifyBlocking(userDataCache) {
      getUserData(USER_ID, true)
    }
  }

  @Test
  fun `SHOULD fetch user ids ON fetchAllUserIds`() {
    testScope.runTest {
      userService.fetchAllUserIds("googleAdId")
    }

    verifyBlocking(userPersistenceService) {
      fetchAllUserIds("googleAdId")
    }
  }

  @Test
  fun `SHOULD mark user as delete ON markUserAsDeleted`() {
    testScope.runTest {
      userService.markUserAsDeleted(USER_ID)
    }

    verifyBlocking(userPersistenceService) {
      markUserAsDeleted(USER_ID)
    }
  }

  @Test
  fun `SHOULD obfuscate user data ON obfuscateUserDataPersonals`() = testScope.runTest {
    userPersistenceService.mock({ obfuscateUserPersonals(USER_ID) }, 5)
    val actual = userService.obfuscateUserDataPersonals(USER_ID)
    assertThat(actual).isEqualTo(5)
  }

  @Test
  fun `SHOULD obfuscate user gaid tracking data ON obfuscateUserGaidTrackingPersonals`() = testScope.runTest {
    trackingService.mock({ obfuscateUserGaidTrackingPersonals(USER_ID) }, 5)
    val actual = userService.obfuscateUserGaidTrackingPersonals(USER_ID)
    assertThat(actual).isEqualTo(5)
  }

  @Test
  fun `SHOULD return user latest activity time ON getLatestActivityTime WHEN there is current revenue`() = testScope.runTest {
    val time = Instant.now().minusSeconds(777)

    rewardingFacade.mock({ getLatestRevenueTime(USER_ID) }, time)

    val actual = userService.getLatestActivityTime(USER_ID)

    assertThat(actual).isEqualTo(time)
  }

  @Test
  fun `SHOULD return user latest activity time ON getLatestActivityTime WHEN no current revenue`() = testScope.runTest {
    val time = Instant.now().minusSeconds(77).truncatedTo(ChronoUnit.SECONDS)
    val dayStart = Instant.now().truncatedTo(ChronoUnit.DAYS)

    rewardingFacade.mock({ getLatestRevenueTime(USER_ID) }, null)
    userPersistenceService.mock({ loadUserAppVersionAndLastActivityDay(USER_ID) }, Pair(AppVersionDto(ANDROID, 42), time.localUtcDate()))

    val actual = userService.getLatestActivityTime(USER_ID)

    assertThat(actual).isEqualTo(dayStart)
  }

  @Test
  fun `SHOULD return user latest app activity time ON getLatestAppActivityTime`() = testScope.runTest {
    val time = Instant.now().minusSeconds(77).truncatedTo(ChronoUnit.SECONDS)
    val dayStart = Instant.now().truncatedTo(ChronoUnit.DAYS)

    userPersistenceService.mock({ loadUserAppVersionAndLastActivityDay(USER_ID) }, Pair(AppVersionDto(ANDROID, 42), time.localUtcDate()))

    val actual = userService.getLatestAppActivityTime(USER_ID)

    assertThat(actual).isEqualTo(dayStart)
  }

  @Test
  fun `SHOULD return user app version ON getAppVersion`() = testScope.runTest {
    val appVersion = AppVersionDto(platform = ANDROID, version = 42)
    userPersistenceService.mock({ loadUserAppVersionAndLastActivityDay(USER_ID) }, appVersion to now)

    userService.getAppVersion(USER_ID).let { actual ->
      assertThat(actual).isEqualTo(appVersion)
    }
  }

  @Test
  fun `SHOULd trigger persistence trackNotification on trackNotification`() {
    testScope.runTest { userService.trackNotification(USER_ID, NotificationType.THIRTY_MINUTES_TO_CASHOUT) }

    verifyBlocking(userPersistenceService) { trackUserNotification(USER_ID, NotificationType.THIRTY_MINUTES_TO_CASHOUT, now) }
  }

  @ParameterizedTest
  @ValueSource(longs = [5L, 25L, -1L])
  fun `SHOULD check if user has no recent notifications ON noRecentNotifications`(hoursPassed: Long) = testScope.runTest {
    if (hoursPassed == -1L)
      userPersistenceService.mock({ getLastNotificationDateByType(USER_ID, NotificationType.THIRTY_MINUTES_TO_CASHOUT) }, null)
    else
      userPersistenceService.mock(
        { getLastNotificationDateByType(USER_ID, NotificationType.THIRTY_MINUTES_TO_CASHOUT) },
        Instant.now().minus(hoursPassed, ChronoUnit.HOURS)
      )

    val actual = userService.noRecentNotifications(USER_ID, NotificationType.THIRTY_MINUTES_TO_CASHOUT)

    when (hoursPassed) {
      5L -> assertThat(actual).isFalse()
      25L -> assertThat(actual).isTrue()
      -1L -> assertThat(actual).isTrue()
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["has", "hasn't"])
  fun `SHOULD check if user has previous notifications On hasPreviousNotification`(strings: String) = testScope.runTest {
    when (strings) {
      "has" -> userPersistenceService.mock(
        { getLastNotificationDateByType(USER_ID, NotificationType.RATING_PROMPT) },
        Instant.now()
      )

      "hasn't" -> userPersistenceService.mock(
        { getLastNotificationDateByType(USER_ID, NotificationType.RATING_PROMPT) },
        null
      )
    }

    val actual = userService.hasPreviousNotification(USER_ID, NotificationType.RATING_PROMPT)

    when (strings) {
      "has" -> assertTrue(actual)
      "hasn't" -> assertFalse(actual)
    }
  }

  @Test
  fun `SHOULD use provided date ON noRecentNotifications for checking recent notifications`() = testScope.runTest {
    userPersistenceService.mock(
      { getLastNotificationDateByType(USER_ID, NotificationType.THIRTY_MINUTES_TO_CASHOUT) },
      Instant.now().minus(22, ChronoUnit.HOURS)
    )
    val actual =
      userService.noRecentNotifications(USER_ID, NotificationType.THIRTY_MINUTES_TO_CASHOUT, now.plus(3, ChronoUnit.HOURS))


    assertThat(actual).isTrue()
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD call updating methods ON updateValidGoogleAdId`(userWhitelisted: Boolean) {
    trackingService.mock({ isGoogleAdIdWhitelisted(GAID) }, userWhitelisted)

    testScope.runTest { userService.updateValidGoogleAdId(USER_ID, GAID, appVersionAndroid.version) }

    verifyBlocking(trackingService) { isGoogleAdIdWhitelisted(GAID) }
    verifyBlocking(userPersistenceService, times(if (userWhitelisted) 1 else 0)) { whitelistUser(USER_ID) }

    verifyBlocking(trackingService) { updateGoogleAdId(USER_ID, GAID) }
    verifyBlocking(messageBus) { publishAsync(NewGoogleAdId(USER_ID, GAID, appVersionAndroid.version)) }
    verifyBlocking(messageBus) {
      publish(
        TrackingDataUpdatedEventDto(
          userId = USER_ID,
          trackingId = GAID,
          trackingType = IDFA.name,
          appPlatform = ANDROID.name,
          platform = ANDROID.name,
          market = "test-market",
          createdAt = now
        )
      )
    }
  }

  @Test
  fun `SHOULD fetch external ids ON fetchExternalIds`() = testScope.runTest {
    val externalIds = UserExternalIds(USER_ID, GAID, IDFA_VALUE, ADJUST_ID, FIREBASE_APP_ID, generateTrackingData())
    userDataCache.mock({ fetchExternalIds(USER_ID) }, externalIds)

    userService.fetchExternalIds(USER_ID)
      .also { assertThat(it).isEqualTo(externalIds) }

    verifyBlocking(userDataCache) { fetchExternalIds(USER_ID) }
  }

  @ParameterizedTest
  @ValueSource(strings = ["have some attempts left", "no attempts left"])
  fun `SHOULD return true if we were able to take off one attempt and false otherwise`(option: String) = testScope.runTest {
    (if (option == "have some attempts left") 1 else 0)
      .also { userPersistenceService.mock({ reduceAmountOfNotificationsOnEmptyCPByOneReturnRowsUpdated(USER_ID) }, it) }

    userService.shouldNotifyOnEmptyCPNotification(USER_ID).let {
      if (option == "have some attempts left") assertThat(it).isTrue() else assertThat(it).isFalse()
    }

    verifyBlocking(userPersistenceService) { reduceAmountOfNotificationsOnEmptyCPByOneReturnRowsUpdated(USER_ID) }
  }

  @Test
  fun `SHOULD trigger updateUserOnEmptyCPNotificationTable ON scheduleOnEmptyCPEndNotifications`() {
    testScope.runTest { userService.scheduleOnEmptyCPEndNotifications(USER_ID, 250) }

    verifyBlocking(userPersistenceService) { updateUserOnEmptyCPNotificationTable(USER_ID, 250) }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD calculate useGdpr = true ON getGdprState`(booleans: Boolean) = testScope.runTest {
    userPersistenceService.mock({ getUserCountryCode(USER_ID) }, "SOME")
    marketService.mock({ isGdprAppliesToCountry("SOME") }, booleans)

    val actual = userService.getGdprState(USER_ID)

    assertThat(actual).isEqualTo(GdprStateDto(booleans))
  }

  @Test
  fun `SHOULD update user trackingData AND remove LAT popup ON updateTrackingData`() {
    val trackingData = generateTrackingData()
    trackingService.mock({ isValidTrackingId(trackingData.id) }, true)

    testScope.runTest {
      userService.updateTrackingData(USER_ID, trackingData, appVersion = appVersionIos)
    }

    verifyBlocking(trackingService) { changeUserCurrentTrackingData(USER_ID, trackingData) }
    verifyBlocking(messageBus) { publishAsync(NewTrackingData(USER_ID, trackingData)) }
    verifyBlocking(messageBus) {
      publish(
        TrackingDataUpdatedEventDto(
          userId = USER_ID,
          trackingId = trackingData.id,
          trackingType = trackingData.type.name,
          appPlatform = trackingData.platform.name,
          platform = trackingData.platform.name,
          market = "test-market",
          createdAt = now
        )
      )
    }
    verifyBlocking(messageBus) { publishAsync(DeletePopupMessageEffect(USER_ID, LIMITED_AD_TRACKING)) }
  }

  @Test
  fun `SHOULD do nothing ON updateTrackingData WHEN new trackingData equals current trackingData`() {
    val trackingData = generateTrackingData()
    userPersistenceService.mock({ fetchTrackingData(USER_ID) }, trackingData)

    testScope.runTest {
      userService.updateTrackingData(USER_ID, trackingData, appVersion = appVersionIos)
    }

    verifyNoInteractions(trackingService)
    verifyBlocking(messageBus, never()) { publishAsync(NewTrackingData(USER_ID, trackingData)) }
    verifyBlocking(messageBus, never()) { publishAsync(DeletePopupMessageEffect(USER_ID, LIMITED_AD_TRACKING)) }
    verifyNoInteractions(messageBus)
  }

  @ParameterizedTest
  @ValueSource(strings = [TRACKING_ID_STUB, TRACKING_ID_STUB_SHORT])
  fun `SHOULD set LAT and add LAT popup ON updateTrackingData when provided id is a stub`(trackingId: String) {
    val trackingData = generateTrackingData(trackingId = trackingId)

    testScope.runTest {
      userService.updateTrackingData(USER_ID, trackingData, appVersion = appVersionIos)
    }

    verifyBlocking(userPersistenceService) { setLimitedTrackingUser(USER_ID) }
    verifyBlocking(messageBus) { publishAsync(CreateLatPopupMessageEffect(USER_ID, EN_LOCALE)) }
    verifyNoMoreInteractions(messageBus)
  }

  @Test
  fun `SHOULD whitelist userId ON updateTrackingData WHEN trackingData is whitelisted`() {
    val trackingData = generateTrackingData()
    trackingService.mock({ isTrackingDataWhitelisted(trackingData) }, true)
    trackingService.mock({ isValidTrackingId(trackingData.id) }, true)

    testScope.runTest {
      userService.updateTrackingData(USER_ID, trackingData, appVersion = appVersionIos)
    }

    verifyBlocking(userPersistenceService) { whitelistUser(USER_ID) }
    verifyBlocking(trackingService) { changeUserCurrentTrackingData(USER_ID, trackingData) }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD call updating methods ON updateValidTrackingData`(userWhitelisted: Boolean) {
    val trackingData = generateTrackingData()
    trackingService.mock({ isTrackingDataWhitelisted(trackingData) }, userWhitelisted)

    testScope.runTest { userService.updateValidTrackingData(USER_ID, trackingData) }

    verifyBlocking(trackingService) { isTrackingDataWhitelisted(trackingData) }
    verifyBlocking(userPersistenceService, times(if (userWhitelisted) 1 else 0)) { whitelistUser(USER_ID) }

    verifyBlocking(trackingService) { changeUserCurrentTrackingData(USER_ID, trackingData) }
    verifyBlocking(messageBus) { publishAsync(NewTrackingData(USER_ID, trackingData)) }
    verifyBlocking(messageBus) {
      publish(
        TrackingDataUpdatedEventDto(
          userId = USER_ID,
          trackingId = trackingData.id,
          trackingType = trackingData.type.name,
          appPlatform = trackingData.platform.name,
          platform = trackingData.platform.name,
          market = "test-market",
          createdAt = now
        )
      )
    }
  }

  @Test
  fun `SHOULD make correct calls ON whitelistGoogleAdId`() {
    val googleAdId = UUID.randomUUID().toString()
    val userId = UUID.randomUUID().toString()
    userPersistenceService.mock({ fetchUserId(googleAdId) }, userId)

    testScope.runTest { userService.whitelistGoogleAdId(googleAdId) }

    verifyBlocking(userPersistenceService, times(1)) { whitelistUser(userId) }
    verifyBlocking(trackingService, times(1)) { addGoogleAdIdToWhitelist(googleAdId) }
  }

  @Test
  fun `SHOULD make correct calls ON whitelistTrackingData`() {
    val trackingData = generateTrackingData()
    val userId = UUID.randomUUID().toString()
    userPersistenceService.mock({ fetchUserId(trackingData) }, userId)

    testScope.runTest { userService.whitelistTrackingData(trackingData) }

    verifyBlocking(userPersistenceService, times(1)) { whitelistUser(userId) }
    verifyBlocking(trackingService, times(1)) { addTrackingDataToWhitelist(trackingData) }
  }

  @Test
  fun `SHOULD call persistence service ON countUsersWithSameGoogleAdId`() = testScope.runTest {
    userPersistenceService.mock({ countUsersWithSameGoogleAdId(USER_ID) }, 42)

    userService.countUsersWithSameGoogleAdId(USER_ID)
      .let { actual ->
        assertThat(actual).isEqualTo(42)
      }
  }

  @Test
  fun `SHOULD return null ON loadCoinGoal WHEN period does not exist`() = testScope.runTest {
    val userId = UUID.randomUUID().toString()
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriod(userId) }, null)

    userService.loadCoinGoal(userId).let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD load coin goal ON loadCoinGoal WHEN 1st period`() = testScope.runTest {
    val userId = UUID.randomUUID().toString()
    abTestingService.mock({ isEm2Participant(userId) }, false)
    cashoutPeriodsPersistenceService.mock(
      { getCurrentCashoutPeriod(userId) },
      CashoutPeriodDto(
        userId = userId,
        periodStart = now,
        periodEnd = now.plusSeconds(10),
        coinGoal = 42,
        counter = 5,
        noEarningsCounter = 3,
        coinGoalMilestones = emptyList(),
      )
    )

    userService.loadCoinGoal(userId).let { actual ->
      assertThat(actual).isEqualTo(42)
    }
  }

  @Test
  fun `SHOULD load coin goal ON loadCoinGoal WHEN NOT 1st period`() = testScope.runTest {
    val userId = UUID.randomUUID().toString()
    cashoutPeriodsPersistenceService.mock(
      { getCurrentCashoutPeriod(userId) },
      CashoutPeriodDto(
        userId = userId,
        periodStart = now,
        periodEnd = now.plusSeconds(10),
        coinGoal = 42,
        counter = 5,
        noEarningsCounter = 3,
        coinGoalMilestones = emptyList(),
      )
    )
    userService.loadCoinGoal(userId).let { actual ->
      assertThat(actual).isEqualTo(42)
    }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD load user game coins ON loadUserGameCoins`(em2Participant: Boolean) = testScope.runTest {
    val userId = UUID.randomUUID().toString()
    val gamePlayStatuses = listOf(
      GamePlayStatusDto(
        gameId = 1,
        coins = 100,
        playedRecently = true,
        firstPlayedAt = now,
        lastPlayedAt = now,
      ),
      GamePlayStatusDto(
        gameId = 2,
        coins = 100,
        playedRecently = false,
        firstPlayedAt = now,
        lastPlayedAt = now,
      )
    )
    abTestingService.mock({ isEm2Participant(userId) }, em2Participant)
    if (em2Participant) {
      userPersistenceService.mock({ loadRoundedPerGameCoinsForEm2User(userId) }, gamePlayStatuses)
    } else {
      userPersistenceService.mock({ loadPerGameCoinsForUser(userId) }, gamePlayStatuses)
    }

    val actual = userService.loadUserGameCoins(userId)

    assertThat(actual).isEqualTo(gamePlayStatuses.associateBy { it.gameId })
  }

  @ParameterizedTest
  @ValueSource(strings = ["id1", "id2"])
  fun `SHOULD call addTrackingData ON addUserTrackingData`(lastTrackedId: String) {
    val td = generateTrackingData(trackingId = "id1", IDFV, ANDROID)
    trackingService.mock({ getLatestTrackingData(any(), any(), any()) }, TrackingData(lastTrackedId, IDFV, ANDROID))

    testScope.runTest {
      userService.addUserTrackingData(USER_ID, td)
    }

    verifyBlocking(trackingService, times(1)) { getLatestTrackingData(USER_ID, td.type, td.platform) }
    when (lastTrackedId) {
      "id1" -> {
        verifyBlocking(trackingService, never()) { addTrackingData(any(), any()) }
        verifyNoInteractions(messageBus)
      }

      "id2" -> {
        verifyBlocking(trackingService, times(1)) { addTrackingData(USER_ID, td) }
        verifyBlocking(messageBus) {
          publish(
            TrackingDataUpdatedEventDto(
              userId = "user1",
              trackingId = "id1",
              trackingType = "IDFV",
              appPlatform = "ANDROID",
              platform = "ANDROID",
              market = "test-market",
              createdAt = now
            )
          )
        }
      }
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["id1", "id2"])
  fun `SHOULD call addTrackingData ON addUserTrackingData WHEN iOS`(lastTrackedId: String) {
    val td = generateTrackingData(trackingId = "id1", IDFA, IOS)
    trackingService.mock({ getLatestTrackingData(any(), any(), any()) }, TrackingData(lastTrackedId, IDFA, IOS))

    testScope.runTest {
      userService.addUserTrackingData(USER_ID, td)
    }

    verifyBlocking(trackingService, times(1)) { getLatestTrackingData(USER_ID, td.type, td.platform) }
    when (lastTrackedId) {
      "id1" -> {
        verifyBlocking(trackingService, never()) { addTrackingData(any(), any()) }
        verifyNoInteractions(messageBus)
      }

      "id2" -> {
        verifyBlocking(trackingService, times(1)) { addTrackingData(USER_ID, td) }
        verifyBlocking(messageBus) {
          publish(
            TrackingDataUpdatedEventDto(
              userId = "user1",
              trackingId = "id1",
              trackingType = "IDFA",
              appPlatform = "IOS",
              platform = "IOS",
              market = "test-market",
              createdAt = now
            )
          )
        }
      }
    }
  }

  @Test
  fun `SHOULD call markUserAsConnectedViaVpn ON markUserAsConnectedViaVpn`() {
    testScope.runTest {
      userService.updateGpsLocationCountry(USER_ID, "DE")
    }

    verifyBlocking(userPersistenceService, times(1)) { updateGpsLocationCountry(USER_ID, "DE") }
  }

  @Test
  fun `SHOULD fetch user ON fetchUserId`() = testScope.runTest {
    userPersistenceService.mock({ fetchUserId("trackingId") }, "userId")

    userService.fetchUserId("trackingId").let { actual ->
      assertThat(actual).isEqualTo("userId")
    }
  }

  @Test
  fun `SHOULD fetch fetchUserIds ON fetchUserIds`() = testScope.runTest {
    val expected = listOf(
      UserIdAndTrackingId("id1", "userId1"),
      UserIdAndTrackingId("id2", "userId2")
    )

    userPersistenceService.mock({ fetchUserIds(listOf("id1", "id2")) }, expected)

    userService.fetchUserIds(listOf("id1", "id2")).let { actual ->
      assertThat(actual).isEqualTo(expected)
    }
  }


  @Test
  fun `SHOULD return User object ON getCoinGoalUser`() = testScope.runTest {
    userPersistenceService.mock({ loadCoinGoalUser(USER_ID) }, userStub)
    userService.getCoinGoalUser(USER_ID).let { actual ->
      assertThat(actual!!).isEqualTo(userStub)
    }

    verifyBlocking(userPersistenceService) { loadCoinGoalUser(USER_ID) }
  }

  @Test
  fun `SHOULD return null ON getCoinGoalUser WHEN user does not exist`() = testScope.runTest {
    userPersistenceService.throwException({ loadCoinGoalUser(USER_ID) }, UserRecordNotFoundException(USER_ID))
    userService.getCoinGoalUser(USER_ID).let { actual ->
      assertThat(actual).isNull()
    }

    verifyBlocking(userPersistenceService) { loadCoinGoalUser(USER_ID) }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD send email ON requestAccountDeletion WHEN user has cash-out emails`(useEncryption: Boolean) {
    if (useEncryption) {
      encryptionService.mock({ decryptOrEmpty("<EMAIL>") }, "<EMAIL>")
      encryptionService.mock({ decryptOrEmpty("<EMAIL>") }, "<EMAIL>")
    } else {
      encryptionService.mock({ decryptOrEmpty("<EMAIL>") }, "<EMAIL>")
      encryptionService.mock({ decryptOrEmpty("<EMAIL>") }, "<EMAIL>")
    }
    cashoutPersistenceService.mock(
      { loadTransactions(USER_ID) },
      listOf(
        cashoutTransactionStub.copy(userId = USER_ID, encryptedEmail = "<EMAIL>"),
        cashoutTransactionStub.copy(userId = USER_ID, encryptedEmail = "<EMAIL>"),
      )
    )
    userPersistenceService.mock({ fetchGoogleAdId(USER_ID) }, GAID)

    testScope.runTest {
      userService.requestUserDeletion(USER_ID)
    }

    verifyBlocking(userPersistenceService) { requestUserDeletion(userId = USER_ID, hasCashoutData = true) }
    verifyBlocking(mailService) {
      sendMail(
        sender = eq(MailAddress("<EMAIL>", "Reporting")),
        subject = argThat { this.contains("Please delete account $USER_ID") },
        content = argThat {
          this.contains("Please delete my account and all its associated data\nMy user ID $USER_ID\n")
            && this.contains("My googleAdId $GAID\n") && (if (useEncryption) this.contains("<EMAIL>") else this.contains("<EMAIL>"))
        },
        attachments = isNull(),
        anyVararg()
      )
    }
  }

  @Test
  fun `SHOULD delete user automatically and not send email ON requestAccountDeletion WHEN user has no cash-out emails`() {
    cashoutPersistenceService.mock({ loadTransactions(USER_ID) }, emptyList())
    userPersistenceService.mock({ fetchGoogleAdId(USER_ID) }, GAID)

    testScope.runTest {
      userService.requestUserDeletion(USER_ID)
    }

    verifyBlocking(userPersistenceService) { requestUserDeletion(userId = USER_ID, hasCashoutData = false) }
    verifyBlocking(mailService, never()) { sendMail(anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull()) }
    verifyBlocking(personalDataService) { deletePersonalsForUser(userId = USER_ID, googleAdId = GAID) }
  }

  @Test
  fun `SHOULD return null ON getNeedInitializeApplovin WHEN feature flag is disabled`() = testScope.runTest {
    featureFlagsFacade.mock({ boolValue(PlaytimeFeatureFlags.ANDROID_ALLOW_LAZY_APPLOVIN_INITIALIZATION) }, false)
    val actual = userService.useAndroidLazyApplovinInitialization(USER_ID)

    assertEquals(expected = false, actual)

    verifyBlocking(userPersistenceService, never()) { loadUserDevice(USER_ID) }
  }

  @Test
  fun `SHOULD return null ON getNeedInitializeApplovin WHEN user device is unknown`() = testScope.runTest {
    featureFlagsFacade.mock({ boolValue(PlaytimeFeatureFlags.ANDROID_ALLOW_LAZY_APPLOVIN_INITIALIZATION) }, true)
    userPersistenceService.mock({ loadUserDevice(USER_ID) }, null)
    val actual = userService.useAndroidLazyApplovinInitialization(USER_ID)

    assertEquals(expected = false, actual)
  }

  @Test
  fun `SHOULD return false ON getNeedInitializeApplovin WHEN user device has low specs`() = testScope.runTest {
    featureFlagsFacade.mock({ boolValue(PlaytimeFeatureFlags.ANDROID_ALLOW_LAZY_APPLOVIN_INITIALIZATION) }, true)
    userPersistenceService.mock({ loadUserDevice(USER_ID) }, UserDeviceSpecs(ramSize = 1500))
    val actual = userService.useAndroidLazyApplovinInitialization(USER_ID)

    assertEquals(expected = true, actual = actual)
  }

  @Test
  fun `SHOULD return true ON getNeedInitializeApplovin WHEN user device with good specs`() = testScope.runTest {
    featureFlagsFacade.mock({ boolValue(PlaytimeFeatureFlags.ANDROID_ALLOW_LAZY_APPLOVIN_INITIALIZATION) }, true)
    userPersistenceService.mock({ loadUserDevice(USER_ID) }, UserDeviceSpecs(ramSize = 8000))
    val actual = userService.useAndroidLazyApplovinInitialization(USER_ID)

    assertEquals(expected = false, actual = actual)
  }

  @Test
  fun `SHOULD store first video ad reward and emit define user ecpm group effect ON storeVideoReward`() = testScope.runTest {
    val reward = VideoAdReward(revenue = 0.123456789123456)
    userPersistenceService.mock({ storeFirstVideoReward(USER_ID, reward) }, true)

    userService.storeVideoReward(USER_ID, reward)

    verifyBlocking(userPersistenceService) { storeFirstVideoReward(USER_ID, reward) }
    verifyBlocking(messageBus) { publishAsync(DefineUsersEcpmGroupEffect(USER_ID, reward.revenue.toBigDecimal())) }
    verifyBlocking(bigQueryEventPublisher) { publish(UserFirstVideoRewardBqEventDto(USER_ID, reward.revenue.toBigDecimal(), timeService.now())) }
  }

  @Test
  fun `SHOULD try to store first video ad reward ON storeVideoReward WHEN it was already stored and skip emitting the effect`() = testScope.runTest {
    val reward = VideoAdReward(revenue = 0.123456789123456)
    userPersistenceService.mock({ storeFirstVideoReward(USER_ID, reward) }, false)


    userService.storeVideoReward(USER_ID, reward)

    verifyBlocking(userPersistenceService) { storeFirstVideoReward(USER_ID, reward) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD call persistence whitelist user ON whitelistUser`() {
    runBlocking {
      userService.whitelistUser(USER_ID)
    }
    verifyBlocking(userPersistenceService) { whitelistUser(USER_ID) }
  }

  @Test
  fun `SHOULD call persistence updateUserConsent ON updateUserConsent`() {
    val consent = ConsentApiDto(hasConsentedToAnalytics = true, hasConsentedToTargetedAdvertisement = false, librariesConsent = null)
    runBlocking {
      userService.updateUserConsent(USER_ID, consent)
    }
    verifyBlocking(userPersistenceService) { updateUserConsent(USER_ID, consent) }
  }

  @Test
  fun `SHOULD correctly call persistence layer ON countUsersWithSameGoogleAdId`() {
    runBlocking {
      userService.countUsersWithSameGoogleAdId("u1", "g1")
    }

    verifyBlocking(userPersistenceService) { countUsersWithSameGoogleAdId("u1", "g1") }
  }

  @Test
  fun `SHOULD correctly call persistence layer ON countUsersWithSameTrackingData`() {
    val expectedTrackingData = generateTrackingData()

    runBlocking {
      userService.countUsersWithSameTrackingData("u1", expectedTrackingData)
    }

    verifyBlocking(userPersistenceService) { countUsersWithSameTrackingData("u1", expectedTrackingData) }
  }

  @Test
  fun `SHOULD correctly call persistence layer ON loadUserCountries`() {
    runBlocking {
      userService.loadUserCountries("u1", "1.1.1.1")
    }

    verifyBlocking(userPersistenceService) { loadUserCountries("u1", "1.1.1.1") }
  }

  @Test
  fun `SHOULD correctly call persistence layer ON getUserLastGameCoinsDate`() {
    runBlocking {
      userService.getUserLastGameCoinsDate("u1")
    }

    verifyBlocking(userPersistenceService) { getUserLastGameCoinsDate("u1") }
  }

  @Test
  fun `SHOULD correctly call persistence layer ON getUserLastGameEm2CoinsDate`() {
    runBlocking {
      userService.getUserLastGameEm2CoinsDate("u1")
    }

    verifyBlocking(userPersistenceService) { getUserLastGameEm2CoinsDate("u1") }
  }

  @Test
  fun `SHOULD correctly call persistence layer ON getUserCountryCode`() {
    runBlocking {
      userService.getUserCountryCode("u1")
    }

    verifyBlocking(userPersistenceService) { getUserCountryCode("u1") }
  }

  @Test
  fun `SHOULD correctly call persistence layer ON isUserUnique`() {
    runBlocking {
      userService.isUserUnique("u1")
    }

    verifyBlocking(userPersistenceService) { isUserUnique("u1") }
  }

  @Test
  fun `SHOULD correctly call persistence layer ON getUserAdditionalCountryInfo`() {
    runBlocking {
      userService.getUserAdditionalCountryInfo("u1")
    }

    verifyBlocking(userPersistenceService) { getUserAdditionalCountryInfo("u1") }
  }

  @Test
  fun `SHOULD correctly call persistence layer ON getUserCountryTierSettings`() {
    runBlocking {
      userService.getUserCountryTierSettings("u1")
    }

    verifyBlocking(userPersistenceService) { getUserCountryTierSettings("u1") }
  }

  @Test
  fun `SHOULD correctly call persistence layer ON wasUserEverConnectedViaVpn`() {
    runBlocking {
      userService.wasUserEverConnectedViaVpn("u1")
    }

    verifyBlocking(userPersistenceService) { wasUserEverConnectedViaVpn("u1") }
  }

  @Test
  fun `SHOULD correctly call persistence layer ON getUserFirstAppVersion`() {
    runBlocking {
      userService.getUserFirstAppVersion("u1")
    }

    verifyBlocking(userPersistenceService) { getUserFirstAppVersion("u1") }
  }

  @Test
  fun `SHOULD call persistence layer ON getUserEcpmGroup`() {
    userPersistenceService.mock({ getUserEcpmGroup("u1") }, 10500)

    runBlocking {
      userService.getUserEcpmGroup("u1")
    }.let { assertThat(it).isEqualTo(10500) }

    verifyBlocking(userPersistenceService) { getUserEcpmGroup("u1") }
  }

  @Test
  fun `SHOULD track min thresholds for 20 groups on calculateMinThresholdsForEcpmGroups`() {
    userPersistenceService.mock(
      { getFirstRewardsForUsersCreatedDuringPreviousDaysOrdered(7) },
      (1..20000).map { it.toBigDecimal() }.sortedDescending()
    )
    val expected = mapOf(
      0 to BigDecimal("19001"),
      1 to BigDecimal("18001"),
      2 to BigDecimal("17001"),
      3 to BigDecimal("16001"),
      4 to BigDecimal("15001"),
      5 to BigDecimal("14001"),
      6 to BigDecimal("13001"),
      7 to BigDecimal("12001"),
      8 to BigDecimal("11001"),
      9 to BigDecimal("10001"),
      10 to BigDecimal("9001"),
      11 to BigDecimal("8001"),
      12 to BigDecimal("7001"),
      13 to BigDecimal("6001"),
      14 to BigDecimal("5001"),
      15 to BigDecimal("4001"),
      16 to BigDecimal("3001"),
      17 to BigDecimal("2001"),
      18 to BigDecimal("1001"),
      19 to BigDecimal("1"),
    )
    runBlocking { userService.calculateMinThresholdsForEcpmGroups() }

    verifyBlocking(userPersistenceService) {
      writeCurrentEcpmGroupsThresholds(expected)
    }
  }

  @Test
  fun `SHOULD track min thresholds for 20 groups ON calculateMinThresholdsForEcpmGroups WHEN we have only 10 rewards`() {
    userPersistenceService.mock(
      { getFirstRewardsForUsersCreatedDuringPreviousDaysOrdered(7) },
      (1..20).map { it.toBigDecimal() }.sortedDescending()
    )
    val expected = mapOf(
      0 to BigDecimal("20"),
      1 to BigDecimal("19"),
      2 to BigDecimal("18"),
      3 to BigDecimal("17"),
      4 to BigDecimal("16"),
      5 to BigDecimal("15"),
      6 to BigDecimal("14"),
      7 to BigDecimal("13"),
      8 to BigDecimal("12"),
      9 to BigDecimal("11"),
      10 to BigDecimal("10"),
      11 to BigDecimal("9"),
      12 to BigDecimal("8"),
      13 to BigDecimal("7"),
      14 to BigDecimal("6"),
      15 to BigDecimal("5"),
      16 to BigDecimal("4"),
      17 to BigDecimal("3"),
      18 to BigDecimal("2"),
      19 to BigDecimal("1"),
    )
    runBlocking { userService.calculateMinThresholdsForEcpmGroups() }

    verifyBlocking(userPersistenceService) {
      writeCurrentEcpmGroupsThresholds(expected)
    }
  }

  @Test
  fun `SHOULD fall ungracefully ON calculateMinThresholdsForEcpmGroups WHEN we have less than 20 rewards for the last 7 days`() {
    userPersistenceService.mock(
      { getFirstRewardsForUsersCreatedDuringPreviousDaysOrdered(7) },
      (1..17).map { it.toBigDecimal() }.sortedDescending()
    )
    assertFailsWith<IllegalStateException> {
      runBlocking {
        userService.calculateMinThresholdsForEcpmGroups()
      }
    }
  }

  @Test
  fun `SHOULD return null ON getDay0RevenueForUser WHEN user created less than 24 hours ago`() {
    userDataCache.mock({ getUserData(USER_ID, includingDeleted = true) }, userDtoStub.copy(createdAt = timeService.now()))

    rewardingFacade.mock({ getRevenueTotals(any()) }, RevenueTotals(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    val result = runBlocking { userService.getDay0RevenueForUser(USER_ID) }

    assertThat(result).isNull()
  }

  @Test
  fun `SHOULD return d0 ON getDay0RevenueForUser WHEN user older than 24 hours and has revenue`() {
    userDataCache.mock({ getUserData(USER_ID, includingDeleted = true) }, userDtoStub.copy(createdAt = timeService.now().minus(2, ChronoUnit.DAYS)))

    rewardingFacade.mock({ getRevenueTotals(any()) }, RevenueTotals(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.TEN))
    val result = runBlocking { userService.getDay0RevenueForUser(USER_ID) }

    assertThat(result).isEqualTo(BigDecimal.TEN)
  }

  private fun generateTrackingData(
    trackingId: String = UUID.randomUUID().toString(),
    trackingType: TrackingDataType = IDFA,
    appPlatform: AppPlatform = ANDROID
  ): TrackingData = TrackingData(trackingId, trackingType, appPlatform)

}
