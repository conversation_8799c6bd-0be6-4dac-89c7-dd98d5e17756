package com.moregames.playtime.user.challenge.progress.calculator

import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.ApplicationId
import com.moregames.playtime.user.objectives.progress.achievement.AchievementDto
import com.moregames.playtime.user.objectives.progress.calculator.MilestoneProgressCalculator
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class MilestoneProgressCalculatorTest {
  private val json: Json = com.moregames.playtime.utils.Json.defaultJsonConverter
  private val underTest = MilestoneProgressCalculator(json)

  companion object {
    const val USER_ID = "userId"
  }

  @Test
  fun `SHOULD return next achievement WHEN achievement is correct`() {
    val progressDto = UserChallengeProgressDto.MilestoneProgressDto(
      LevelIdProgressCalculatorTest.Companion.USER_ID,
      ApplicationId.SOLITAIRE_VERSE_APP_ID,
      milestone = 45,
    )
    val result = underTest.nextAchievement(progressDto)
    assertEquals(AchievementDto(setOf("45")), result)
  }

  @Test
  fun `SHOULD return empty achievement WHEN progressDto has wrong type`() {
    val progressDto = UserChallengeProgressDto.LevelIdProgressDto(
      LevelIdProgressCalculatorTest.Companion.USER_ID,
      ApplicationId.SOLITAIRE_VERSE_APP_ID,
      levelId = "5"
    )
    val result = underTest.nextAchievement(progressDto)
    assertEquals(AchievementDto.empty(), result)
  }

}