package com.moregames.playtime.user.dto

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.playtime.user.dto.UserApiDto.MilestoneDesc
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuActionApiDto
import com.moregames.playtime.user.dto.threedotmenu.ThreeDotMenuItem
import kotlinx.serialization.ExperimentalSerializationApi
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

@OptIn(ExperimentalSerializationApi::class)
class UserApiDtoTest {

  private val now = Instant.now()
  private val threeDotMenu = listOf(
    ThreeDotMenuItem(ThreeDotMenuActionApiDto.routeToRewards(), "My Rewards"),
    ThreeDotMenuItem(ThreeDotMenuActionApiDto.contactUs(), "Contact Us"),
    ThreeDotMenuItem(ThreeDotMenuActionApiDto.openLinkInPopUp("https://www.justplayapps.com/privacy-policy"), "Privacy policy"),
    ThreeDotMenuItem(ThreeDotMenuActionApiDto.openLinkInPopUp("https://www.justplayapps.com/loyalty-program-rules"), "Loyalty Program Rules"),
  )
  private val user = User(
    userId = "userId",
    googleAdId = null,
    deviceToken = "deviceToken",
    coinsGoal = 4242,
    createdAt = Instant.now().minus(1, ChronoUnit.DAYS),
    appPlatform = ANDROID,
    appVersion = 0,
    countryCode = "US",
    isConsentedToAnalytics = false,
  )

  private val coinsBalance = UserCurrentCoinsGoalBalance(
    coins = 314,
    gameCoins = 271,
    goalCoins = 602,
  )

  @Test
  fun `SHOULD create UserApiDto ON secondary constructor`() {
    val expected = UserApiDto(
      userId = user.userId,
      coins = coinsBalance.goalCoins,
      coinsString = coinsBalance.coins.toString(),
      coinsBalance = coinsBalance.coins,
      coinGoal = 0,
      coinGoalString = "0",
      cashoutAvailable = false,
      cashoutAmount = "",
      nextCashoutTimestamp = null,
      useRewards = false,
      timestamp = now,
      coinGoalLabel = "mainCoinGoalReachedLabel",
      expLabels = user.expLabels,
      tutorialSteps = emptyList(),
      videoAdIntervalSeconds = null,
      showTimerInCoinGoalSection = false,
      threeDotMenuItems = threeDotMenu,
      useAmplitudeAnalytics = false,
      market = "test-market",
      attestationRequired = false,
      consentedToAnalytics = true,
      enablePlaystoreTrackingNotifications = true,
      playersOnlineType = "CENTER_LONG_BLUE",
      giftBoxInsteadOfEarnings = false,
      cashoutButtonStyle = null,
      cashoutTimerSubtext = null,
      paymentProviderSurvey = null,
      cashoutProgressBarMode = null,
      numberSeparatorType = "locale",
      preGameMode = null,
      showPayPalLogo = null,
      coinGoalBarMode = UserApiDto.CoinGoalBarMode.EM3,
      cashoutPeriodId = "cashout-period-id",
      milestonesConfig = listOf(
        MilestoneDesc(20000, 6000),
        MilestoneDesc(40000, 6000),
        MilestoneDesc(60000, 10000),
      ),
      bonusCashBarAvailable = false,
    )

    val actual = UserApiDto(
      user = user,
      coinsBalance = coinsBalance,
      cashoutAvailable = false,
      cashoutAmount = "",
      nextCashoutTimestamp = null,
      useRewards = false,
      timestamp = now,
      tutorialSteps = emptyList(),
      videoAdIntervalSeconds = null,
      showTimerInCoinGoalSection = false,
      threeDotMenuItems = threeDotMenu,
      market = "test-market",
      attestationRequired = false,
      consentedToAnalytics = true,
      coinGoal = 0,
      coinGoalReached = true,
      giftBoxInsteadOfEarnings = false,
      cashoutButtonStyle = null,
      cashoutTimerSubtext = null,
      cashoutProgressBarMode = null,
      useAmplitudeAnalytics = false,
      paymentProviderSurvey = null,
      incompleteCashoutRestoringMode = null,
      initializeApplovin = null,
      cashoutBonusCoins = null,
      faceScanPreScreen = null,
      paymentProviderAvailable = null,
      privacyRegulation = null,
      showPayPalLogo = null,
      coinGoalBarMode = UserApiDto.CoinGoalBarMode.EM3,
      cashoutPeriodId = "cashout-period-id",
      milestonesConfig = listOf(
        MilestoneDesc(20000, 6000),
        MilestoneDesc(40000, 6000),
        MilestoneDesc(60000, 10000),
      ),
      bonusCashBarAvailable = false,
    )

    assertThat(actual).isEqualTo(expected)
  }

}