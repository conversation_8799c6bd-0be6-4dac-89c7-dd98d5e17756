package com.moregames.playtime.user

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.junit.MockExtension
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.ForceAssignGaUserPropertiesEffect
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.verify

@ExtendWith(MockExtension::class)
class UserFirebasePropertiesForceAssignmentsServiceTest(
  private val persistenceService: UserFirebasePropertiesForceAssignmentsPersistenceService,
  private val messageBus: MessageBus,
) {

  private val service = UserFirebasePropertiesForceAssignmentsService(
    persistenceService = persistenceService,
    messageBus = messageBus,
  )

  @Test
  fun `SHOULD publish effects and remove records afterwards WHEN forceAssignGaUserProperties called`() = runTest {
    persistenceService.mock(
      { getUserAssignmentsBatch("a", 100) },
      listOf(UserFirebasePropertiesForceAssignmentEntity(userId = USER_ID, firebaseAppInstanceId = FIREBASE_APP_INSTANCE_ID, appPlatform = AppPlatform.ANDROID))
    )

    val result = service.forceAssignGaUserProperties("a", 100)

    verify(messageBus).publishAsync(ForceAssignGaUserPropertiesEffect(USER_ID, FIREBASE_APP_INSTANCE_ID, AppPlatform.ANDROID))
    verify(persistenceService).remove(listOf(USER_ID))

    assertThat(result).isEqualTo(1)
  }

  private companion object {
    private const val USER_ID = "userId"
    private const val FIREBASE_APP_INSTANCE_ID = "firebaseAppId"
  }
}