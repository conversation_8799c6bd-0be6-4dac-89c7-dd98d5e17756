package com.moregames.playtime.user.offer

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.util.mock
import com.moregames.base.util.mockExperimentVariation
import com.moregames.playtime.general.MarketService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestFactory
import org.mockito.kotlin.mock
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class DisableMxOfferwallExperimentServiceTest {
  private val abTestingService: AbTestingService = mock()
  private val marketService: MarketService = mock()

  private val underTest = DisableMxOfferwallExperimentService(
    abTestingService = abTestingService,
    marketService = marketService,
  )

  @Test
  fun `SHOULD return false WHEN market is not latam`() = runBlocking {
    marketService.mock({ isLatamMarket() }, false)

    assertFalse { underTest.shouldHideOfferwall(USER_ID) }
    assertFalse { underTest.shouldSkipOfwHighlight(USER_ID) }
  }

  @TestFactory
  fun `SHOULD return false ON shouldSkipOfwHighlight WHEN market is latam but wrong variation`(): List<DynamicTest> =
    listOf(DEFAULT, Variations.MX_NO_OFW).map {
      DynamicTest.dynamicTest(it.getKey()) {
        marketService.mock({ isLatamMarket() }, true)
        abTestingService.mockExperimentVariation(USER_ID, ClientExperiment.DISABLE_OFW_MX, it)

        assertFalse { runBlocking { underTest.shouldSkipOfwHighlight(USER_ID) } }
      }
    }

  @TestFactory
  fun `SHOULD return false ON shouldHideOfw WHEN market is latam but wrong variation`(): List<DynamicTest> =
    listOf(DEFAULT, Variations.MX_NO_OFW_HIGHLIGHT).map {
      DynamicTest.dynamicTest(it.getKey()) {
        marketService.mock({ isLatamMarket() }, true)
        abTestingService.mockExperimentVariation(USER_ID, ClientExperiment.DISABLE_OFW_MX, it)

        assertFalse { runBlocking { underTest.shouldHideOfferwall(USER_ID) } }
      }
    }

  @Test
  fun `SHOULD return true ON shouldHideOfw WHEN market is latam and correct variation`() {
    marketService.mock({ isLatamMarket() }, true)
    abTestingService.mockExperimentVariation(USER_ID, ClientExperiment.DISABLE_OFW_MX, Variations.MX_NO_OFW)

    assertTrue { runBlocking { underTest.shouldHideOfferwall(USER_ID) } }
  }

  @Test
  fun `SHOULD return true ON shouldSkipOfwHighlight WHEN market is latam and correct variation`() {
    marketService.mock({ isLatamMarket() }, true)
    abTestingService.mockExperimentVariation(USER_ID, ClientExperiment.DISABLE_OFW_MX, Variations.MX_NO_OFW_HIGHLIGHT)

    assertTrue { runBlocking { underTest.shouldSkipOfwHighlight(USER_ID) } }
  }

  private companion object {
    val USER_ID = "userId"
  }
}