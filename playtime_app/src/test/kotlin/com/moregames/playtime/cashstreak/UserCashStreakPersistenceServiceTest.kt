package com.moregames.playtime.cashstreak

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isFalse
import assertk.assertions.isNull
import assertk.assertions.isTrue
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.util.TimeService
import com.moregames.playtime.cashstreak.CashStreakEngine.CashStreakData
import com.moregames.playtime.cashstreak.model.CashStreakReward
import com.moregames.playtime.cashstreak.model.CashStreakRewardType
import com.moregames.playtime.cashstreak.table.CashStreakRewardsTable
import com.moregames.playtime.cashstreak.table.UserCashStreakRewardsTable
import com.moregames.playtime.cashstreak.table.UserCashStreakTable
import com.moregames.playtime.notifications.NotificationType.DO_NOT_LOSE_CASH_STREAK
import com.moregames.playtime.user.prepareUser
import com.moregames.playtime.user.table.UserLastNotificationTable
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit

@ExtendWith(DatabaseExtension::class)
class UserCashStreakPersistenceServiceTest(private val database: Database) {

  private val timeService: TimeService = mock()
  private val service: UserCashStreakPersistenceService = UserCashStreakPersistenceService(database, timeService)

  companion object {
    private val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    private val mDeadline = now.plusSeconds(101)
  }

  @BeforeEach
  fun init() {
    whenever(timeService.now()).thenReturn(now)
    transaction(database) {
      UserCashStreakRewardsTable.deleteAll()
      CashStreakRewardsTable.deleteAll()
    }
  }

  @Test
  fun `SHOULD track initial data for the streak ON startTheFirstStreak`() {
    val userId = database.prepareUser()

    runBlocking {
      service.startTheFirstStreak(userId, mDeadline)
    }

    transaction(database) {
      UserCashStreakTable
        .select { UserCashStreakTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserCashStreakTable.streakCounter]).isEqualTo(1)
          assertThat(it[UserCashStreakTable.deadline]).isEqualTo(mDeadline)
          assertThat(it[UserCashStreakTable.lastGoalReachedAt]).isEqualTo(now)
          assertThat(it[UserCashStreakTable.streakStart]).isEqualTo(now)
          assertThat(it[UserCashStreakTable.currentStepEnd]).isEqualTo(mDeadline.minus(1, ChronoUnit.DAYS))
          assertThat(it[UserCashStreakTable.maxStreakCounter]).isEqualTo(1)
        }
    }
  }

  @Test
  fun `SHOULD do not track initial data for the streak ON startTheFirstStreak WHEN someone was faster`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserCashStreakTable.insertIgnore {
        it[UserCashStreakTable.userId] = userId
        it[streakCounter] = 1
        it[deadline] = mDeadline
        it[lastGoalReachedAt] = now
        it[streakStart] = now
        it[currentStepEnd] = mDeadline.minus(1, ChronoUnit.DAYS)
        it[maxStreakCounter] = 1
      }
    }

    runBlocking {
      service.startTheFirstStreak(userId, mDeadline.plusSeconds(1))
    }

    transaction(database) {
      UserCashStreakTable
        .select { UserCashStreakTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserCashStreakTable.streakCounter]).isEqualTo(1)
          assertThat(it[UserCashStreakTable.deadline]).isEqualTo(mDeadline)
          assertThat(it[UserCashStreakTable.lastGoalReachedAt]).isEqualTo(now)
          assertThat(it[UserCashStreakTable.streakStart]).isEqualTo(now)
          assertThat(it[UserCashStreakTable.currentStepEnd]).isEqualTo(mDeadline.minus(1, ChronoUnit.DAYS))
          assertThat(it[UserCashStreakTable.maxStreakCounter]).isEqualTo(1)
        }
    }
  }

  @Test
  fun `SHOULD return streak data ON getCashStreakData WHEN we have some`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserCashStreakTable.insertIgnore {
        it[UserCashStreakTable.userId] = userId
        it[streakCounter] = 17
        it[deadline] = mDeadline
        it[lastGoalReachedAt] = now
        it[streakStart] = now.minusSeconds(50000)
        it[currentStepEnd] = mDeadline.minus(1, ChronoUnit.DAYS)
        it[maxStreakCounter] = 117
      }
    }

    runBlocking {
      service.getCashStreakData(userId)
    }.let {
      assertThat(it).isEqualTo(
        CashStreakData(
          deadline = mDeadline,
          lastGoalReachedAt = now,
          streakCounter = 17,
          currentStepEnd = mDeadline.minus(1, ChronoUnit.DAYS),
          streakStart = now.minusSeconds(50000),
          maxStreakCounter = 117
        )
      )
    }
  }

  @Test
  fun `SHOULD return null  ON getCashStreakData WHEN we have no data yet`() {
    val userId = database.prepareUser()

    runBlocking {
      service.getCashStreakData(userId)
    }.let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD update streak data for the user with initial values ON startFromTheVeryBeginning`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserCashStreakTable.insertIgnore {
        it[UserCashStreakTable.userId] = userId
        it[streakCounter] = 17
        it[deadline] = now.minusSeconds(10001)
        it[lastGoalReachedAt] = now.minusSeconds(20002)
        it[maxStreakCounter] = 117
      }
    }

    runBlocking {
      service.startFromTheVeryBeginning(userId, mDeadline)
    }

    transaction(database) {
      UserCashStreakTable
        .select { UserCashStreakTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserCashStreakTable.streakCounter]).isEqualTo(1)
          assertThat(it[UserCashStreakTable.deadline]).isEqualTo(mDeadline)
          assertThat(it[UserCashStreakTable.lastGoalReachedAt]).isEqualTo(now)
          assertThat(it[UserCashStreakTable.maxStreakCounter]).isEqualTo(117)
        }
    }
  }

  @Test
  fun `SHOULD track last goal reached time and increase counter ON acceptCashStreak`() {
    val userId = database.prepareUser()
    val curStreakData = CashStreakData(
      deadline = mDeadline.minus(1, ChronoUnit.DAYS),
      lastGoalReachedAt = now.minus(1, ChronoUnit.DAYS),
      streakCounter = 15,
      streakStart = now.minusSeconds(50000),
      currentStepEnd = mDeadline.minus(2, ChronoUnit.DAYS),
      maxStreakCounter = 117
    )

    transaction(database) {
      UserCashStreakTable.insertIgnore {
        it[UserCashStreakTable.userId] = userId
        it[streakCounter] = curStreakData.streakCounter
        it[deadline] = curStreakData.deadline
        it[lastGoalReachedAt] = curStreakData.lastGoalReachedAt
        it[maxStreakCounter] = curStreakData.maxStreakCounter
      }
    }

    runBlocking {
      service.acceptCashStreakReturnAccepted(userId, curStreakData)
    }.let { assertThat(it).isTrue() }

    transaction(database) {
      UserCashStreakTable
        .select { UserCashStreakTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserCashStreakTable.streakCounter]).isEqualTo(16)
          assertThat(it[UserCashStreakTable.deadline]).isEqualTo(mDeadline)
          assertThat(it[UserCashStreakTable.lastGoalReachedAt]).isEqualTo(now)
          assertThat(it[UserCashStreakTable.maxStreakCounter]).isEqualTo(117)
        }
    }
  }

  @Test
  fun `SHOULD shift max streak counter ON acceptCashStreak WHEN current streak is longer than the previous one`() {
    val userId = database.prepareUser()
    val curStreakData = CashStreakData(
      deadline = mDeadline.minus(1, ChronoUnit.DAYS),
      lastGoalReachedAt = now.minus(1, ChronoUnit.DAYS),
      streakCounter = 15,
      streakStart = now.minusSeconds(50000),
      currentStepEnd = mDeadline.minus(2, ChronoUnit.DAYS),
      maxStreakCounter = 15
    )

    transaction(database) {
      UserCashStreakTable.insertIgnore {
        it[UserCashStreakTable.userId] = userId
        it[streakCounter] = curStreakData.streakCounter
        it[deadline] = curStreakData.deadline
        it[lastGoalReachedAt] = curStreakData.lastGoalReachedAt
      }
    }

    runBlocking {
      service.acceptCashStreakReturnAccepted(userId, curStreakData)
    }.let { assertThat(it).isTrue() }

    transaction(database) {
      UserCashStreakTable
        .select { UserCashStreakTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserCashStreakTable.streakCounter]).isEqualTo(16)
          assertThat(it[UserCashStreakTable.deadline]).isEqualTo(mDeadline)
          assertThat(it[UserCashStreakTable.lastGoalReachedAt]).isEqualTo(now)
          assertThat(it[UserCashStreakTable.streakCounter]).isEqualTo(16)
        }
    }
  }

  @Test
  fun `SHOULD do nothing ON acceptCashStreak WHEN some other thread was faster`() {
    val userId = database.prepareUser()
    val curStreakData = CashStreakData(
      deadline = mDeadline.minus(1, ChronoUnit.DAYS).minusSeconds(1),
      lastGoalReachedAt = now.minus(1, ChronoUnit.DAYS).minusSeconds(1),
      streakCounter = 14,
      streakStart = now.minusSeconds(50000),
      currentStepEnd = mDeadline.minus(2, ChronoUnit.DAYS).minusSeconds(1),
      maxStreakCounter = 114
    )

    transaction(database) {
      UserCashStreakTable.insertIgnore {
        it[UserCashStreakTable.userId] = userId
        it[streakCounter] = 16
        it[deadline] = mDeadline
        it[lastGoalReachedAt] = now
        it[currentStepEnd] = mDeadline.minus(1, ChronoUnit.DAYS)
        it[streakStart] = now.minusSeconds(50000)
        it[maxStreakCounter] = 114
      }
    }

    runBlocking {
      service.acceptCashStreakReturnAccepted(userId, curStreakData)
    }.let { assertThat(it).isFalse() }

    transaction(database) {
      UserCashStreakTable
        .select { UserCashStreakTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserCashStreakTable.streakCounter]).isEqualTo(16)
          assertThat(it[UserCashStreakTable.deadline]).isEqualTo(mDeadline)
          assertThat(it[UserCashStreakTable.lastGoalReachedAt]).isEqualTo(now)
          assertThat(it[UserCashStreakTable.maxStreakCounter]).isEqualTo(114)
        }
    }
  }

  @Test
  fun `SHOULD return unclaimed rewards ON getUnclaimedRewards`() {
    val now = Instant.now().minusSeconds(10500)
    val uniqueKey = now.toString()
    val userId = database.prepareUser()
    val rewardId1 = database.prepareReward(2, CashStreakRewardType.COINS, BigDecimal("25000"), false)
    val rewardId2 = database.prepareReward(3, CashStreakRewardType.COINS, BigDecimal("26000"), true)
    database.prepareReward(4, CashStreakRewardType.COINS, BigDecimal("27000"), false)

    val expected = CashStreakReward(
      achievementDay = 3,
      type = CashStreakRewardType.COINS,
      value = BigDecimal("26000.000000"),
      bigReward = true
    )

    transaction(database) {
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId1
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.CLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId2
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.UNCLAIMED
        }
    }


    runBlocking {
      service.getUnclaimedRewards(userId)
    }.let { assertThat(it).isEqualTo(listOf(expected)) }
  }

  @Test
  fun `SHOULD return empty unclaimed rewards ON getUnclaimedRewards WHEN all rewards were claimed`() {
    val now = Instant.now().minusSeconds(10500)
    val uniqueKey = now.toString()
    val userId = database.prepareUser()
    val rewardId1 = database.prepareReward(2, CashStreakRewardType.COINS, BigDecimal("25000"), false)
    database.prepareReward(3, CashStreakRewardType.COINS, BigDecimal("26000"), true)
    database.prepareReward(4, CashStreakRewardType.COINS, BigDecimal("27000"), false)

    transaction(database) {
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId1
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.CLAIMED
        }
    }

    runBlocking {
      service.getUnclaimedRewards(userId)
    }.let { assertThat(it).isEqualTo(emptyList()) }
  }

  @Test
  fun `SHOULD return empty unclaimed rewards ON getUnclaimedRewards WHEN no rewards for the user`() {
    val userId = database.prepareUser()
    database.prepareReward(2, CashStreakRewardType.COINS, BigDecimal("25000"), false)
    database.prepareReward(3, CashStreakRewardType.COINS, BigDecimal("26000"), true)
    database.prepareReward(4, CashStreakRewardType.COINS, BigDecimal("27000"), false)

    runBlocking {
      service.getUnclaimedRewards(userId)
    }.let { assertThat(it).isEqualTo(emptyList()) }
  }

  @Test
  fun `SHOULD claim all rewards on claimAllUnclaimedRewardsReturnBonusCoinsAmount`() {
    val now = Instant.now().minusSeconds(10500)
    val uniqueKey = now.toString()
    val userId = database.prepareUser()
    val rewardId1 = database.prepareReward(2, CashStreakRewardType.COINS, BigDecimal("25000"), false)
    val rewardId2 = database.prepareReward(3, CashStreakRewardType.EARNING_POWER, BigDecimal("2"), true)
    val rewardId3 = database.prepareReward(4, CashStreakRewardType.COINS, BigDecimal("27000"), false)
    val rewardId4 = database.prepareReward(5, CashStreakRewardType.COINS, BigDecimal("28000"), false)

    transaction(database) {
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId1
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.CLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId2
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.UNCLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId3
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.UNCLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId4
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.UNCLAIMED
        }
    }

    runBlocking {
      service.claimAllUnclaimedRewardsReturnBonusCoinsAmount(userId)
    }.let { assertThat(it).isEqualTo(BigDecimal("55000.000000")) }

    transaction(database) {
      UserCashStreakRewardsTable
        .select {
          UserCashStreakRewardsTable.userId eq userId
        }
        .map {
          assertThat(it[UserCashStreakRewardsTable.status]).isEqualTo(CashStreakRewardsService.CashStreakRewardStatus.CLAIMED)
        }
    }
  }

  @Test
  fun `SHOULD return 0 coins ON claimAllUnclaimedRewardsReturnBonusCoinsAmount WHEN all coins were claimed`() {
    val now = Instant.now().minusSeconds(10500)
    val uniqueKey = now.toString()
    val userId = database.prepareUser()
    val rewardId1 = database.prepareReward(2, CashStreakRewardType.COINS, BigDecimal("25000"), false)
    val rewardId2 = database.prepareReward(3, CashStreakRewardType.EARNING_POWER, BigDecimal("2"), true)
    val rewardId3 = database.prepareReward(4, CashStreakRewardType.COINS, BigDecimal("27000"), false)
    val rewardId4 = database.prepareReward(5, CashStreakRewardType.COINS, BigDecimal("28000"), false)

    transaction(database) {
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId1
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.CLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId2
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.UNCLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId3
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.CLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId4
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.CLAIMED
        }
    }

    runBlocking {
      service.claimAllUnclaimedRewardsReturnBonusCoinsAmount(userId)
    }.let { assertThat(it).isNull() }

    transaction(database) {
      UserCashStreakRewardsTable
        .select {
          UserCashStreakRewardsTable.userId eq userId
        }
        .map {
          assertThat(it[UserCashStreakRewardsTable.status]).isEqualTo(CashStreakRewardsService.CashStreakRewardStatus.CLAIMED)
        }
    }
  }

  @Test
  fun `SHOULD return 0 coins ON claimAllUnclaimedRewardsReturnBonusCoinsAmount WHEN user had no rewards`() {
    val userId = database.prepareUser()

    runBlocking {
      service.claimAllUnclaimedRewardsReturnBonusCoinsAmount(userId)
    }.let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD return reward on specific day on getRewardByAchievementDayId`() {
    database.prepareReward(2, CashStreakRewardType.COINS, BigDecimal("25000"), false)
    database.prepareReward(3, CashStreakRewardType.EARNING_POWER, BigDecimal("2"), true)
    database.prepareReward(4, CashStreakRewardType.COINS, BigDecimal("27000"), false)
    val rewardId4 = database.prepareReward(5, CashStreakRewardType.COINS, BigDecimal("28000"), false)
    database.prepareReward(7, CashStreakRewardType.COINS, BigDecimal("29000"), true)

    runBlocking {
      service.getRewardIdByAchievementDay(5)
    }.let { assertThat(it).isEqualTo(rewardId4) }

    runBlocking {
      service.getRewardIdByAchievementDay(6)
    }.let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD give reward to user ON giveRewardToUser`() {
    val now = Instant.now()
    val uniqueKey = now.toString()
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    val rewardId1 = database.prepareReward(2, CashStreakRewardType.COINS, BigDecimal("25000"), false)
    val rewardId2 = database.prepareReward(3, CashStreakRewardType.EARNING_POWER, BigDecimal("2"), true)
    val rewardId3 = database.prepareReward(4, CashStreakRewardType.EARNING_POWER, BigDecimal("3"), true)

    runBlocking {
      service.giveRewardToUser(userId, rewardId1, uniqueKey)
      service.giveRewardToUser(userId, rewardId1, uniqueKey)//double call with the same reward
      service.giveRewardToUser(userId, rewardId2, uniqueKey)
      service.giveRewardToUser(userId2, rewardId2, uniqueKey)
      service.giveRewardToUser(userId2, rewardId2, uniqueKey)//*
      service.giveRewardToUser(userId2, rewardId3, uniqueKey)
    }

    transaction(database) {
      UserCashStreakRewardsTable
        .select {
          UserCashStreakRewardsTable.userId eq userId
        }.associate {
          it[UserCashStreakRewardsTable.rewardId] to it[UserCashStreakRewardsTable.status]
        }.let {
          assertThat(it).isEqualTo(
            mapOf(
              rewardId1 to CashStreakRewardsService.CashStreakRewardStatus.UNCLAIMED,
              rewardId2 to CashStreakRewardsService.CashStreakRewardStatus.UNCLAIMED,
            )
          )
        }
      UserCashStreakRewardsTable
        .select {
          UserCashStreakRewardsTable.userId eq userId2
        }.associate {
          it[UserCashStreakRewardsTable.rewardId] to it[UserCashStreakRewardsTable.status]
        }.let {
          assertThat(it).isEqualTo(
            mapOf(
              rewardId2 to CashStreakRewardsService.CashStreakRewardStatus.UNCLAIMED,
              rewardId3 to CashStreakRewardsService.CashStreakRewardStatus.UNCLAIMED,
            )
          )
        }
    }
  }

  @Test
  fun `SHOULD return last earnings booster even if it was not claimed ON getCurrentEarningsBooster WHEN the streak is alive`() {
    val uniqueKey = now.toString()
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    val userId3 = database.prepareUser()
    val rewardId1 = database.prepareReward(2, CashStreakRewardType.COINS, BigDecimal("25000"), false)
    val rewardId2 = database.prepareReward(3, CashStreakRewardType.EARNING_POWER, BigDecimal("2"), true)
    val rewardId3 = database.prepareReward(4, CashStreakRewardType.EARNING_POWER, BigDecimal("3"), true)
    val rewardId4 = database.prepareReward(5, CashStreakRewardType.EARNING_POWER, BigDecimal("4"), true)

    transaction(database) {
      UserCashStreakTable.insertIgnore {
        it[UserCashStreakTable.userId] = userId
        it[streakCounter] = 16
        it[deadline] = mDeadline
        it[lastGoalReachedAt] = now
        it[currentStepEnd] = mDeadline.minus(1, ChronoUnit.DAYS)
        it[streakStart] = now.minusSeconds(50000)
      }
      UserCashStreakTable.insertIgnore {
        it[UserCashStreakTable.userId] = userId2
        it[streakCounter] = 16
        it[deadline] = mDeadline
        it[lastGoalReachedAt] = now
        it[currentStepEnd] = mDeadline.minus(1, ChronoUnit.DAYS)
        it[streakStart] = now.minusSeconds(50000)
      }
      UserCashStreakTable.insertIgnore {
        it[UserCashStreakTable.userId] = userId3
        it[streakCounter] = 16
        it[deadline] = now.minusSeconds(5) // failed streak
        it[lastGoalReachedAt] = now
        it[currentStepEnd] = mDeadline.minus(1, ChronoUnit.DAYS)
        it[streakStart] = now.minusSeconds(50000)
      }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId1
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.CLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId2
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.CLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId3
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.UNCLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId2
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId4
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.CLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId3
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId4
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.CLAIMED
        }
    }

    runBlocking {
      service.getCurrentEarningsBooster(userId)
    }.let {
      assertThat(it).isEqualTo(
        CashStreakReward(4, CashStreakRewardType.EARNING_POWER, BigDecimal("3.000000"), true)
      )
    }
    runBlocking {
      service.getCurrentEarningsBooster(userId2)
    }.let {
      assertThat(it).isEqualTo(
        CashStreakReward(5, CashStreakRewardType.EARNING_POWER, BigDecimal("4.000000"), true)
      )
    }
    runBlocking {
      service.getCurrentEarningsBooster(userId3)
    }.let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD drop all the rewards ON dropRewards`() {
    val uniqueKey = now.toString()
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    val rewardId1 = database.prepareReward(2, CashStreakRewardType.COINS, BigDecimal("25000"), false)
    val rewardId2 = database.prepareReward(3, CashStreakRewardType.EARNING_POWER, BigDecimal("2"), true)
    val rewardId3 = database.prepareReward(4, CashStreakRewardType.EARNING_POWER, BigDecimal("3"), true)
    val rewardId4 = database.prepareReward(5, CashStreakRewardType.EARNING_POWER, BigDecimal("4"), true)

    transaction(database) {
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId1
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.CLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId2
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.CLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId3
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.UNCLAIMED
        }
      UserCashStreakRewardsTable
        .insert {
          it[UserCashStreakRewardsTable.userId] = userId2
          it[streakUniqueKey] = uniqueKey
          it[rewardId] = rewardId4
          it[status] = CashStreakRewardsService.CashStreakRewardStatus.UNCLAIMED
        }
    }

    runBlocking {
      service.dropRewards(userId)
    }

    transaction(database) {
      UserCashStreakRewardsTable
        .select {
          UserCashStreakRewardsTable.userId eq userId
        }.count().let { assertThat(it).isEqualTo(0) }
      UserCashStreakRewardsTable
        .select {
          UserCashStreakRewardsTable.userId eq userId2
        }.count().let { assertThat(it).isEqualTo(1) }
    }
  }

  @Test
  fun `SHOULD track notifications in DB ON trackNotificationsSent`() {
    val userIds = (1..3).map { database.prepareUser() }

    transaction(database) {
      UserLastNotificationTable
        .insert {
          it[userId] = userIds[1]
          it[notificationType] = DO_NOT_LOSE_CASH_STREAK.name
          it[notifiedAt] = now.minusSeconds(10050)
        }
    }

    runBlocking {
      service.trackNotificationsSent(userIds, DO_NOT_LOSE_CASH_STREAK)
    }

    transaction(database) {
      UserLastNotificationTable
        .select {
          (UserLastNotificationTable.userId inList userIds)
        }
        .map {
          assertThat(it[UserLastNotificationTable.notifiedAt]).isEqualTo(now)
          assertThat(it[UserLastNotificationTable.notificationType]).isEqualTo(DO_NOT_LOSE_CASH_STREAK.name)
        }
        .let { assertThat(it.size).isEqualTo(3) }
    }
  }

  @Test
  fun `SHOULD get a batch of users on streak with close deadline and not notified recently ON getUsersOnStreakCloseToDeadline`() {
    val userIds = (1..10).map { database.prepareUser() }
    //1st on was not on streak ever
    transaction(database) {
      UserCashStreakTable.batchInsert(userIds.takeLast(9)) { userId ->
        val mDeadline = when {
          userIds.indexOf(userId) == 1 -> now.minusSeconds(10) // deadline passed
          userIds.indexOf(userId) == 2 -> now.plus(10, ChronoUnit.HOURS) // too early to notify
          userIds.indexOf(userId) == 3 -> now.plus(1, ChronoUnit.HOURS)
          else -> now.plus(2, ChronoUnit.HOURS)
        }
        this[UserCashStreakTable.userId] = userId
        this[UserCashStreakTable.streakCounter] = 16
        this[UserCashStreakTable.deadline] = mDeadline
        this[UserCashStreakTable.lastGoalReachedAt] = now.minusSeconds(10500)
        this[UserCashStreakTable.currentStepEnd] = mDeadline.minus(1, ChronoUnit.DAYS)
        this[UserCashStreakTable.streakStart] = now.minusSeconds(50000)
        this[UserCashStreakTable.maxStreakCounter] = 116
      }

      UserLastNotificationTable
        .insert {
          it[userId] = userIds[4]
          it[notifiedAt] = now.minus(1, ChronoUnit.HOURS)
          it[notificationType] = DO_NOT_LOSE_CASH_STREAK.name
        }
      UserLastNotificationTable
        .insert {
          it[userId] = userIds[5]
          it[notifiedAt] = now.minus(10, ChronoUnit.HOURS) // can notify again
          it[notificationType] = DO_NOT_LOSE_CASH_STREAK.name
        }
    }
    runBlocking {
      service.getUsersOnStreakCloseToDeadline(3, batchSize = 50)
    }.let { assertThat(it.toSet()).isEqualTo(userIds.takeLast(5).toSet() + userIds[3]) }

  }

  @Test
  fun `SHOULD return all rewards ON loadAllRewards`() {
    val expected =
      (1..10).map {
        val bigReward = (it in listOf(2, 3))
        CashStreakReward(it, CashStreakRewardType.COINS, BigDecimal("5000.000000") * it.toBigDecimal(), bigReward)
      }
    (1..10).shuffled().map {
      val bigReward = (it in listOf(2, 3))
      database.prepareReward(it, CashStreakRewardType.COINS, BigDecimal("5000").multiply(it.toBigDecimal()), bigReward)
    }

    runBlocking {
      service.loadAllRewardsOrdered()
    }.let {
      assertThat(it).isEqualTo(expected)
    }
  }

  private fun Database.prepareReward(
    achievementDay: Int,
    type: CashStreakRewardType,
    rewardValue: BigDecimal,
    bigReward: Boolean
  ): Int =
    transaction(this) {
      CashStreakRewardsTable.insertAndGetId {
        it[CashStreakRewardsTable.achievementDay] = achievementDay
        it[CashStreakRewardsTable.type] = type
        it[value] = rewardValue
        it[CashStreakRewardsTable.bigReward] = bigReward
      }.value
    }

}