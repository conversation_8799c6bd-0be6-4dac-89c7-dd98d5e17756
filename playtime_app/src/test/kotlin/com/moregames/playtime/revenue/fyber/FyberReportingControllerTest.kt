package com.moregames.playtime.revenue.fyber

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.playtime.revenue.fyber.dto.FyberCoinsReport
import com.moregames.playtime.util.defaultJsonConverter
import io.ktor.application.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.routing.*
import io.ktor.serialization.*
import io.ktor.server.testing.*
import org.junit.jupiter.api.Test
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal
import kotlin.test.assertFailsWith

class FyberReportingControllerTest {
  private val fyberReportingManager: FyberReportingManager = mock()

  private fun controller(): Application.() -> Unit = {
    install(IgnoreTrailingSlash)
    install(ContentNegotiation) {
      json(json = defaultJsonConverter)
    }
    routing {
      FyberReportingController(
        fyberReportingManager = fyberReportingManager
      ).startRouting(this)
    }
  }

  companion object {
    private val userId = "1bed19ba-5977-4f84-9338-7348d6170008"
    private val signature = "fa4c1aa617d38d322764450995665574ccf36ded"
    private val report = FyberCoinsReport(
      userId = userId,
      signature = signature,
      amount = 196826,
      currencyName = "Coins",
      currencyId = "coins",
      transactionId = "da44bd8b-e0ce-4079-9d57-208a99cbd797",
      payout = BigDecimal("4.4733"),
      vcsEnabled = false
    )
  }

  @Test
  fun `SHOULD trigger onIncomingReport ON incoming report callback call`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Get,
      uri = "callback?_trans_id_=da44bd8b-e0ce-4079-9d57-208a99cbd797&payout_net=4.4733&sid=fa4c1aa617d38d322764450995665574ccf36ded&uid=1bed19ba-5977-4f84-9338-7348d6170008&amount=196826&currency_name=Coins&currency_id=coins"
    ) {
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(fyberReportingManager) { onIncomingReport(report) }
  }

  @Test
  fun `SHOULD trigger onIncomingReport ON incoming report callback call WHEN vcs true provided`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Get,
      uri = "callback?_trans_id_=da44bd8b-e0ce-4079-9d57-208a99cbd797&payout_net=4.4733&sid=fa4c1aa617d38d322764450995665574ccf36ded&uid=1bed19ba-5977-4f84-9338-7348d6170008&amount=196826&currency_name=Coins&currency_id=coins&vcs_enabled=True"
    ) {
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(fyberReportingManager) { onIncomingReport(report.copy(vcsEnabled = true)) }
  }

  @Test
  fun `SHOULD trigger onIncomingReport ON incoming report callback call WHEN vcs false provided`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Get,
      uri = "callback?_trans_id_=da44bd8b-e0ce-4079-9d57-208a99cbd797&payout_net=4.4733&sid=fa4c1aa617d38d322764450995665574ccf36ded&uid=1bed19ba-5977-4f84-9338-7348d6170008&amount=196826&currency_name=Coins&currency_id=coins&vcs_enabled=False"
    ) {
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(fyberReportingManager) { onIncomingReport(report) }
  }

  @Test
  fun `SHOULD shorten string values onIncomingReport ON incoming report callback call WHEN they are too long`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Get,
      uri = "callback?_trans_id_=da44bd8b-e0ce-4079-9d57-208a99cbd797&payout_net=4.4733&sid=fa4c1aa617d38d322764450995665574ccf36ded&uid=1bed19ba-5977-4f84-9338-7348d6170008&amount=196826&currency_name=CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCoins&currency_id=CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCcoins"
    ) {
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(fyberReportingManager) {
      onIncomingReport(
        report.copy(
          currencyName = "CCCCCCCCCCCCCCCCCCCCCCCCCCCCCC",
          currencyId = "CCCCCCCCCCCCCCCCCCCCCCCCCCCCCC"
        )
      )
    }
  }

  @Test
  fun `SHOULD fail on fyber webhook call without a mandatory field like signature`() = withTestApplication(controller()) {
    assertFailsWith<NullPointerException> {
      handleRequest(
        method = HttpMethod.Get,
        uri = "callback?_trans_id_=da44bd8b-e0ce-4079-9d57-208a99cbd797&payout_net=4.4733&uid=1bed19ba-5977-4f84-9338-7348d6170008&amount=196826&currency_name=Coins&currency_id=coins"
      ) {
      }
    }

    verifyNoInteractions(fyberReportingManager)
  }

}