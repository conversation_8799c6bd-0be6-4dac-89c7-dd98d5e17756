package com.moregames.playtime.tracking

import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.TrackingDataType.IDFV
import com.moregames.base.junit.MockExtension
import com.moregames.base.messaging.dto.AdMarketEvent
import com.moregames.base.util.TimeService
import com.moregames.base.util.answer
import com.moregames.base.util.mock
import com.moregames.playtime.app.messaging.dto.MolocoInAppEvent
import com.moregames.playtime.buseffects.SendMolocoInAppEventEffect
import com.moregames.playtime.general.TrackedEventsPersistenceService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.dto.UserExternalIds
import com.moregames.playtime.user.tracking.TrackingData
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.whenever
import java.time.Instant
import java.util.*

@ExtendWith(MockExtension::class)
class AdMarketServiceTest(
  private val userService: UserService,
  private val messageBus: MessageBus,
  private val timeService: TimeService,
  private val adMarketPersistenceService: AdMarketPersistenceService,
  private val trackedEventsPersistenceService: TrackedEventsPersistenceService,
  private val adjustService: AdjustService,
) {
  private val service = AdMarketService(
    userService = userService,
    messageBus = messageBus,
    timeService = timeService,
    adMarketPersistenceService = adMarketPersistenceService,
    trackedEventsPersistenceService = trackedEventsPersistenceService,
    adjustService = { adjustService },
  )

  @BeforeEach
  fun before() {
    whenever(timeService.now()).thenReturn(now)
    adMarketPersistenceService.answer({ storeEvents(any(), any()) }, { it.arguments[1] as List<*> })
  }

  @Test
  fun `SHOULD send market events ON sendMarketEvents`() {
    val userId = UUID.randomUUID().toString()

    val allEvents = listOf(
      AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_1_DOLLAR,
      AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_2_DOLLARS,
      AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_3_DOLLARS,
      AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_4_DOLLARS,
      AdMarketEvent.EventType.EARNED_WITHIN_8_DAYS_8_DOLLARS,
      AdMarketEvent.EventType.EARNED_WITHIN_8_DAYS_13_DOLLARS,
    )

    userService.mock(
      { fetchExternalIds(userId) },
      UserExternalIds(userId, "googleAdId", "idfa", "adjustId", "firebaseAppId", null)
    )

    runBlocking {
      service.sendMarketEvents(userId, allEvents, repeatable = false)
    }

    allEvents.forEach { eventType ->
      verifyBlocking(messageBus) {
        publish(
          AdMarketEvent(
            userId = userId,
            googleAdId = "googleAdId",
            firebaseAppId = "firebaseAppId",
            adjustId = "adjustId",
            idfa = "idfa",
            eventType = eventType,
            createdAt = now,
            trackingId = null,
            trackingType = null,
            appPlatform = null,
          )
        )
      }
    }

  }

  @Test
  fun `SHOULD send market events ON sendMarketEvents WHE iOS user`() {
    val userId = UUID.randomUUID().toString()
    val td = TrackingData("trackingId", IDFV, IOS)
    val allEvents = listOf(
      AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_1_DOLLAR,
      AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_2_DOLLARS,
      AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_3_DOLLARS,
      AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_4_DOLLARS,
      AdMarketEvent.EventType.EARNED_WITHIN_8_DAYS_8_DOLLARS,
      AdMarketEvent.EventType.EARNED_WITHIN_8_DAYS_13_DOLLARS,
    )

    userService.mock(
      { fetchExternalIds(userId) },
      UserExternalIds(userId, "googleAdId", null, "adjustId", "firebaseAppId", td)
    )

    runBlocking {
      service.sendMarketEvents(userId, allEvents, repeatable = false)
    }

    allEvents.forEach { eventType ->
      verifyBlocking(messageBus) {
        publish(
          AdMarketEvent(
            userId = userId,
            googleAdId = "googleAdId",
            adjustId = "adjustId",
            firebaseAppId = "firebaseAppId",
            eventType = eventType,
            createdAt = now,
            trackingId = td.id,
            trackingType = td.type.name,
            appPlatform = td.platform.name,
            platform = td.platform.name,
          )
        )
      }
    }
  }

  @Test
  fun `SHOULD not send market events ON sendMarketEvents WHEN no externalIds for user`() {
    val userId = UUID.randomUUID().toString()

    val events = listOf(AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_1_DOLLAR)

    userService.mock({ fetchExternalIds(userId) }, null)

    runBlocking {
      service.sendMarketEvents(userId, events, repeatable = false)
    }

    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD not send market events ON sendMarketEvents WHEN no adjustId for user and event is not GA compatible`() {
    val userId = UUID.randomUUID().toString()

    val notGaEvent = AdMarketEvent.EventType.entries.find { !it.isGaS2s() }

    userService.mock(
      { fetchExternalIds(userId) },
      UserExternalIds(userId, "googleAdId", null, null, "firebaseAppId", null)
    )

    runBlocking {
      service.sendMarketEvents(userId, listOfNotNull(notGaEvent), repeatable = false)
    }

    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD do nothing ON sendMarketEvents WHEN all events already saved`() {
    val userId = UUID.randomUUID().toString()

    val events = listOf(AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_2_DOLLARS)

    userService.mock(
      { fetchExternalIds(userId) },
      UserExternalIds(userId, "googleAdId", null, "adjustId", "firebaseAppId", null)
    )

    adMarketPersistenceService.mock({ storeEvents(eq(userId), any()) }, emptyList())

    runBlocking {
      service.sendMarketEvents(userId, events, repeatable = false)
    }

    verifyBlocking(adMarketPersistenceService) {
      storeEvents(userId, events)
    }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD do nothing ON sendMarketEvents WHEN no events to send`() {
    runBlocking {
      service.sendMarketEvents("userId", emptyList(), repeatable = false)
    }

    verifyNoInteractions(messageBus)
    verifyNoInteractions(userService)
  }

  @Test
  fun `SHOULD send market moloco events ON sendMarketEvents`() {
    val userId = UUID.randomUUID().toString()

    val allEvents = listOf(
      AdMarketEvent.EventType.SC_WITH_REVENUE,
    )

    userService.mock(
      { fetchExternalIds(userId) },
      UserExternalIds(userId, "googleAdId", "idfa", "adjustId", "firebaseAppId", TrackingData(id = "idfv", type = IDFV, platform = IOS))
    )
    adjustService.mock({ getUserIp(userId) }, "UserIp")
    adjustService.mock({ getUserAgent(userId) }, "UserAgent")

    runBlocking {
      service.sendMarketEvents(
        userId = userId,
        events = allEvents,
        extraParams = mapOf("currency" to "USD", "value" to "1.23")
      )
    }

    allEvents.forEach { eventType ->
      verifyBlocking(messageBus) {
        publishAsync(
          SendMolocoInAppEventEffect(
            MolocoInAppEvent(
              eventType = eventType,
              ipAddress = "UserIp",
              userAgent = "UserAgent",
              idfa = "idfa",
              idfv = "idfv",
              platform = IOS,
              extraParams = mapOf("currency" to "USD", "value" to "1.23"),
              timestamp = now.toEpochMilli(),
            )
          )
        )
      }
    }
  }

  private companion object {
    private val now = Instant.now()
  }
}