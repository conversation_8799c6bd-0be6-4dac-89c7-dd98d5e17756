package com.moregames.playtime.tracking

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.justplayapps.service.rewarding.earnings.UserEarnings
import com.justplayapps.service.rewarding.earnings.UserEarningsService.Companion.ANDROID_NEW_USER_CUTOFF_DATE
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.TrackingDataType.IDFA
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.junit.MockExtension
import com.moregames.base.messaging.dto.AdMarketEvent
import com.moregames.base.messaging.dto.AdMarketEvent.EventType.*
import com.moregames.base.user.RevenueTotals
import com.moregames.base.util.*
import com.moregames.base.util.redis.SafeJedisClient
import com.moregames.playtime.app.userRevenueMinCacheTtlDays
import com.moregames.playtime.buseffects.GamesWithRevenueEventMessage
import com.moregames.playtime.buseffects.UserRevenueMinEventMessage
import com.moregames.playtime.games.GamePersistenceService
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.general.TrackedEventsPersistenceService
import com.moregames.playtime.general.dto.TrackedEvent
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutPeriodCounters
import com.moregames.playtime.user.cashout.CashoutPeriodsPersistenceService
import com.moregames.playtime.user.cashout.CashoutPersistenceService
import com.moregames.playtime.user.dto.UserExternalIds
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.util.defaultJsonConverter
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit

@OptIn(ExperimentalCoroutinesApi::class)
@ExtendWith(MockExtension::class)
class AdMarketEventComputationServiceTest(
  private val rewardingFacade: RewardingFacade,
  private val userService: UserService,
  private val adMarketService: AdMarketService,
  private val testScope: TestScope,
  private val timeService: TimeService,
  private val marketService: MarketService,
  private val gamesService: GamesService,
  private val messageBus: MessageBus,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val jedisClient: SafeJedisClient,
  private val gamePersistenceService: GamePersistenceService,
  private val trackedEventsPersistenceService: TrackedEventsPersistenceService,
  private val cashoutPeriodsPersistenceService: CashoutPeriodsPersistenceService,
  private val cashoutPersistenceService: CashoutPersistenceService,
) {

  private val json: Json = defaultJsonConverter
  
  private val underTest = AdMarketEventComputationService(
    rewardingFacade = rewardingFacade,
    userService = userService,
    adMarketService = adMarketService,
    coroutineScope = { testIoScope(testScope) },
    timeService = timeService,
    marketService = marketService,
    gamesService = gamesService,
    messageBus = messageBus,
    featureFlagsFacade = featureFlagsFacade, 
    jedisClient = jedisClient, 
    json = json,
    gamePersistenceService = gamePersistenceService,
    trackedEventsPersistenceService = trackedEventsPersistenceService,
    cashoutPeriodsPersistenceService = cashoutPeriodsPersistenceService,
    cashoutPersistenceService = cashoutPersistenceService,
  )

  @BeforeEach
  fun setUp() {
    userService.mock({ getUser(eq(USER_ID), any()) }, userDto.copy(createdAt = now, appPlatform = ANDROID))
    userService.mock({ userExists(USER_ID) }, true)
    timeService.mock( { now()}, now)
    rewardingFacade.mock({ getUserPerGameRevenue(USER_ID) }, emptyMap())
    gamesService.mock({ getGameId(ApplicationId.SOLITAIRE_VERSE_APP_ID, ANDROID) }, 40)
    gamesService.mock({ getGameId(ApplicationId.TREASURE_MASTER_APP_ID, ANDROID) }, 50)
    rewardingFacade.mock({ getRevenueTotals(USER_ID) }, revenueTotals(BigDecimal.ZERO))
    rewardingFacade.mock({ getTotalUsdEarningsForUser(USER_ID) }, BigDecimal.ZERO)
    rewardingFacade.mock({ loadUserEarningsForMetaId(1) }, UserEarnings(userId, 1, BigDecimal("1.0"), null))
    featureFlagsFacade.mock({ userRevenueMinCacheTtlDays() }, 1)
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(0, 0, 0))
  }

  @ParameterizedTest
  @ValueSource(doubles = [0.5, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0])
  fun `SHOULD send event ON computeSumAndSendDayOneReachedNRevenueEvent WHEN revenue is not null`(revenue: Double) = testScope.runTest {
    val oneDayAgo = now.minus(24, ChronoUnit.HOURS)
    userService.mock({ getUser(USER_ID) }, userDto.copy(createdAt = oneDayAgo))
    rewardingFacade.mock({ getRevenueSumForUserForTimeInterval(any(), any(), any()) }, BigDecimal(revenue))

    underTest.computeSumAndSendDayOneReachedNRevenueEvent(USER_ID)
    testScope.advanceUntilIdle()


    verifyBlocking(adMarketService, times(1)) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(AdMarketEvent.EventType.valueOf("DAY_1_REACHED_${if (revenue % 1 == 0.0) revenue.toInt().toString() else revenue.toString().replace(".", "")}")),
        repeatable = false
      )
    }
  }

  @Test
  fun `SHOULD not send event ON computeSumAndSendDayOneReachedNRevenueEvent WHEN revenue is below 0dot5`() = testScope.runTest {
    val oneDayAgo = now.minus(24, ChronoUnit.HOURS)

    userService.mock({ getUser(USER_ID) }, userDto.copy(createdAt = oneDayAgo))
    rewardingFacade.mock({ getRevenueSumForUserForTimeInterval(any(), any(), any()) }, BigDecimal("0.4"))

    underTest.computeSumAndSendDayOneReachedNRevenueEvent(USER_ID)
    testScope.advanceUntilIdle()

    verifyNoInteractions(adMarketService)
  }

  @ParameterizedTest
  @CsvSource(
    "0, ${ApplicationId.TREASURE_MASTER_APP_ID}",
    "3, ${ApplicationId.TREASURE_MASTER_APP_ID}",
    "5, ${ApplicationId.SOLITAIRE_VERSE_APP_ID}",
    "10, ${ApplicationId.WATER_SORTER_APP_ID}",
    "15, ${ApplicationId.SPACE_CONNECT_APP_ID}",
    "20, ${ApplicationId.MAD_SMASH_APP_ID}",
    "30, ${ApplicationId.PUZZLE_POP_BLASTER_APP_ID}",
    "35, ${ApplicationId.WORD_SEEKER_APP_ID}",
    "40, ${ApplicationId.TANGRAM_APP_ID}",
    "45, ${ApplicationId.TILE_MATCH_PRO_APP_ID}",
    "50, untracked",
  )
  fun `SHOULD create and send market events on computeAndSendProgressEvents`(progress: Int, applicationId: String) {
    runBlocking { underTest.computeAndSendProgressEvents(userId = USER_ID, applicationId = applicationId, progress = progress) }

    val triggeredEvents = when {
      applicationId == ApplicationId.TREASURE_MASTER_APP_ID && progress == 0 -> emptyList()
      applicationId == ApplicationId.TREASURE_MASTER_APP_ID && progress == 3 -> listOf(ACTION_BEAT_3_STAGES_TREASUREMASTER)
      applicationId == ApplicationId.SOLITAIRE_VERSE_APP_ID && progress == 5 -> listOf(ACTION_WIN_3_GAMES_SOLITAIRE, ACTION_WIN_5_GAMES_SOLITAIRE)
      applicationId == ApplicationId.WATER_SORTER_APP_ID && progress == 10 -> listOf(ACTION_COMPLETE_3_LEVELS_WATERSORTER, ACTION_COMPLETE_5_LEVELS_WATERSORTER, ACTION_COMPLETE_10_LEVELS_WATERSORTER)
      applicationId == ApplicationId.MAD_SMASH_APP_ID && progress == 20 -> listOf(ACTION_COMPLETE_3_LEVELS_MADSMASH, ACTION_COMPLETE_5_LEVELS_MADSMASH, ACTION_COMPLETE_10_LEVELS_MADSMASH, ACTION_COMPLETE_15_LEVELS_MADSMASH, ACTION_COMPLETE_20_LEVELS_MADSMASH)
      applicationId == ApplicationId.PUZZLE_POP_BLASTER_APP_ID && progress == 30 -> listOf(ACTION_COMPLETE_3_LEVELS_PUZZLEPOP, ACTION_COMPLETE_5_LEVELS_PUZZLEPOP, ACTION_COMPLETE_10_LEVELS_PUZZLEPOP, ACTION_COMPLETE_15_LEVELS_PUZZLEPOP, ACTION_COMPLETE_20_LEVELS_PUZZLEPOP)
      applicationId == ApplicationId.WORD_SEEKER_APP_ID && progress == 35 -> listOf(ACTION_COMPLETE_3_LEVELS_WORDSEEKER, ACTION_COMPLETE_5_LEVELS_WORDSEEKER, ACTION_COMPLETE_10_LEVELS_WORDSEEKER, ACTION_COMPLETE_15_LEVELS_WORDSEEKER, ACTION_COMPLETE_20_LEVELS_WORDSEEKER)
      applicationId == ApplicationId.TANGRAM_APP_ID && progress == 40 -> listOf(ACTION_COMPLETE_3_LEVELS_TANGRAMHEAVEN, ACTION_COMPLETE_5_LEVELS_TANGRAMHEAVEN, ACTION_COMPLETE_10_LEVELS_TANGRAMHEAVEN, ACTION_COMPLETE_15_LEVELS_TANGRAMHEAVEN, ACTION_COMPLETE_20_LEVELS_TANGRAMHEAVEN)
      applicationId == ApplicationId.TILE_MATCH_PRO_APP_ID && progress == 45 -> listOf(ACTION_COMPLETE_3_LEVELS_TILEMATCH, ACTION_COMPLETE_5_LEVELS_TILEMATCH, ACTION_COMPLETE_10_LEVELS_TILEMATCH, ACTION_COMPLETE_15_LEVELS_TILEMATCH, ACTION_COMPLETE_20_LEVELS_TILEMATCH)
      applicationId == "untracked" && progress == 50 -> emptyList()
      else -> emptyList()
    }

    if (triggeredEvents.isEmpty()) {
      verifyNoInteractions(adMarketService)
    } else {
      verifyBlocking(adMarketService) { sendMarketEvents(eq(USER_ID), eq(triggeredEvents), any(), any(), anyOrNull(), anyOrNull()) }
    }
  }

  @Test
  fun `SHOULD emit sc_with_revenue event ON computeAndSendSuccessfulCashoutEvents`() = testScope.runTest {
    underTest.computeAndSendSuccessfulCashoutEvents(USER_ID, BigDecimal("11.00"))
    advanceUntilIdle()

    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(SC_WITH_REVENUE),
        repeatable = true,
        extraParams = mapOf("currency" to "USD", "value" to "11.00"),
      )
    }
  }

  @Test
  fun `SHOULD emit adjust sc_with_revenue_partner event ON computeAndSendSuccessfulCashoutEvents`() = testScope.runTest {
    underTest.computeAndSendSuccessfulCashoutEvents(USER_ID, BigDecimal("11.00"))
    advanceUntilIdle()

    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(SC_WITH_REVENUE_PARTNER),
        repeatable = true,
        partnerParams = mapOf("sc_currency" to "USD", "sc_revenue" to "11.00")
      )
    }
  }

  @Test
  fun `SHOULD not emit adjust sc_with_revenue_partner event ON computeAndSendSuccessfulCashoutEvents when no revenue`() = testScope.runTest {
    underTest.computeAndSendSuccessfulCashoutEvents(USER_ID, BigDecimal.ZERO)
    advanceUntilIdle()

    verifyBlocking(adMarketService, never()) {
      sendMarketEvents(userId = eq(USER_ID), events = argThat { any { it == SC_WITH_REVENUE_PARTNER } }, any(), any(), anyOrNull(), anyOrNull())
    }
  }

  @ParameterizedTest
  @ValueSource(doubles = [0.5, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0])
  fun `SHOULD emit sc_reached_N and sc_reached_min_N events ON computeAndSendSuccessfulCashoutEvents WEHN user is younger than 7 days`(revenue: Double) = testScope.runTest {
    underTest.computeAndSendSuccessfulCashoutEvents(USER_ID, BigDecimal.valueOf(revenue))
    testScope.advanceUntilIdle()

    val scReachedMinXEventRevenueThresholds = listOf(
      BigDecimal(10.0) to SC_REACHED_MIN_10,
      BigDecimal(9.0) to SC_REACHED_MIN_9,
      BigDecimal(8.0) to SC_REACHED_MIN_8,
      BigDecimal(7.0) to SC_REACHED_MIN_7,
      BigDecimal(6.0) to SC_REACHED_MIN_6,
      BigDecimal(5.0) to SC_REACHED_MIN_5,
      BigDecimal(4.0) to SC_REACHED_MIN_4,
      BigDecimal(3.0) to SC_REACHED_MIN_3,
      BigDecimal(2.0) to SC_REACHED_MIN_2,
      BigDecimal(1.0) to SC_REACHED_MIN_1,
      BigDecimal(0.5) to SC_REACHED_MIN_0_5,
    )

    val expectedEvents = mutableListOf<AdMarketEvent.EventType>()
    expectedEvents.add(AdMarketEvent.EventType.valueOf("SC_REACHED_${if (revenue % 1 == 0.0) revenue.toInt().toString() else revenue.toString().replace(".", "_")}"))
    scReachedMinXEventRevenueThresholds.forEach { (revenueReached, event) ->
      if (revenueReached <= BigDecimal.valueOf(revenue)) {
        expectedEvents.add(event)
      }
    }

    val actualEventsCaptor = argumentCaptor<List<AdMarketEvent.EventType>>()
    verifyBlocking(adMarketService, times(3)) {
      sendMarketEvents(eq(USER_ID), actualEventsCaptor.capture(), any(), any(), anyOrNull(), anyOrNull())
    }

    val actualEvents = actualEventsCaptor.thirdValue
    assertThat(actualEvents.toSet()).isEqualTo(expectedEvents.toSet())
  }

  @Test
  fun `SHOULD emit only sc_reached_min_N ON computeAndSendSuccessfulCashoutEvents WHEN user is older than 7 days`() = testScope.runTest {
    userService.mock({ getUser(USER_ID, includingDeleted = true) }, userDto.copy(createdAt = now.minus(8, ChronoUnit.DAYS)))

    underTest.computeAndSendSuccessfulCashoutEvents(USER_ID, BigDecimal.ONE)
    testScope.advanceUntilIdle()
    val actualEventsCaptor = argumentCaptor<List<AdMarketEvent.EventType>>()
    verifyBlocking(adMarketService, times(3)) {
      sendMarketEvents(eq(USER_ID), actualEventsCaptor.capture(), any(), any(), anyOrNull(), anyOrNull())
    }

    val expectedEvents = listOf(SC_REACHED_MIN_0_5, SC_REACHED_MIN_1)

    val actualEvents = actualEventsCaptor.thirdValue
    assertThat(actualEvents.toSet()).isEqualTo(expectedEvents.toSet())
  }

  @Test
  fun `SHOULD not emit any cashout events ON computeAndSendSuccessfulCashoutEvents WHEN user is older than 30 days`() = testScope.runTest {
    userService.mock({ getUser(USER_ID, includingDeleted = true) }, userDto.copy(createdAt = now.minus(31, ChronoUnit.DAYS)))

    underTest.computeAndSendSuccessfulCashoutEvents(USER_ID, BigDecimal.ZERO)
    testScope.advanceUntilIdle()
    verifyNoInteractions(adMarketService)
  }

  @Test
  fun `SHOULD emit user_banned events WHEN computeAndSendUserBannedEvents is called`() = testScope.runTest {
    underTest.computeAndSendUserBannedEvents(USER_ID)
    testScope.advanceUntilIdle()

    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(USER_BANNED),
        repeatable = true,
        tracked = true,
      )
    }
  }

  @Test
  fun `SHOULD emit first game played events WHEN computeAndSendFirstGamePlayedEvents is called`() = runTest {
    underTest.computeAndSendFirstGamePlayedEvents(USER_ID, ApplicationId.SOLITAIRE_VERSE_APP_ID)

    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(FIRST_GAME_PLAYED_SOLITAIRE_VERSE),
        repeatable = false
      )
    }
  }

  @Test
  fun `SHOULD emit games with revenue events WHEN computeAndSendGameWithRevenueEvents is called`() = runTest {
    underTest.computeAndSendGameWithRevenueEvents(USER_ID, amount)

    verifyBlocking(adMarketService){
      sendMarketEvents(
        userId = USER_ID,
        repeatable = true,
        events = listOf(GAMES_WITH_REVENUE),
        extraParams = mapOf("value" to amount.toString()),
      )
    }
  }

  @Test
  fun `SHOULD emit offerwall with revenue events WHEN computeAndSendOfferwallWithRevenueEvents is called`() = runTest {
    underTest.computeAndSendOfferwallWithRevenueEvents(USER_ID, amount)

    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        repeatable = true,
        events = listOf(OW_WITH_REVENUE),
        extraParams = mapOf("currency" to "USD", "value" to amount.toString()),
      )
    }
  }

  @Test
  fun `SHOULD send user got specific revenue events WHEN `() = runTest {
    underTest.computeAndSendUserGotSpecificRevenueEvents(
      userId = USER_ID,
      amount = amount,
      amountWithoutEarnings = amountWithoutEarnings,
    )

    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        repeatable = true,
        events = listOf(USER_GOT_SPECIFIC_AMOUNT_REVENUE),
        extraParams = mapOf("currency" to "USD", "revenue" to amount.toString(), "value" to amount.toString()),
      )
    }
    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        repeatable = true,
        events = listOf(REVENUE_WITHOUT_EARNINGS),
        extraParams = mapOf("currency" to "USD", "revenue" to amountWithoutEarnings.toString(), "value" to amountWithoutEarnings.toString()),
      )
    }
  }

  @Test
  fun `SHOULD send base events ON computeAndSendRevenueEvents`() {
    rewardingFacade.mock(
      { getRevenueTotals(USER_ID) },
      RevenueTotals(
        revenue = BigDecimal(2.2),
        offerwallRevenue = BigDecimal.ZERO,
        day0Revenue = BigDecimal.ONE,
        day2Revenue = BigDecimal.ONE
      )
    )

    runTest {
      underTest.computeAndSendRevenueEvents(userDto, BigDecimal("2.10"), BigDecimal("2.10"))
    }

    verifyBlocking(messageBus) {
      publish(
        GamesWithRevenueEventMessage(
          userId = USER_ID,
          amount = BigDecimal("2.10"),
        )
      )
    }

    verifyBlocking(messageBus) {
      publish(
        UserRevenueMinEventMessage(
          userId = USER_ID,
          amount = BigDecimal("2.10"),
        )
      )
    }

    verifyNoMoreInteractions(messageBus)
  }

  @Test
  fun `SHOULD not publish revenue events ON computeAndSendRevenueEvents WHEN old Android user created before cutoff date but before the 7 days limit`() {
    whenever(timeService.now()).thenReturn(ANDROID_NEW_USER_CUTOFF_DATE)

    runTest {
      underTest.computeAndSendRevenueEvents(userDto.copy(id = USER_ID, googleAdId = null, appPlatform = ANDROID, createdAt = ANDROID_NEW_USER_CUTOFF_DATE.minus(8, ChronoUnit.DAYS)), BigDecimal("2.10"), BigDecimal("2.10"))
    }

    verifyBlocking(messageBus, never()) {
      publish(
        UserRevenueMinEventMessage(
          userId = USER_ID,
          amount = BigDecimal("2.37"),
        )
      )
    }
  }

  @Test
  fun `SHOULD send single_period_reached events on computeAndSendRevenueEvents for EU user`() = runTest {
    marketService.mock({ isEUCountry(any()) }, true)

    underTest.computeAndSendRevenueEvents(userDto, BigDecimal("2.37"), BigDecimal("2.37"))
    advanceUntilIdle()

    verifyBlocking(messageBus) {
      publish(
        UserRevenueMinEventMessage(
          userId = USER_ID,
          amount = BigDecimal("2.37"),
        )
      )
    }

    verifyBlocking(adMarketService, times(1)) {
      sendMarketEvents(
        USER_ID,
        listOf(EU_SINGLE_PERIOD_REACHED_0_1, EU_SINGLE_PERIOD_REACHED_0_2_5, SINGLE_PERIOD_REACHED_0_5, SINGLE_PERIOD_REACHED_1, SINGLE_PERIOD_REACHED_2),
        repeatable = false
      )
    }
  }

  @Test
  fun `SHOULD not send single_period_reached events on computeAndSendRevenueEvents for EU user AND too low revenue`() = runTest {
    marketService.mock({ isEUCountry(any()) }, true)

    underTest.computeAndSendRevenueEvents(userDto, BigDecimal("0.09"), BigDecimal("0.09"))
    advanceUntilIdle()

    verifyBlocking(messageBus) {
      publish(
        UserRevenueMinEventMessage(
          userId = USER_ID,
          amount = BigDecimal("0.09"),
        )
      )
    }

    verifyBlocking(adMarketService, times(1)) {
      sendMarketEvents(USER_ID, emptyList(), repeatable = false)
    }
  }

  @Test
  fun `SHOULD send single_period_reached events on computeAndSendRevenueEvents for not EU user`() = runTest {
    marketService.mock({ isEUCountry(any()) }, false)

    underTest.computeAndSendRevenueEvents(userDto, BigDecimal("2.37"), BigDecimal("2.37"))
    delay(50)

    verifyBlocking(messageBus) {
      publish(
        UserRevenueMinEventMessage(
          userId = USER_ID,
          amount = BigDecimal("2.37"),
        )
      )
    }

    verifyBlocking(adMarketService, times(1)) {
      sendMarketEvents(USER_ID, listOf(SINGLE_PERIOD_REACHED_0_5, SINGLE_PERIOD_REACHED_1, SINGLE_PERIOD_REACHED_2), repeatable = false)
    }
  }

  @ParameterizedTest
  @ValueSource(doubles = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0])
  fun `SHOULD emit solitaire_revenue_reached_min_X events ON computeAndSendRevenueEvents WHEN user is younger than 7 days`(revenue: Double) = runTest {
    marketService.mock({ isEUCountry(any()) }, true)
    rewardingFacade.mock({ getUserPerGameRevenue(USER_ID) }, mapOf(40 to BigDecimal(revenue)))

    underTest.computeAndSendRevenueEvents(userDto, BigDecimal("0.09"), BigDecimal("0.09"))

    val expectedEvents = (revenue.toInt() downTo 1).map { AdMarketEvent.EventType.valueOf("SOLITAIRE_REVENUE_REACHED_MIN_$it") }

    val actualEventsCaptor = argumentCaptor<List<AdMarketEvent.EventType>>()

    verifyBlocking(adMarketService) {
      sendMarketEvents(eq(USER_ID), actualEventsCaptor.capture(), any(), any(), anyOrNull(), anyOrNull())
    }

    val actualEvents = actualEventsCaptor.firstValue
    assertThat(expectedEvents.toSet()).isEqualTo(actualEvents.toSet())
    verifyNoMoreInteractions(adMarketService)
  }

  @ParameterizedTest
  @ValueSource(doubles = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0])
  fun `SHOULD emit treasuremaster_revenue_reached_min_X events ON computeAndSendRevenueEvents WHEN user is younger than 7 days`(revenue: Double) = runTest {
    marketService.mock({ isEUCountry(any()) }, true)
    rewardingFacade.mock({ getUserPerGameRevenue(USER_ID) }, mapOf(50 to BigDecimal(revenue)))

    underTest.computeAndSendRevenueEvents(userDto, BigDecimal("0.09"), BigDecimal("0.09"))

    val expectedEvents = (revenue.toInt() downTo 1).map { AdMarketEvent.EventType.valueOf("TREASUREMASTER_REVENUE_REACHED_MIN_$it") }

    val actualEventsCaptor = argumentCaptor<List<AdMarketEvent.EventType>>()
    verifyBlocking(adMarketService) {
      sendMarketEvents(eq(USER_ID), actualEventsCaptor.capture(), any(), any(), anyOrNull(), anyOrNull())
    }

    val actualEvents = actualEventsCaptor.firstValue
    assertThat(expectedEvents.toSet()).isEqualTo(actualEvents.toSet())
    verifyNoMoreInteractions(adMarketService)
  }

  @ParameterizedTest
  @ValueSource(doubles = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0])
  fun `SHOULD emit nothing WHEN computeAndSendRevenueEvents and user is older than 7 days`(revenue: Double) = runTest {
    marketService.mock({ isEUCountry(any()) }, true)
    rewardingFacade.mock({ getUserPerGameRevenue(USER_ID) }, mapOf(50 to BigDecimal(revenue)))

    underTest.computeAndSendRevenueEvents(userDto.copy(createdAt = now.minus(8, ChronoUnit.DAYS)), BigDecimal("0.09"), BigDecimal("0.09"))

    val expectedEvents = emptyList<AdMarketEvent.EventType>()

    val actualEventsCaptor = argumentCaptor<List<AdMarketEvent.EventType>>()
    verifyBlocking(adMarketService) {
      sendMarketEvents(eq(USER_ID), actualEventsCaptor.capture(), any(), any(), anyOrNull(), anyOrNull())
    }

    val actualEvents = actualEventsCaptor.firstValue
    assertThat(expectedEvents.toSet()).isEqualTo(actualEvents.toSet())

    verifyNoMoreInteractions(adMarketService)
  }

  @ParameterizedTest
  @ValueSource(doubles = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0])
  fun `SHOULD emit revenue_reached_min_X events ON computeAndSendRevenueEvents WHEN user is younger than 7 days`(revenue: Double) = runTest {
    rewardingFacade.mock({ getRevenueTotals(USER_ID) }, revenueTotals(revenue.toBigDecimal()))
    rewardingFacade.mock({ getTotalUsdEarningsForUser(USER_ID) }, BigDecimal.ONE)
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, googleAdId = "googleAdId", createdAt = now))

    underTest.computeAndSendRevenueEvents(userDto, BigDecimal("0.09"), BigDecimal("0.09"))

    val expectedEvents = (revenue.toInt() downTo 1).flatMap { value ->
      listOfNotNull(
        AdMarketEvent.EventType.valueOf("REVENUE_REACHED_MIN_$value"),
        if (value > 1) AdMarketEvent.EventType.valueOf("NET_REVENUE_REACHED_MIN_${value - 1}") else null,
        AdMarketEvent.EventType.valueOf("OW_REVENUE_REACHED_MIN_$value"),
      )
    }

    val actualEventsCaptor = argumentCaptor<List<AdMarketEvent.EventType>>()
    verifyBlocking(adMarketService) {
      sendMarketEvents(eq(USER_ID), actualEventsCaptor.capture(), any(), any(), anyOrNull(), anyOrNull())
    }

    val actualEvents = actualEventsCaptor.firstValue
    assertThat(actualEvents.toSet()).isEqualTo(expectedEvents.toSet())
    verifyNoMoreInteractions(adMarketService)
  }

  @Test
  fun `SHOULD cache data and send nothing ON computeAndSendUserRevenueMinEvents WHEN amount is not enough big`() {
    val userTrackingData = TrackingData(id = "trackingId", type = IDFA, platform = ANDROID)
    userService.mock({ fetchExternalIds(USER_ID) }, UserExternalIds(USER_ID, "googleAdId", "idfa", "adjustId", "firebaseAppId", userTrackingData))

    jedisClient.mock(
      { get("UserRevenueMinX:${USER_ID}") },
      json.encodeToString(UserRevenueMinX(min1 = BigDecimal.ZERO, min2 = BigDecimal.ZERO, min3 = BigDecimal.ZERO))
    )
    runBlocking {
      underTest.computeAndSendUserRevenueMinEvents(userId = USER_ID, amount = AMOUNT_01)
    }

    verifyBlocking(jedisClient) {
      set(
        eq("UserRevenueMinX:$USER_ID"),
        eq(json.encodeToString(UserRevenueMinX(min1 = AMOUNT_01, min2 = AMOUNT_01, min3 = AMOUNT_01))),
        argThat { this.toString() == "[ex, 86400]" }
      )
    }

    verifyNoInteractions(adMarketService)
  }

  @Test
  fun `SHOULD cache data and send min_1 ON computeAndSendUserRevenueMinEvents WHEN min_1 is stored `() {
    val userTrackingData = TrackingData(id = "trackingId", type = IDFA, platform = ANDROID)
    userService.mock({ fetchExternalIds(USER_ID) }, UserExternalIds(USER_ID, "googleAdId", "idfa", "adjustId", "firebaseAppId", userTrackingData))
    jedisClient.mock(
      { get("UserRevenueMinX:${USER_ID}") },
      json.encodeToString(UserRevenueMinX(min1 = BigDecimal("0.9"), min2 = BigDecimal.ZERO, min3 = BigDecimal.ZERO))
    )
    runBlocking {
      underTest.computeAndSendUserRevenueMinEvents(userId = USER_ID, amount = AMOUNT_01)
    }

    verifyBlocking(jedisClient) {
      set(
        eq("UserRevenueMinX:$USER_ID"),
        eq(json.encodeToString(UserRevenueMinX(min1 = BigDecimal.ZERO, min2 = AMOUNT_01, min3 = AMOUNT_01))),
        argThat { this.toString() == "[ex, 86400]" }
      )
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(AdMarketEvent.EventType.USER_REVENUE_MIN_1),
        repeatable = true,
        extraParams = mapOf("value" to BigDecimal("1.0").toString()),
      )
    }
    verifyNoMoreInteractions(adMarketService)
  }

  @Test
  fun `SHOULD cache data and send min_2 ON computeAndSendUserRevenueMinEvents WHEN min_2 is stored `() {
    val userTrackingData = TrackingData(id = "trackingId", type = IDFA, platform = ANDROID)
    userService.mock({ fetchExternalIds(USER_ID) }, UserExternalIds(USER_ID, "googleAdId", "idfa", "adjustId", "firebaseAppId", userTrackingData))
    jedisClient.mock(
      { get("UserRevenueMinX:${USER_ID}") },
      json.encodeToString(UserRevenueMinX(min1 = BigDecimal.ZERO, min2 = BigDecimal("1.9"), min3 = BigDecimal.ZERO))
    )
    runBlocking {
      underTest.computeAndSendUserRevenueMinEvents(userId = USER_ID, amount = AMOUNT_01)
    }

    verifyBlocking(jedisClient) {
      set(
        eq("UserRevenueMinX:$USER_ID"),
        eq(json.encodeToString(UserRevenueMinX(min1 = AMOUNT_01, min2 = BigDecimal.ZERO, min3 = AMOUNT_01))),
        argThat { this.toString() == "[ex, 86400]" }
      )
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(AdMarketEvent.EventType.USER_REVENUE_MIN_2),
        repeatable = true,
        extraParams = mapOf("value" to BigDecimal("2.0").toString()),
      )
    }
    verifyNoMoreInteractions(adMarketService)
  }

  @Test
  fun `SHOULD cache data and send min_3 ON computeAndSendUserRevenueMinEvents WHEN min_3 is stored `() {
    val userTrackingData = TrackingData(id = "trackingId", type = IDFA, platform = ANDROID)
    userService.mock({ fetchExternalIds(USER_ID) }, UserExternalIds(USER_ID, "googleAdId", "idfa", "adjustId", "firebaseAppId", userTrackingData))
    jedisClient.mock(
      { get("UserRevenueMinX:${USER_ID}") },
      json.encodeToString(UserRevenueMinX(min1 = BigDecimal.ZERO, min2 = BigDecimal.ZERO, min3 = BigDecimal("2.9")))
    )
    runBlocking {
      underTest.computeAndSendUserRevenueMinEvents(userId = USER_ID, amount = AMOUNT_01)
    }

    verifyBlocking(jedisClient) {
      set(
        eq("UserRevenueMinX:$USER_ID"),
        eq(json.encodeToString(UserRevenueMinX(min1 = AMOUNT_01, min2 = AMOUNT_01, min3 = BigDecimal.ZERO))),
        argThat { this.toString() == "[ex, 86400]" }
      )
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(AdMarketEvent.EventType.USER_REVENUE_MIN_3),
        repeatable = true,
        extraParams = mapOf("value" to BigDecimal("3.0").toString()),
      )
    }
    verifyNoMoreInteractions(adMarketService)
  }

  @Test
  fun `SHOULD cache data and send all events ON computeAndSendUserRevenueMinEvents WHEN all events are stored `() {
    val userTrackingData = TrackingData(id = "trackingId", type = IDFA, platform = ANDROID)
    userService.mock({ fetchExternalIds(USER_ID) }, UserExternalIds(USER_ID, "googleAdId", "idfa", "adjustId", "firebaseAppId", userTrackingData))
    jedisClient.mock(
      { get("UserRevenueMinX:${USER_ID}") },
      json.encodeToString(UserRevenueMinX(min1 = BigDecimal("0.9"), min2 = BigDecimal("1.9"), min3 = BigDecimal("2.9")))
    )
    runBlocking {
      underTest.computeAndSendUserRevenueMinEvents(userId = USER_ID, amount = AMOUNT_01)
    }

    verifyBlocking(jedisClient) {
      set(
        eq("UserRevenueMinX:$USER_ID"),
        eq(json.encodeToString(UserRevenueMinX(min1 = BigDecimal.ZERO, min2 = BigDecimal.ZERO, min3 = BigDecimal.ZERO))),
        argThat { this.toString() == "[ex, 86400]" }
      )
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(AdMarketEvent.EventType.USER_REVENUE_MIN_1),
        repeatable = true,
        extraParams = mapOf("value" to BigDecimal("1.0").toString()),
      )
    }
    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(AdMarketEvent.EventType.USER_REVENUE_MIN_2),
        repeatable = true,
        extraParams = mapOf("value" to BigDecimal("2.0").toString()),
      )
    }
    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(AdMarketEvent.EventType.USER_REVENUE_MIN_3),
        repeatable = true,
        extraParams = mapOf("value" to BigDecimal("3.0").toString()),
      )
    }
    verifyNoMoreInteractions(adMarketService)
  }

  @Test
  fun `SHOULD cache data and send all events ON computeAndSendUserRevenueMinEvents WHEN all events are zero `() {
    val userTrackingData = TrackingData(id = "trackingId", type = IDFA, platform = ANDROID)
    userService.mock({ fetchExternalIds(USER_ID) }, UserExternalIds(USER_ID, "googleAdId", "idfa", "adjustId", "firebaseAppId", userTrackingData))
    jedisClient.mock(
      { get("UserRevenueMinX:${USER_ID}") },
      json.encodeToString(UserRevenueMinX(min1 = BigDecimal.ZERO, min2 = BigDecimal.ZERO, min3 = BigDecimal.ZERO))
    )
    runBlocking {
      underTest.computeAndSendUserRevenueMinEvents(userId = USER_ID, amount = AMOUNT_4)
    }

    verifyBlocking(jedisClient) {
      set(
        eq("UserRevenueMinX:$USER_ID"),
        eq(json.encodeToString(UserRevenueMinX(min1 = BigDecimal.ZERO, min2 = BigDecimal.ZERO, min3 = BigDecimal.ZERO))),
        argThat { this.toString() == "[ex, 86400]" }
      )
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(AdMarketEvent.EventType.USER_REVENUE_MIN_1),
        repeatable = true,
        extraParams = mapOf("value" to "4"),
      )
    }
    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(AdMarketEvent.EventType.USER_REVENUE_MIN_2),
        repeatable = true,
        extraParams = mapOf("value" to "4"),
      )
    }
    verifyBlocking(adMarketService) {
      sendMarketEvents(
        userId = USER_ID,
        events = listOf(AdMarketEvent.EventType.USER_REVENUE_MIN_3),
        repeatable = true,
        extraParams = mapOf("value" to "4"),
      )
    }
    verifyNoMoreInteractions(adMarketService)
  }

  @ParameterizedTest
  @ValueSource(ints = [0, 1, 45])
  fun `SHOULD do nothing ON computeAndSendTimePlayedEvents WHEN banners shown counter is not from a thresholds list`(bannersShown: Int) {
    val gameId = 1

    testScope.runTest { underTest.computeAndSendTimePlayedEvents(userId, gameId, bannersShown) }

    verifyNoInteractions(gamePersistenceService)
  }

  @ParameterizedTest
  @ValueSource(strings = ["too old", "banned", "not exists", "matching"])
  fun `SHOULD do nothing ON computeAndSendTimePlayedEvents WHEN user does not exist, too old or banned`(option: String) {
    timeService.mock({ now() }, now)
    gamePersistenceService.mock({ getGamesOrderedByFirstTimeBannerShow(userId) }, listOf(1 to now))
    when (option) {
      "too old" -> userService.mock({ getUser(userId) }, userDto.copy(createdAt = now.minus(31, ChronoUnit.DAYS)))
      "banned" -> userService.mock({ getUser(userId) }, userDto.copy(isBanned = true))
      "not exists" -> userService.mock({ userExists(userId) }, false)
    }

    testScope.runTest { underTest.computeAndSendTimePlayedEvents(userId, 1, 120) }

    val event = FIRST_GAME_PLAYED_2MIN
    val trackedEvents = bothPlatformsEvents(event)
    if (option != "matching") {
      verifyNoInteractions(gamePersistenceService)
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = true)
    } else {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    }

  }

  @Test
  fun `SHOULD do not add ad market events ON computeAndSendTimePlayedEvents WHEN it is a 6th played game`() {
    val gameId = 1
    gamePersistenceService.mock(
      { getGamesOrderedByFirstTimeBannerShow(userId) },
      listOf(2 to now, 3 to now, 4 to now, 5 to now, 6 to now, 1 to now)
    ) //game1 is the 6th played game

    testScope.runTest { underTest.computeAndSendTimePlayedEvents(userId, gameId, 120) }

    verifyBlocking(gamePersistenceService) { getGamesOrderedByFirstTimeBannerShow(userId) }
    verifyNoInteractions(trackedEventsPersistenceService, adMarketService)
  }

  @ParameterizedTest
  @CsvSource("120, 1st", "300, 1st", "300, 1st", "120, 3rd", "300, 3rd", "600, 3rd", "1200, 3rd", "1800, 3rd", "2400, 3rd")
  fun `SHOULD add ad market events ON computeAndSendTimePlayedEvents WHEN it is the 1st played game for Android`(timeElapsed: Int, prefix: String) {
    val gameId = if (prefix == "1st") 1 else 3
    gamePersistenceService.mock({ getGamesOrderedByFirstTimeBannerShow(userId) }, listOf(1 to now, 2 to now, 3 to now))

    testScope.runTest { underTest.computeAndSendTimePlayedEvents(userId, gameId, timeElapsed) }

    val event = when {
      timeElapsed == 120 && gameId == 1 -> FIRST_GAME_PLAYED_2MIN
      timeElapsed == 300 && gameId == 1 -> FIRST_GAME_PLAYED_5MIN
      timeElapsed == 600 && gameId == 1 -> FIRST_GAME_PLAYED_10MIN
      timeElapsed == 1200 && gameId == 1 -> FIRST_GAME_PLAYED_20MIN
      timeElapsed == 1800 && gameId == 1 -> FIRST_GAME_PLAYED_30MIN
      timeElapsed == 2400 && gameId == 1 -> FIRST_GAME_PLAYED_40MIN
      timeElapsed == 120 && gameId == 3 -> THIRD_GAME_PLAYED_2MIN
      timeElapsed == 300 && gameId == 3 -> THIRD_GAME_PLAYED_5MIN
      timeElapsed == 600 && gameId == 3 -> THIRD_GAME_PLAYED_10MIN
      timeElapsed == 1200 && gameId == 3 -> THIRD_GAME_PLAYED_20MIN
      timeElapsed == 1800 && gameId == 3 -> THIRD_GAME_PLAYED_30MIN
      timeElapsed == 2400 && gameId == 3 -> THIRD_GAME_PLAYED_40MIN
      else -> return
    }
    val trackedEvents = bothPlatformsEvents(event)
    verifyEventsWerePropagatedForSending(trackedEvents, event)
  }

  @ParameterizedTest
  @CsvSource("120, 1st", "300, 1st", "300, 1st", "120, 3rd", "300, 3rd", "600, 3rd", "1200, 3rd", "1800, 3rd", "2400, 3rd")
  fun `SHOULD add ad market events ON computeAndSendTimePlayedEvents WHEN it is the 1st played game for IOS`(timeElapsed: Int, prefix: String) {
    userService.mock({ getUser(userId) }, userDtoStub.copy(appPlatform = AppPlatform.IOS, createdAt = now))
    val gameId = if (prefix == "1st") 1 else 3
    gamePersistenceService.mock({ getGamesOrderedByFirstTimeBannerShow(userId) }, listOf(1 to now, 2 to now, 3 to now))

    testScope.runTest { underTest.computeAndSendTimePlayedEvents(userId, gameId, timeElapsed) }

    val eventName = when {
      timeElapsed == 120 && gameId == 1 -> FIRST_GAME_PLAYED_2MIN
      timeElapsed == 300 && gameId == 1 -> FIRST_GAME_PLAYED_5MIN
      timeElapsed == 600 && gameId == 1 -> FIRST_GAME_PLAYED_10MIN
      timeElapsed == 1200 && gameId == 1 -> FIRST_GAME_PLAYED_20MIN
      timeElapsed == 1800 && gameId == 1 -> FIRST_GAME_PLAYED_30MIN
      timeElapsed == 2400 && gameId == 1 -> FIRST_GAME_PLAYED_40MIN
      timeElapsed == 120 && gameId == 3 -> THIRD_GAME_PLAYED_2MIN
      timeElapsed == 300 && gameId == 3 -> THIRD_GAME_PLAYED_5MIN
      timeElapsed == 600 && gameId == 3 -> THIRD_GAME_PLAYED_10MIN
      timeElapsed == 1200 && gameId == 3 -> THIRD_GAME_PLAYED_20MIN
      timeElapsed == 1800 && gameId == 3 -> THIRD_GAME_PLAYED_30MIN
      timeElapsed == 2400 && gameId == 3 -> THIRD_GAME_PLAYED_40MIN
      else -> return
    }

    when (timeElapsed) {
      120, 300, 600 -> {
        val trackedEvents = bothPlatformsEvents(eventName)
        verifyEventsWerePropagatedForSending(trackedEvents, eventName)
      }

      1200, 1800, 2400 -> {
        verifyNoInteractions(trackedEventsPersistenceService, adMarketService)
      }
    }
  }

  @ParameterizedTest
  @CsvSource(
    "0, ${ApplicationId.SOLITAIRE_VERSE_APP_ID}",
    "300, ${ApplicationId.SOLITAIRE_VERSE_APP_ID}",
    "600, ${ApplicationId.TREASURE_MASTER_APP_ID}",
    "900, ${ApplicationId.MAD_SMASH_APP_ID}",
    "1200, ${ApplicationId.BUBBLE_POP_APP_ID}",
    "1800, ${ApplicationId.WATER_SORTER_APP_ID}",
    "2400, untracked"
  )
  fun `SHOULD add ad market events ON computeAndSendTimePlayedEvents WHEN it is the particular game being played on Android`(timeElapsed: Int, application: String) {
    val gameId = 1
    gamesService.mock({ getGameId(application, userDtoStub.appPlatform) }, gameId)

    gamePersistenceService.mock({ getGamesOrderedByFirstTimeBannerShow(userId) }, listOf(1 to now, 2 to now, 3 to now))

    testScope.runTest { underTest.computeAndSendTimePlayedEvents(userId, gameId, timeElapsed) }

    val triggeredEvents = when {
      application == ApplicationId.SOLITAIRE_VERSE_APP_ID && timeElapsed == 0 -> emptyList()
      application == ApplicationId.SOLITAIRE_VERSE_APP_ID && timeElapsed == 300 -> listOf(FIRST_GAME_PLAYED_5MIN, PLAY_SOLITAIRE_5_MIN)
      application == ApplicationId.TREASURE_MASTER_APP_ID && timeElapsed == 600 -> listOf(FIRST_GAME_PLAYED_10MIN, PLAY_TREASUREMASTER_10_MIN, PLAY_TREASUREMASTER_5_MIN)
      application == ApplicationId.MAD_SMASH_APP_ID && timeElapsed == 900 -> listOf(PLAY_MADSMASH_15_MIN, PLAY_MADSMASH_10_MIN, PLAY_MADSMASH_5_MIN)
      application == ApplicationId.BUBBLE_POP_APP_ID && timeElapsed == 1200 -> listOf(FIRST_GAME_PLAYED_20MIN, PLAY_BUBBLEPOP_15_MIN, PLAY_BUBBLEPOP_10_MIN, PLAY_BUBBLEPOP_5_MIN)
      application == ApplicationId.WATER_SORTER_APP_ID && timeElapsed == 1800 -> listOf(FIRST_GAME_PLAYED_30MIN, PLAY_WATERSORTER_15_MIN, PLAY_WATERSORTER_10_MIN, PLAY_WATERSORTER_5_MIN)
      application == "untracked" && timeElapsed == 2400 -> listOf(FIRST_GAME_PLAYED_40MIN)
      else -> emptyList()
    }

    if (triggeredEvents.isEmpty()) {
      verifyNoInteractions(trackedEventsPersistenceService, adMarketService)
    } else {
      val trackedEvents = triggeredEvents.flatMap { bothPlatformsEvents(it) }
      verifyEventsWerePropagatedForSending(trackedEvents, triggeredEvents)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less day2 revenue", "no 4 day retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 3$ of revenue by day 2 and day4 retention`(option: String) {
    val event = EARNED_WITHIN_2_DAYS_3_DOLLARS_RD4
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
    when (option) {
      "less day2 revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("2.99"), BigDecimal.ZERO)
      )

      "no 4 day retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = true)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less day2 revenue", "no 5 day retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 3$ of revenue by day 2 and day5 retention`(option: String) {
    val event = EARNED_WITHIN_2_DAYS_3_DOLLARS_RD5
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    when (option) {
      "less day2 revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("2.99"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = (option == "less day2 revenue"))
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less day2 revenue", "no 6 day retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 4$ of revenue by day 2 and day6 retention`(option: String) {
    val event = EARNED_WITHIN_2_DAYS_4_DOLLARS_RD6
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("4.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    when (option) {
      "less day2 revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("3.99"), BigDecimal.ZERO)
      )

      "no 6 day retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less day2 revenue", "no 5 day retention", "no successful cashouts"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 2$ of revenue by day 2 and day5 retention and successful cashout`(option: String) {
    val event = EARNED_WITHIN_2_DAYS_2_DOLLARS_RD5_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less day2 revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
      "no successful cashouts" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = option == "no successful cashouts")
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less day2 revenue", "no 4 day retention", "no successful cashouts"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 4$ of revenue by day 2 and day4 retention and successful cashout`(option: String) {
    val event = EARNED_WITHIN_2_DAYS_4_DOLLARS_RD4_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("5.0"), BigDecimal.ZERO, BigDecimal("4.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less day2 revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("5.0"), BigDecimal.ZERO, BigDecimal("3.99"), BigDecimal.ZERO)
      )

      "no 4 day retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
      "no successful cashouts" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less day2 revenue", "no 5 day retention", "no successful cashouts"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 3$ of revenue by day 2 and day5 retention and successful cashout`(option: String) {
    val event = EARNED_WITHIN_2_DAYS_3_DOLLARS_RD5_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("5.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less day2 revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("5.0"), BigDecimal.ZERO, BigDecimal("2.99"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
      "no successful cashouts" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no 5 day retention", "no successful cashouts", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 6$ of revenue by day 6 and day5 retention and successful cashout`(option: String) {
    val event = EARNED_WITHIN_6_DAYS_6_DOLLARS_RD5_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("6.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("5.0"), BigDecimal.ZERO, BigDecimal("3.00"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
      "no successful cashouts" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "too late" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no 5 day retention", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 6$ of revenue by day 6 and day5 retention`(option: String) {
    val event = EARNED_WITHIN_6_DAYS_6_DOLLARS_RD5
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("6.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("5.0"), BigDecimal.ZERO, BigDecimal("3.00"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no 5 day retention", "no successful cashouts", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 8$ of revenue by day 6 and day5 retention and successful cashout`(option: String) {
    val event = EARNED_WITHIN_6_DAYS_8_DOLLARS_RD5_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("8.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("7.99"), BigDecimal.ZERO, BigDecimal("3.00"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
      "no successful cashouts" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no 5 day retention", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 8$ of revenue by day 6 and day5 retention`(option: String) {
    val event = EARNED_WITHIN_6_DAYS_8_DOLLARS_RD5
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("8.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("7.99"), BigDecimal.ZERO, BigDecimal("3.00"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no 5 day retention", "no successful cashouts", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 4$ of revenue by day 6 and day5 retention and successful cashout`(option: String) {
    val event = EARNED_WITHIN_6_DAYS_4_DOLLARS_RD5_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("3.99"), BigDecimal.ZERO, BigDecimal("3.00"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
      "no successful cashouts" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less active cps"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has 10 or more active cashout periods`(option: String) {
    val event = TEN_ACTIVE_CPS
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 10))
    when (option) {
      "less active cps" -> cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 9))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = true)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less active cps", "no successful cashout"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has 10 or more active cashout periods and successful cashout`(option: String) {
    val event = TEN_ACTIVE_CPS_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 10))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less active cps" -> cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 9))
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less active cps", "no successful cashout", "no day4 retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has 10 or more active cashout periods and successful cashout and day4 retention`(option: String) {
    val event = TEN_ACTIVE_CPS_RD4_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 10))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less active cps" -> cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 9))
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "no day4 retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "less active cps", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 3$ of revenue by day 2 and 10 active cashout periods`(option: String) {
    val event = EARNED_WITHIN_2_DAYS_3_DOLLARS_10ACP
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("3.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 10))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("2.99"), BigDecimal.ZERO, BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "less active cps" -> cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 9))
      "too late" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "less active cps", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 4$ of revenue by day 2 and 9 active cashout periods`(option: String) {
    val event = EARNED_WITHIN_2_DAYS_4_DOLLARS_9ACP
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 9))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("3.99"), BigDecimal.ZERO, BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "less active cps" -> cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 8))
      "too late" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = (option == "too late"))
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "less active cps"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 6$ of revenue by day 6 and 9 active cashout periods`(option: String) {
    val event = EARNED_WITHIN_6_DAYS_6_DOLLARS_9ACP
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("6.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 9))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("5.99"), BigDecimal.ZERO, BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "less active cps" -> cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 8))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = (option == "less revenue"))
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no ofw revenue", "no day3 retention", "no successful cashout", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 4$ of revenue by day 6 and some ofw revenue and successful cashout and day3 retention`(option: String) {
    val event = EARNED_WITHIN_6_DAYS_4_DOLLARS_RD3_OFW_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal("0.01"), BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("3.99"), BigDecimal("0.01"), BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "no ofw revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO)
      )

      "no day3 retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no ofw revenue", "no day3 retention", "no successful cashout", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 6$ of revenue by day 6 and some ofw revenue and successful cashout and day3 retention`(option: String) {
    val event = EARNED_WITHIN_6_DAYS_6_DOLLARS_RD3_OFW_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("6.0"), BigDecimal("0.01"), BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("5.99"), BigDecimal("0.01"), BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "no ofw revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("6.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO)
      )

      "no day3 retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no ofw revenue", "no day3 retention", "no successful cashout", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 8$ of revenue by day 6 and some ofw revenue and successful cashout and day3 retention`(option: String) {
    val event = EARNED_WITHIN_6_DAYS_8_DOLLARS_RD3_OFW_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("8.0"), BigDecimal("0.01"), BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("7.99"), BigDecimal("0.01"), BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "no ofw revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("6.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO)
      )

      "no day3 retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no ofw revenue", "no day3 retention", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 4$ of revenue by day 6 and some ofw revenue and day3 retention`(option: String) {
    val event = EARNED_WITHIN_6_DAYS_4_DOLLARS_RD3_OFW
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal("0.01"), BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("3.99"), BigDecimal("0.01"), BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "no ofw revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO)
      )

      "no day3 retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = (option == "no ofw revenue"))
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no successful cashout", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 4$ of ofw revenue by day 6 and successful cashout`(option: String) {
    val event = EARNED_WITHIN_6_DAYS_4_OFW_DOLLARS_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal("4.00"), BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("15.99"), BigDecimal("3.99"), BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "too late" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has got 6$ of ofw revenue by day 6`(option: String) {
    val event = EARNED_WITHIN_6_DAYS_6_OFW_DOLLARS
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("6.0"), BigDecimal("6.00"), BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("15.99"), BigDecimal("3.99"), BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "too late" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "no ofw revenue"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user have got any of ofw revenue`(option: String) {
    val event = EARNED_ANY_OFW_DOLLARS
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal("0.01"), BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(8, ChronoUnit.DAYS)))
    when (option) {
      "no ofw revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO)
      )
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = true)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "no successful cashout", "no day4 retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has a successful cashout and day4 retention`(option: String) {
    val event = RD4_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "no day4 retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = option == "no successful cashout")
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "no successful cashout", "no day3 retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has a successful cashout and day3 retention`(option: String) {
    val event = RD3_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "no day3 retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = option == "no successful cashout")
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "no successful cashout", "no day2 retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has a successful cashout and day2 retention`(option: String) {
    val event = RD2_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "no day2 retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(1, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = option == "no successful cashout")
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "no successful cashout", "no day1 retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has a successful cashout and day1 retention`(option: String) {
    val event = RD1_SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(1, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "no day1 retention" -> userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(0, ChronoUnit.DAYS)))
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = option == "no successful cashout")
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "no successful cashout"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user has a successful cashout`(option: String) {
    val event = SC
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(1, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    val trackedEvents = bothPlatformsEvents(event)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, event)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = true)
      verifySendMarketEventsWasNotTriggeredFor(event)
    }
  }

  @ParameterizedTest
  @ValueSource(ints = [1, 2, 3, 4, 5])
  fun `SHOULD create tracked events ON computeAndSendEarningCalculatedEvents WHEN user earn 1-4$ in 2 days`(revenue: Int) {
    val userRevenue = BigDecimal(revenue)
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }
    val trackedEvents = mutableListOf<TrackedEvent>()
    trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_1_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_2_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_5_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_75_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))

    repeat(4.coerceAtMost(revenue)) { i ->
      addTrackedEventsForAllPlatforms(userId = userId, event = AdMarketEvent.EventType.valueOf("EARNED_WITHIN_2_DAYS_${i + 1}_DOLLAR${"S".takeIf { i != 0 }.orEmpty()}"), trackedEventList = trackedEvents)
    }
    trackedEvents.add(TrackedEvent(userId = userId, event = FB_MOBILE_LEVEL_ACHIEVED, platformsToTrack = TrackedEvent.EventTrackingPlatform.FACEBOOK))
    val adMarketEvents = trackedEvents
      .filter { it.platformsToTrack == TrackedEvent.EventTrackingPlatform.FIREBASE }
      .mapNotNull { trackedEvent -> AdMarketEvent.EventType.entries.find { it.eventName == trackedEvent.eventName } }

    verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }
    verifyBlocking(adMarketService) {
      sendMarketEvents(userId, adMarketEvents, repeatable = false) }

    verifyNoMoreInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(longs = [3, 4, 5])
  fun `SHOULD NOT create tracked events ON computeAndSendEarningCalculatedEvents WHEN user earn 4$ in 3+ days`(days: Long) {
    val userRevenue = BigDecimal("3.0")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(userId, emptyList(), repeatable = false) }
    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
  fun `SHOULD create tracked events ON computeAndSendEarningCalculatedEvents WHEN user earn 8$ in 1-10 days`(days: Long) {
    val userRevenue = BigDecimal("8.0")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }

    val trackedEvents = mutableListOf<TrackedEvent>()
    val adMarketEvents = mutableListOf<AdMarketEvent.EventType>()
    if (days <= 2) {
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_1_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_2_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_5_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_75_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      repeat(4) { i ->
        addTrackedEventsForAllPlatforms(userId = userId, event = AdMarketEvent.EventType.valueOf("EARNED_WITHIN_2_DAYS_${i + 1}_DOLLAR${"S".takeIf { i != 0 }.orEmpty()}"), trackedEventList = trackedEvents)
      }
      trackedEvents.add(TrackedEvent(userId = userId, event = FB_MOBILE_LEVEL_ACHIEVED, platformsToTrack = TrackedEvent.EventTrackingPlatform.FACEBOOK))
    }
    if (days <= 8) {
      addTrackedEventsForAllPlatforms(userId = userId, event = EARNED_WITHIN_8_DAYS_8_DOLLARS, trackedEventList = trackedEvents)
    }
    if (days in 5..6) {
      addTrackedEventsForAllPlatforms(userId = userId, event = EARNED_WITHIN_6_DAYS_6_DOLLARS_RD5, trackedEventList = trackedEvents)
      addTrackedEventsForAllPlatforms(userId = userId, event = EARNED_WITHIN_6_DAYS_8_DOLLARS_RD5, trackedEventList = trackedEvents)
    }

    adMarketEvents.addAll(
      trackedEvents
        .filter { it.platformsToTrack == TrackedEvent.EventTrackingPlatform.FIREBASE }
        .mapNotNull { trackedEvent -> AdMarketEvent.EventType.entries.find { it.eventName == trackedEvent.eventName } }
    )

    if (trackedEvents.size > 0) {
      verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }
      verifyBlocking(adMarketService) {
        sendMarketEvents(userId, adMarketEvents, repeatable = false) }
    }

    verifyNoMoreInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
  fun `SHOULD create tracked events ON computeAndSendEarningCalculatedEvents WHEN user earn 13$ in 1-10 days`(days: Long) {
    val userRevenue = BigDecimal("13.0")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }
    val trackedEvents = mutableListOf<TrackedEvent>()
    val adMarketEvents = mutableListOf<AdMarketEvent.EventType>()
    if (days <= 2) {
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_1_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_2_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_5_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_75_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      repeat(4) { i ->
        addTrackedEventsForAllPlatforms(userId = userId, event = AdMarketEvent.EventType.valueOf("EARNED_WITHIN_2_DAYS_${i + 1}_DOLLAR${"S".takeIf { i != 0 }.orEmpty()}"), trackedEventList = trackedEvents)
      }
      trackedEvents.add(TrackedEvent(userId = userId, event = FB_MOBILE_LEVEL_ACHIEVED, platformsToTrack = TrackedEvent.EventTrackingPlatform.FACEBOOK))
    }

    if (days <= 8) {
      addTrackedEventsForAllPlatforms(userId = userId, event = EARNED_WITHIN_8_DAYS_8_DOLLARS, trackedEventList = trackedEvents)
      addTrackedEventsForAllPlatforms(userId = userId, event = EARNED_WITHIN_8_DAYS_13_DOLLARS, trackedEventList = trackedEvents)
    }

    if (days in 5..6) {
      addTrackedEventsForAllPlatforms(userId = userId, event = EARNED_WITHIN_6_DAYS_6_DOLLARS_RD5, trackedEventList = trackedEvents)
      addTrackedEventsForAllPlatforms(userId = userId, event = EARNED_WITHIN_6_DAYS_8_DOLLARS_RD5, trackedEventList = trackedEvents)
    }

    adMarketEvents.addAll(
      trackedEvents
        .filter { it.platformsToTrack == TrackedEvent.EventTrackingPlatform.FIREBASE }
        .mapNotNull { trackedEvent -> AdMarketEvent.EventType.entries.find { it.eventName == trackedEvent.eventName } }
    )

    if (trackedEvents.size > 0) {
      verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }
      verifyBlocking(adMarketService) {
        sendMarketEvents(userId, adMarketEvents, repeatable = false) }
    }
    verifyNoMoreInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
  fun `SHOULD NOT create tracked events ON computeAndSendEarningCalculatedEvents WHEN user is banned but earn 13$ in 1-10 days`(days: Long) {
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS), isBanned = true))

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }

    verifyNoInteractions(adMarketService)
    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @Test
  fun `SHOULD NOT create tracked events ON computeAndSendEarningCalculatedEvents WHEN user does not exist`() {
    userService.mock({ userExists(userId) }, false)

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }

    verifyBlocking(rewardingFacade) { loadUserEarningsForMetaId(1) }
    verifyNoMoreInteractions(rewardingFacade)
    verifyNoInteractions(trackedEventsPersistenceService)
    verifyNoInteractions(adMarketService)
  }

  @ParameterizedTest
  @ValueSource(longs = [9, 10])
  fun `SHOULD NOT create tracked events ON computeAndSendEarningCalculatedEvents WHEN user earn 13$ in 9+ days`(days: Long) {
    val userRevenue = BigDecimal("13.0")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(userId, emptyList(), repeatable = false) }
    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @Test
  fun `SHOULD NOT create tracked events ON computeAndSendEarningCalculatedEvents WHEN user is old`() {
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(10, ChronoUnit.DAYS)))

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(userId, emptyList(), repeatable = false) }
    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(doubles = [0.5, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 15.0, 20.0, 30.0])
  fun `SHOULD create earnings_reached_min_x events ON computeAndSendEarningCalculatedEvents WHEN user is not old`(revenue: Double) {
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(30, ChronoUnit.DAYS)))

    val thresholds = listOf(
      30.0 to EARNINGS_REACHED_MIN_30,
      20.0 to EARNINGS_REACHED_MIN_20,
      15.0 to EARNINGS_REACHED_MIN_15,
      10.0 to EARNINGS_REACHED_MIN_10,
      9.0 to EARNINGS_REACHED_MIN_9,
      8.0 to EARNINGS_REACHED_MIN_8,
      7.0 to EARNINGS_REACHED_MIN_7,
      6.0 to EARNINGS_REACHED_MIN_6,
      5.0 to EARNINGS_REACHED_MIN_5,
      4.0 to EARNINGS_REACHED_MIN_4,
      3.0 to EARNINGS_REACHED_MIN_3,
      2.0 to EARNINGS_REACHED_MIN_2,
      1.0 to EARNINGS_REACHED_MIN_1,
      0.5 to EARNINGS_REACHED_MIN_05,
    )

    rewardingFacade.mock({ getTotalUsdEarningsForUser(userId) }, BigDecimal(revenue))
    val expectedEvents = thresholds
      .filter { (threshold, _) -> revenue >= threshold }
      .map { (_, eventName) -> eventName }

    val actualEventsCaptor = argumentCaptor<List<AdMarketEvent.EventType>>()

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(eq(userId), actualEventsCaptor.capture(), any(), any(), anyOrNull(), anyOrNull())
    }

    val actualEvents = actualEventsCaptor.firstValue
    assertThat(actualEvents.toSet()).isEqualTo(expectedEvents.toSet())

    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(doubles = [0.5, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 15.0, 20.0, 30.0])
  fun `SHOULD not create earnings_reached_min_x events ON computeAndSendEarningCalculatedEvents WHEN user is old`(revenue: Double) {
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(31, ChronoUnit.DAYS)))

    rewardingFacade.mock({ getTotalUsdEarningsForUser(userId) }, BigDecimal(revenue))

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(userId, emptyList(), repeatable = false)
    }
    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8])
  fun `SHOULD create tracked event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user earn 0,1$ in 1,2 day AND EU market`(days: Long) {
    val userRevenue = BigDecimal("0.1")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }
    val marketsEvents = mutableListOf<AdMarketEvent.EventType>()
    val trackedEvents = mutableListOf<TrackedEvent>()
    if (days <= 2) {
      marketsEvents.add(EARNED_WITHIN_2_DAYS_0_1_DOLLARS)
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_1_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }
    } else {
      verifyNoInteractions(trackedEventsPersistenceService)
    }
    verifyBlocking(adMarketService) {
      sendMarketEvents(userId, marketsEvents, repeatable = false) }
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8])
  fun `SHOULD create tracked event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user earn 0,2$ in 1,2 day AND EU market`(days: Long) {
    val userRevenue = BigDecimal("0.2")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }
    val marketsEvents = mutableListOf<AdMarketEvent.EventType>()
    val trackedEvents = mutableListOf<TrackedEvent>()
    if (days <= 2) {
      marketsEvents.add(EARNED_WITHIN_2_DAYS_0_1_DOLLARS)
      marketsEvents.add(EARNED_WITHIN_2_DAYS_0_2_DOLLARS)
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_1_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_2_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }
    } else {
      verifyNoInteractions(trackedEventsPersistenceService)
    }
    verifyBlocking(adMarketService) {
      sendMarketEvents(userId, marketsEvents, repeatable = false) }
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8])
  fun `SHOULD create tracked event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user earn 0,5$ in 1,2 day AND EU market`(days: Long) {
    val userRevenue = BigDecimal("0.5")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }
    val marketsEvents = mutableListOf<AdMarketEvent.EventType>()
    val trackedEvents = mutableListOf<TrackedEvent>()
    if (days <= 2) {
      marketsEvents.add(EARNED_WITHIN_2_DAYS_0_1_DOLLARS)
      marketsEvents.add(EARNED_WITHIN_2_DAYS_0_2_DOLLARS)
      marketsEvents.add(EARNED_WITHIN_2_DAYS_0_5_DOLLARS)
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_1_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_2_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_5_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }
    } else {
      verifyNoInteractions(trackedEventsPersistenceService)
    }
    verifyBlocking(adMarketService) {
      sendMarketEvents(userId, marketsEvents, repeatable = false) }
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8])
  fun `SHOULD create tracked event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user earn 0,75$ in 1,2 day AND EU market`(days: Long) {
    val userRevenue = BigDecimal("0.75")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }

    val marketsEvents = mutableListOf<AdMarketEvent.EventType>()
    val trackedEvents = mutableListOf<TrackedEvent>()
    if (days <= 2) {
      marketsEvents.add(EARNED_WITHIN_2_DAYS_0_1_DOLLARS)
      marketsEvents.add(EARNED_WITHIN_2_DAYS_0_2_DOLLARS)
      marketsEvents.add(EARNED_WITHIN_2_DAYS_0_5_DOLLARS)
      marketsEvents.add(EARNED_WITHIN_2_DAYS_0_75_DOLLARS)
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_1_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_2_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_5_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_75_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))

      verifyBlocking(trackedEventsPersistenceService) {
        addTrackedEvents(trackedEvents)
      }
    } else {
      verifyNoInteractions(trackedEventsPersistenceService)
    }
    verifyBlocking(adMarketService) {
      sendMarketEvents(userId, marketsEvents, repeatable = false) }
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2])
  fun `SHOULD not create tracked event FOR GA and ADJUST ON computeAndSendEarningCalculatedEvents WHEN user earn 0,09$ in 1,2 day AND EU market`(days: Long) {
    val userRevenue = BigDecimal("0.09")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }

    val marketsEvents = mutableListOf<AdMarketEvent.EventType>()
    verifyNoInteractions(trackedEventsPersistenceService)
    verifyBlocking(adMarketService) { sendMarketEvents(userId, marketsEvents, repeatable = false) }
  }

  @Test
  fun `SHOULD not fail ON computeAndSendEarningCalculatedEvents WHEN sendMarketEvents throws`() {
    val userRevenue = BigDecimal(1)
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    timeService.mock({ now() }, midnight.plus(12, ChronoUnit.HOURS))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    adMarketService.throwException({ sendMarketEvents(eq(userId), any<List<AdMarketEvent.EventType>>(), any(), any(), anyOrNull(), anyOrNull()) }, RuntimeException("exception"))

    val trackedEvents = mutableListOf<TrackedEvent>()
    trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_1_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_2_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_5_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    trackedEvents.add(TrackedEvent(userId = userId, event = EARNED_WITHIN_2_DAYS_0_75_DOLLARS, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    addTrackedEventsForAllPlatforms(userId = userId, event = EARNED_WITHIN_2_DAYS_1_DOLLAR, trackedEventList = trackedEvents)
    trackedEvents.add(TrackedEvent(userId = userId, event = FB_MOBILE_LEVEL_ACHIEVED, platformsToTrack = TrackedEvent.EventTrackingPlatform.FACEBOOK))

    testScope.runTest { underTest.computeAndSendEarningCalculatedEvents(1) }

    verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }

    verifyNoMoreInteractions(trackedEventsPersistenceService)
  }


  @Test
  fun `SHOULD not send ad market events ON computeAndSendEarningCalculatedEvents WHEN work with empty User earnings`() {
    rewardingFacade.mock(
      { loadUserEarningsForMetaId(1) },
      null
    )
    verifyNoInteractions(adMarketService)
    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @CsvSource(
    "US,2.00",
    "GB,1.40",
    "AU,1.40",
    "CH,1.35",
    "DE,1.35",
    "CA,1.30",
    "NZ,1.20",
    "NL,1.10",
    "NO,1.05",
    "DK,1.05",
    "SE,1.00",
    "SG,0.90",
    "FI,0.85",
    "IE,0.70",
    "FR,0.60",
    "PL,0.55",
    "BE,0.50",
    "IT,0.50",
    "ES,0.35",
    "PT,0.30"
  )
  fun `SHOULD create tracked events ON computeAndSendEarningCalculatedEvents WHEN user earned x in 2 days`(countryCode: String, revenueThreshold: BigDecimal) {
    rewardingFacade.mock(
      { loadUserEarningsForMetaId(1) },
      UserEarnings(userId, 1, revenueThreshold, null)
    )
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(revenueThreshold, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    userService.mock({ getUserCountryCode(userId) }, countryCode)

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }

    verifyBlocking(trackedEventsPersistenceService) {
      addTrackedEvents(check {
        it.any { element -> element.eventName == "cashout_2_reached_x" }
      })
    }
    verifyBlocking(adMarketService) {
      sendMarketEvents(eq(userId), check<List<AdMarketEvent.EventType>> { it.any { element -> element == EARNED_WITHIN_2_DAYS_X_DOLLARS } }, any(), any(), anyOrNull(), anyOrNull())
    }

    verifyNoMoreInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @CsvSource(
    "US,2.00",
    "GB,1.40",
    "AU,1.40",
    "CH,1.35",
    "DE,1.35",
    "CA,1.30",
    "NZ,1.20",
    "NL,1.10",
    "NO,1.05",
    "DK,1.05",
    "SE,1.00",
    "SG,0.90",
    "FI,0.85",
    "IE,0.70",
    "FR,0.60",
    "PL,0.55",
    "BE,0.50",
    "IT,0.50",
    "ES,0.35",
    "PT,0.30"
  )
  fun `SHOULD NOT create tracked events cashout_2_reached_x ON computeAndSendEarningCalculatedEvents WHEN user did NOT earn x in 2 days`(
    countryCode: String,
    revenueThreshold: BigDecimal
  ) {
    rewardingFacade.mock(
      { loadUserEarningsForMetaId(1) },
      UserEarnings(userId, 1, revenueThreshold, null)
    )
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal.valueOf(0.25), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userDtoStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    userService.mock({ getUserCountryCode(userId) }, countryCode)

    testScope.runTest {
      underTest.computeAndSendEarningCalculatedEvents(1)
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(eq(userId), check<List<AdMarketEvent.EventType>> { it.none { element -> element == EARNED_WITHIN_2_DAYS_X_DOLLARS } }, any(), any(), anyOrNull(), anyOrNull())
    }
  }

  private fun verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents: List<TrackedEvent>, noInteractions: Boolean = false) {
    if (noInteractions) {
      verifyBlocking(trackedEventsPersistenceService, never()) { addTrackedEvents(any()) }
    } else {
      verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(argThat { this.none { it in trackedEvents } }) }
    }
  }

  private fun verifySendMarketEventsWasNotTriggeredFor(event: AdMarketEvent.EventType) {
    verifyBlocking(adMarketService) { sendMarketEvents(eq(userId), argThat<List<AdMarketEvent.EventType>> { this.none { it == event } }, any(), any(), anyOrNull(), anyOrNull()) }
  }

  private fun verifyEventsWerePropagatedForSending(trackedEvents: List<TrackedEvent>, event: AdMarketEvent.EventType) {
    verifyEventsWerePropagatedForSending(trackedEvents, listOf(event))
  }

  private fun verifyEventsWerePropagatedForSending(trackedEvents: List<TrackedEvent>, events: List<AdMarketEvent.EventType>) {
    verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(argThat { containsAll(trackedEvents) }) }
    verifyBlocking(adMarketService) { sendMarketEvents(eq(userId), argThat<List<AdMarketEvent.EventType>> { containsAll(events) }, any(), any(), anyOrNull(), anyOrNull()) }
  }

  private fun bothPlatformsEvents(event: AdMarketEvent.EventType) = listOf(
    TrackedEvent(userId = userId, event = event, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE),
    TrackedEvent(userId = userId, event = event, platformsToTrack = TrackedEvent.EventTrackingPlatform.FACEBOOK)
  )

  private fun addTrackedEventsForAllPlatforms(
    @Suppress("SameParameterValue") userId: String,
    event: AdMarketEvent.EventType,
    trackedEventList: MutableList<TrackedEvent>
  ) {
    trackedEventList.addAll(
      TrackedEvent.EventTrackingPlatform.entries.map { platform ->
        TrackedEvent(
          userId = userId,
          event = event,
          platformsToTrack = platform
        )
      }
    )
  }
  
  companion object {
    const val USER_ID = "userId"
    const val userId = USER_ID
    private val amount = BigDecimal("0.0005123")
    private val amountWithoutEarnings = BigDecimal("0.0005120")
    val AMOUNT_01 = BigDecimal("0.1")
    val AMOUNT_4 = BigDecimal("4")
    private val now: Instant = Instant.now()
    private val midnight = now.truncatedTo(ChronoUnit.DAYS)
    private val userDto = userDtoStub.copy(id = USER_ID, createdAt = now)

    private fun revenueTotals(sum: BigDecimal) = RevenueTotals(
      revenue = sum,
      offerwallRevenue = sum,
      day2Revenue = sum,
      day0Revenue = sum,
    )
  }
}