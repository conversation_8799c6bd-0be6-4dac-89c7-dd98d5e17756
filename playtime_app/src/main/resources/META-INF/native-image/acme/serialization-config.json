{"types": [{"name": "com.moregames.base.abtesting.Variations", "customTargetConstructorClass": "java.lang.Object"}, {"name": "com.moregames.base.app.BuildVariant", "customTargetConstructorClass": "java.lang.Object"}, {"name": "java.lang.Enum"}, {"name": "java.lang.Object[]"}, {"name": "java.time.Instant", "customTargetConstructorClass": "java.lang.Object"}, {"name": "java.util.HashSet"}, {"name": "java.util.LinkedHashSet"}, {"name": "java.util.concurrent.ArrayBlockingQueue"}, {"name": "java.util.concurrent.locks.AbstractOwnableSynchronizer"}, {"name": "java.util.concurrent.locks.AbstractQueuedSynchronizer"}, {"name": "java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject"}, {"name": "java.util.concurrent.locks.ReentrantLock"}, {"name": "java.util.concurrent.locks.ReentrantLock$NonfairSync"}, {"name": "java.util.concurrent.locks.ReentrantLock$Sync"}], "lambdaCapturingTypes": [], "proxies": []}