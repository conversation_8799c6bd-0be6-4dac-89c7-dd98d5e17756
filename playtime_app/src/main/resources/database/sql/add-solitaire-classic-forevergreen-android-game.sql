-- translation resources
insert into playtime.string_resource (resource_name)
values ('$_solitaire_classic_forevergreen_description'),
       ('$_solitaire_classic_forevergreen_text_install_top'),
       ('$_solitaire_classic_forevergreen_text_install_bottom');

-- translations en stubs
INSERT INTO playtime.string_resource_translation (resource_name, language, translation)
VALUES ('$_solitaire_classic_forevergreen_description', 'en', 'solitaire_classic_forevergreen_description'),
       ('$_solitaire_classic_forevergreen_text_install_top', 'en', 'solitaire_classic_forevergreen_text_install_top'),
       ('$_solitaire_classic_forevergreen_text_install_bottom', 'en', 'solitaire_classic_forevergreen_text_install_bottom')
ON DUPLICATE KEY UPDATE translation = VALUES(translation);

-- fill other languages
INSERT INTO playtime.string_resource_translation (resource_name, language, translation)
    (WITH res AS (SELECT *
                  FROM playtime.string_resource_translation
                  WHERE resource_name like '$\_solitaire_classic_forevergreen%'
                    AND language = 'en'),
          lang AS (SELECT DISTINCT language FROM playtime.string_resource_translation WHERE language != 'en')
     SELECT res.resource_name,
            lang.language,
            res.translation
     FROM res
              LEFT JOIN lang ON 1 = 1)
ON DUPLICATE KEY UPDATE translation = VALUES(translation);

-- Hidden game stub for enable rewarding mechanics
INSERT INTO playtime.games (id, application_id, name, description, icon_filename, image_filename, order_key,
                            applovin_api_key, activity_name, install_image_filename, info_text_install_top,
                            info_text_install_bottom, publisher_id, platform, do_not_show)
SELECT 200079                                                                                                                 AS id,
       'com.forevergreen.solitaire'                                                                                           AS application_id,
       'Solitaire Classic Forevergreen'                                                                                       AS name,
       '$_solitaire_classic_forevergreen_description'                                                                         AS description,
       'solitaire_classic_forevergreen.jpg'                                                                                   AS icon_filename,
       'solitaire_classic_forevergreen_preview.jpg'                                                                           AS image_filename,
       (SELECT order_key FROM playtime.games WHERE platform = 'ANDROID' AND application_id = 'com.forevergreen.atlantis') + 1 AS order_key,
       'gimica-api-key'                                                                                                       AS applovin_api_key,
       'com.unity3d.player.UnityPlayerActivity'                                                                               AS activity_name,
       'install_image_20230504.jpg'                                                                                           AS install_image_filename,
       '$_solitaire_classic_forevergreen_text_install_top'                                                                    AS info_text_install_top,
       '$_solitaire_classic_forevergreen_text_install_bottom'                                                                 AS info_text_install_bottom,
       4                                                                                                                      AS publisher_id,
       'ANDROID'                                                                                                              AS platform,
       1                                                                                                                      as do_not_show;

-- AdUnitIds
INSERT INTO playtime.game_ad_unit_ids
    (package_id, ad_type, ad_unit_id, is_highly_trusted_user, app_platform)
VALUES ('com.forevergreen.solitaire', 'BANNER', '2722a32fe48a065f', 1, 'ANDROID'),
       ('com.forevergreen.solitaire', 'INTERSTITIAL', '039f608e62ea6638', 1, 'ANDROID'),
       ('com.forevergreen.solitaire', 'REWARDED', 'a8dc01c2021c8b07', 1, 'ANDROID'),
       ('com.forevergreen.solitaire', 'BANNER', '34e98aa3334d95f1', 0, 'ANDROID'),
       ('com.forevergreen.solitaire', 'INTERSTITIAL', '79e4939b054edcc3', 0, 'ANDROID'),
       ('com.forevergreen.solitaire', 'REWARDED', '48b26e8690edbc9f', 0, 'ANDROID');
