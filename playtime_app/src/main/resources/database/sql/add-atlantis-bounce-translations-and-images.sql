-- game adjustments and reordering
with t1 as (
    select order_key as base_order_key
    from playtime.games
    where application_id = 'com.gimica.bubblepop'
      and platform = 'ANDROID'
)
UPDATE playtime.games, t1
SET order_key = order_key + 1
WHERE platform = 'ANDROID'
  AND order_key > t1.base_order_key
  AND application_id != 'com.justplay.app';

with t1 as (
    select order_key as base_order_key
    from playtime.games
    where application_id = 'com.gimica.bubblepop'
      and platform = 'ANDROID'
)
UPDATE playtime.games
SET order_key   = (select base_order_key from t1) + 1,
    do_not_show = 0
WHERE platform = 'ANDROID'
  AND application_id = 'com.forevergreen.atlantis';

-- translation resources
INSERT INTO playtime.string_resource_translation VALUES
--
('$_atlantis_bounce_description', 'en', 'Journey to the deep and earn coins'),
('$_atlantis_bounce_description', 'de', 'Begib dich in die Tiefe und verdiene Münzen'),
('$_atlantis_bounce_description', 'fr', 'Explore les profondeurs et gagne des pièces'),
('$_atlantis_bounce_description', 'es', 'Viaja a las profundidades y gana monedas'),
('$_atlantis_bounce_description', 'es-mx', 'Viaja a las profundidades y gana monedas'),
('$_atlantis_bounce_description', 'it', 'Viaggia nelle profondità e guadagna monete'),
('$_atlantis_bounce_description', 'nl', 'Reis naar de diepte en verdien munten'),
('$_atlantis_bounce_description', 'pl', 'Wyrusz w głąb i zdobądź monety'),
('$_atlantis_bounce_description', 'pt', 'Embarque nas profundezas e ganhe moedas'),
('$_atlantis_bounce_description', 'pt-br', 'Embarque nas profundezas e ganhe moedas'),
('$_atlantis_bounce_description', 'ja', '深海の冒険に出てコインを手に入れよう'),
('$_atlantis_bounce_description', 'ko', '깊은 곳으로 여행하며 코인을 획득하세요'),
('$_atlantis_bounce_description', 'zh-hk', '潛入深海贏取金幣'),
('$_atlantis_bounce_description', 'zh-tw', '潛入深海贏取金幣'),
--
('$_atlantis_bounce_text_install_top', 'en', 'Play Atlantis Bounce'),
('$_atlantis_bounce_text_install_top', 'de', 'Atlantis Bounce spielen'),
('$_atlantis_bounce_text_install_top', 'fr', 'Jouez à Atlantis Bounce'),
('$_atlantis_bounce_text_install_top', 'es', 'Juega ya a Atlantis Bounce'),
('$_atlantis_bounce_text_install_top', 'es-mx', 'Juega ya a Atlantis Bounce'),
('$_atlantis_bounce_text_install_top', 'it', 'Gioca a Atlantis Bounce'),
('$_atlantis_bounce_text_install_top', 'nl', 'Speel Atlantis Bounce'),
('$_atlantis_bounce_text_install_top', 'pl', 'Zagraj w Atlantis Bounce'),
('$_atlantis_bounce_text_install_top', 'pt', 'Joga Atlantis Bounce'),
('$_atlantis_bounce_text_install_top', 'pt-br', 'Jogue Atlantis Bounce'),
('$_atlantis_bounce_text_install_top', 'ja', '『Atlantis Bounce』をプレイ'),
('$_atlantis_bounce_text_install_top', 'ko', 'Atlantis Bounce 플레이'),
('$_atlantis_bounce_text_install_top', 'zh-hk', '暢玩《Atlantis Bounce》'),
('$_atlantis_bounce_text_install_top', 'zh-tw', '玩《Atlantis Bounce》'),
--
('$_atlantis_bounce_text_install_bottom', 'en', 'Journey to the deep and earn coins'),
('$_atlantis_bounce_text_install_bottom', 'de', 'Begib dich in die Tiefe und verdiene Münzen'),
('$_atlantis_bounce_text_install_bottom', 'fr', 'Explore les profondeurs et gagne des pièces'),
('$_atlantis_bounce_text_install_bottom', 'es', 'Viaja a las profundidades y gana monedas'),
('$_atlantis_bounce_text_install_bottom', 'es-mx', 'Viaja a las profundidades y gana monedas'),
('$_atlantis_bounce_text_install_bottom', 'it', 'Viaggia nelle profondità e guadagna monete'),
('$_atlantis_bounce_text_install_bottom', 'nl', 'Reis naar de diepte en verdien munten'),
('$_atlantis_bounce_text_install_bottom', 'pl', 'Wyrusz w głąb i zdobądź monety'),
('$_atlantis_bounce_text_install_bottom', 'pt', 'Embarque nas profundezas e ganhe moedas'),
('$_atlantis_bounce_text_install_bottom', 'pt-br', 'Embarque nas profundezas e ganhe moedas'),
('$_atlantis_bounce_text_install_bottom', 'ja', '深海の冒険に出てコインを手に入れよう'),
('$_atlantis_bounce_text_install_bottom', 'ko', '깊은 곳으로 여행하며 코인을 획득하세요'),
('$_atlantis_bounce_text_install_bottom', 'zh-hk', '潛入深海贏取金幣'),
('$_atlantis_bounce_text_install_bottom', 'zh-tw', '潛入深海贏取金幣')

ON DUPLICATE KEY UPDATE translation = VALUES(translation);