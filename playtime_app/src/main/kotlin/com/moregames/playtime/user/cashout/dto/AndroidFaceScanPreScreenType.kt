package com.moregames.playtime.user.cashout.dto

import com.moregames.base.abtesting.variations.AndroidFaceScanPreScreenVariation
import com.moregames.base.util.ClientVersionsSupport.ANDROID_CASHOUT_FACE_PRE_SCAN_BIPA_FULLSCREEN_UNUSED_APP_VERSION
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
enum class AndroidFaceScanPreScreenType {
  @SerialName("fullscreen_allow")
  FULLSCREEN_ALLOW,

  @SerialName("fullscreen_human")
  FULLSCREEN_HUMAN,

  @Deprecated("Unused from version $ANDROID_CASHOUT_FACE_PRE_SCAN_BIPA_FULLSCREEN_UNUSED_APP_VERSION")
  @SerialName("fullscreen_bipa")
  FULLSCREEN_BIPA,

  @SerialName("html_bipa")
  HTML_BIPA,
  ;

  companion object {
    fun fromVariation(variation: AndroidFaceScanPreScreenVariation) =
      when (variation) {
        AndroidFaceScanPreScreenVariation.FullScreenAllow -> FULLSCREEN_ALLOW
        AndroidFaceScanPreScreenVariation.FullScreenHuman -> FULLSCREEN_HUMAN
      }
  }
}
