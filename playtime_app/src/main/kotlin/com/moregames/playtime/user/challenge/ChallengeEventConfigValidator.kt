package com.moregames.playtime.user.challenge

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.TimeService
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.general.exception.InvalidParameterValueException
import com.moregames.playtime.translations.TranslationService
import com.moregames.playtime.translations.TranslationService.Companion.BE_TRANSLATION_RESOURCE_PREFIX
import com.moregames.playtime.user.challenge.api.admin.ChallengeAdminApiDto
import com.moregames.playtime.user.challenge.api.admin.ChallengeEventAdminApiDto
import java.util.Locale

@Singleton
class ChallengeConfigValidator @Inject constructor(
  private val translationService: TranslationService,
  private val timeService: TimeService,
  private val gamesService: GamesService,
) {
  suspend fun validateChallengeEvent(challengeEvent: ChallengeEventAdminApiDto) {
    validateDates(challengeEvent)
    validateProgressMax(challengeEvent)
    validateTranslations(challengeEvent)
    validateGameUniqueness(challengeEvent)
    validateApplicationIds(challengeEvent)
  }

  private fun validateGameUniqueness(challengeEvent: ChallengeEventAdminApiDto) {
    challengeEvent.challenges
      .groupBy { it.gameApplicationId }
      .forEach { group -> if (group.value.size > 1)
        throw InvalidParameterValueException("It is not allowed to have more than 1 game per event, application = ${group.key}") }
  }

  private suspend fun validateApplicationIds(challengeEvent: ChallengeEventAdminApiDto) {
    challengeEvent.challenges.forEach {
      validateApplicationId(it)
    }
  }

  private suspend fun validateApplicationId(challenge: ChallengeAdminApiDto) {
    val game = gamesService.getGameByApplicationId(challenge.gameApplicationId, AppPlatform.ANDROID)
    if (game == null) {
      throw InvalidParameterValueException("There is no game with applicationId = ${challenge.gameApplicationId} for challenge ${challenge.id}")
    }
  }

  private suspend fun validateTranslations(challengeEvent: ChallengeEventAdminApiDto) {
    validateNullableTranslation(challengeEvent.claimWidget?.claimButtonText, "challengeEvent.claimWidget.claimButtonText")
    validateNullableTranslation(challengeEvent.claimWidget?.aheadButtonText, "challengeEvent.claimWidget.aheadButtonText")
    validateTranslation(challengeEvent.claimWidget?.progressBarSubtext, "challengeEvent.claimWidget.progressBarSubtext")
    validateTranslation(challengeEvent.claimWidget?.headerText, "challengeEvent.claimWidget.headerText")
    validateTranslation(challengeEvent.challengesUpdateText, "challengeEvent.challengesUpdateText")
    validateTranslation(challengeEvent.bonusTracker?.completeText, "challengeEvent.bonusTracker.completeText")
    challengeEvent.challenges.forEach { challenge ->
      validateTranslation(challenge.title, "challenge.[${challenge.id}].challengeTitle")
    }
  }

  private suspend fun validateTranslation(text: String?, field: String) {
    if (text == null) return
    if (text.startsWith(BE_TRANSLATION_RESOURCE_PREFIX)
      && translationService.tryTranslate(text, Locale.ENGLISH) == text) {
      throw InvalidParameterValueException("There is no translation for $field")
    }
  }

  private suspend fun validateNullableTranslation(text: String?, field: String) {
    if (text != null) {
      validateTranslation(text, field)
    }
  }

  private fun validateProgressMax(challengeEvent: ChallengeEventAdminApiDto) {
    challengeEvent.challenges.forEach {
        challenge ->
      if (challenge.progressMax <= 0) {
        throw InvalidParameterValueException("Parameter [progressMax] for challenge [${challenge.id}] should be greater than zero")
      }
    }
  }

  private fun validateDates(event: ChallengeEventAdminApiDto) {
    if (event.dateFrom > event.dateTo) {
      throw InvalidParameterValueException("Parameter [dateTo] should be less than [dateFrom]")
    }
    if (event.dateTo < timeService.now()) {
      throw InvalidParameterValueException("Parameter [dateTo] should be greater than the current date")
    }
  }
}