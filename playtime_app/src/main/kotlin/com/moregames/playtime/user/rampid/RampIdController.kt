package com.moregames.playtime.user.rampid

import com.moregames.base.util.extractRequiredParameter
import com.moregames.playtime.user.UserController.Companion.USER_ID_PARAMETER
import io.ktor.application.*
import io.ktor.response.*
import io.ktor.routing.*
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.Serializable
import javax.inject.Inject

class RampIdController @Inject constructor(
  private val rampIdService: RampIdService,
) {

  @OptIn(ExperimentalSerializationApi::class)
  fun startRouting(route: Route) = with(route) {
    route("/rampid") {
      get("/status") {
        val userId = extractRequiredParameter(USER_ID_PARAMETER)
        call.respond(StatusResponse(rampIdService.checkStatus(userId)))
      }
      get("emailHash") {
        val userId = extractRequiredParameter(USER_ID_PARAMETER)
        call.respond(rampIdService.hashEmail(userId))
      }
    }
  }

  @Serializable
  data class StatusResponse(val shouldUseRampId: Boolean)
}