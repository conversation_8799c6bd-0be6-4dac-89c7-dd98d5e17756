package com.moregames.playtime.user

import com.google.inject.Inject
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.util.extractRequiredParameter
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.user.UserController.Companion.PACKAGE_ID_PARAMETER
import com.moregames.playtime.user.UserController.Companion.USER_ID_PARAMETER
import com.moregames.playtime.user.dto.GameStatusApiConfigurationDto
import com.moregames.playtime.user.dto.IosGameStatusApiDto
import com.moregames.playtime.user.fraudscore.HighlyTrustedUsersService
import com.moregames.playtime.user.gamescelebration.GamesCelebrationService
import com.moregames.playtime.user.gamescoinsbooster.GameCoinsBoosterService
import com.moregames.playtime.user.iosgameattconsent.IosGameAttConsentService
import com.moregames.playtime.user.usergame.UserGameService
import io.ktor.application.*
import io.ktor.http.HttpStatusCode.Companion.NotFound
import io.ktor.response.*
import io.ktor.routing.*

class IosExtGamesController @Inject constructor(
  private val iosGameAttConsentService: IosGameAttConsentService,
  private val userGameService: UserGameService,
  private val gamesService: GamesService,
  private val abTestingService: AbTestingService,
  private val userService: UserService,
  private val gameCoinsBoosterService: GameCoinsBoosterService,
  private val gamesCelebrationService: GamesCelebrationService,
  private val highlyTrustedUsersService: HighlyTrustedUsersService
) {
  fun startExtRouting(route: Route) {
    route.route("/ios") {
      get("/status") {
        val userId = extractRequiredParameter(USER_ID_PARAMETER)
        val packageId = extractRequiredParameter(PACKAGE_ID_PARAMETER)

        val gameId = gamesService.getGameId(packageId, IOS)
        if (gameId == null) {
          call.respond(NotFound)
          return@get
        }

        call.respond(
          IosGameStatusApiDto(
            userId = userId,
            configuration = GameStatusApiConfigurationDto(
              adVariant = userGameService.getGamesAdVariantValue(userId, packageId, IOS),
              inGameBalanceNotificationsEnabled = abTestingService.isGameBalanceUpdateNotificationParticipant(userId, IOS, activate = false),
              isHt = highlyTrustedUsersService.isHighlyTrustedUser(userId)
            ),
            lft = userService.getDay0RevenueForUser(userId),
          )
        )
      }

      get("/att-consent-configuration") {
        val userId = extractRequiredParameter("userId")
        //not used yet, contract restrictions support
        val packageId = extractRequiredParameter("packageId")
        val gameVersion = extractRequiredParameter("gameVersion")

        call.respond(iosGameAttConsentService.getConsentConfiguration(userId))
      }

      get("/coins-booster-configuration") {
        val userId = extractRequiredParameter("userId")
        //not used yet, contract restrictions support
        val packageId = extractRequiredParameter("packageId")
        val gameVersion = extractRequiredParameter("gameVersion")

        call.respond(gameCoinsBoosterService.getCoinsBoosterConfiguration(userId))
      }

      get("/celebration-configuration") {
        val userId = extractRequiredParameter("userId")
        //not used yet, contract restrictions support
        val packageId = extractRequiredParameter("packageId")
        val gameVersion = extractRequiredParameter("gameVersion")

        call.respond(gamesCelebrationService.getGamesCelebrationConfiguration(userId))
      }
    }
  }
}