package com.moregames.playtime.user.offer

import com.moregames.playtime.games.inappinstall.dto.AndroidInAppInstallInfoDto
import com.moregames.playtime.user.pregamescreen.AndroidPreGameScreenApiDto
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("game")
data class OfferItemGameApiDto(
  override val id: String,
  val style: GameStyle,
  val activityName: String,
  val isEnabled: Boolean,
  val title: String,
  val subtitle: String,
  val subtext: String,
  val imageUrl: String,
  val iconUrl: String,
  val applicationId: String,
  val showInstallImage: Boolean,
  val installImageUrl: String,
  val videoPreviewUrl: String?,
  val showVideoPreview: Boolean,
  val infoTextInstallTop: String,
  val infoTextInstallBottom: String,
  val backGroundColor: String,
  val installationLink: String?,
  val lastPlayedAt: Long?,
  val wasViewed: Boolean? = null,
  val requireAnalyticsConsent: Boolean? = null,
  val showBadge: Boolean? = null,
  //not used yet, API support
  val badgeUrl: String? = null,
  val widgets: GameAdditionalWidgets? = null,
  val inAppInstallDetails: AndroidInAppInstallInfoDto? = null,
  val webglGame: AndroidWebglGameApiDto? = null,
  val preGameScreenMode: AndroidPreGameScreenApiDto? = null
) : OfferItemApiDto()

@Serializable
enum class GameStyle {
  @SerialName("large_widget")
  LARGE_WIDGET,

  @SerialName("small_icon")
  SMALL_ICON,

  @SerialName("large_image")
  LARGE_IMAGE,

  @SerialName("widget_revamped_v1")
  WIDGET_REVAMPED_V1,
}
