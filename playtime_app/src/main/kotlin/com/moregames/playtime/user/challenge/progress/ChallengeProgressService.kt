package com.moregames.playtime.user.challenge.progress

import com.google.inject.Inject
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.TimeService
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import com.moregames.playtime.buseffects.ChallengeCompletedEffectHandler.ChallengeCompletedEffect
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.user.challenge.common.ChallengeService
import com.moregames.playtime.user.challenge.dto.*
import com.moregames.playtime.user.challenge.dto.bq.UserChallengeEventUpdatedBqDto
import com.moregames.playtime.user.challenge.dto.bq.UserChallengeUpdatedBqDto
import com.moregames.playtime.user.objectives.CalculatedObjectiveProgress
import com.moregames.playtime.user.objectives.progress.ObjectiveProgressCalculator
import java.math.BigDecimal
import javax.inject.Singleton

@Singleton
class ChallengeProgressService @Inject constructor(
  private val challengeService: ChallengeService,
  private val gamesService: GamesService,
  private val progressCalculators: Map<String, ObjectiveProgressCalculator>,
  private val messageBus: MessageBus,
  private val bigQueryEventPublisher: BigQueryEventPublisher,
  private val timeService: TimeService,
) {

  suspend fun handleUserChallengeProgress(userChallengeProgressDto: UserChallengeProgressDto) {
    val userId = userChallengeProgressDto.userId
    val currentEvent = challengeService.getCurrentChallengeEvent(userId) ?: return
    val challenge = findChallenge(userChallengeProgressDto, currentEvent) ?: return
    val progressCalculator = findProgressCalculator(challenge) ?: return
    val userEvent = findOrCreateUserEvent(userId, currentEvent, challenge) ?: return
    val userChallenge = findOrCreateUserChallenge(challenge, userEvent)
    val updateUserChallengeResult = updateProgress(userChallenge, userChallengeProgressDto, progressCalculator)
    updateChallengeEvent(updateUserChallengeResult, userEvent, currentEvent)
  }

  private fun findProgressCalculator(challenge: Challenge): ObjectiveProgressCalculator? {
    val progressCalculator = progressCalculators[challenge.calculator.name]
    if (progressCalculator == null) {
      logger().alert("Not found progress CALCULATOR for challenge ${challenge.id}")
    }
    return progressCalculator
  }

  private suspend fun findOrCreateUserEvent(userId: String, event: ChallengeEvent, challenge: Challenge): UserChallengeEvent? {
    val eventId = event.id
    val userEvent = challengeService.getUserChallengeEvent(userId, eventId)
    if (userEvent == null) {
      val eventIsStarted = challengeService.startUserEvent(eventId, userId)
      if (eventIsStarted) { sendStartedEventToBq(event, userId) }
      return UserChallengeEvent(
        eventId = eventId,
        userId = userId,
        state = ChallengeEventState.IN_PROGRESS,
        applovinNonBannerRevenue = null,
        earnings = BigDecimal.ZERO,
        startedAt = timeService.now()
      )
    }
    return if (challenge.challengeType == ChallengeType.REGULAR && userEvent.state.isFinal()){
      null
    } else {
      userEvent
    }
  }

  private suspend fun findChallenge(userChallengeProgressDto: UserChallengeProgressDto, event: ChallengeEvent): Challenge? {
    val gameId = gamesService.getGameId(userChallengeProgressDto.applicationId, AppPlatform.ANDROID)
    return event.challenges
      .filter { it.gameId == gameId }
      .sortedWith(compareBy( { it.order }, { it.id.value }))
      .firstOrNull()
  }

  private suspend fun findOrCreateUserChallenge(challenge: Challenge, userChallengeEvent: UserChallengeEvent): UserChallenge {
    val userChallenge = challengeService.getUserChallenge(challenge.id, userChallengeEvent.userId)
    if (userChallenge == null) {
      val started = challengeService.startUserChallenge(challenge.id, userChallengeEvent.userId)
      val startedChallenge = UserChallenge(
        userId = userChallengeEvent.userId,
        challenge = challenge,
        progress = 0,
        state = ChallengeState.IN_PROGRESS,
        coins = 0,
        completedAt = null,
        updatedAt = timeService.now(),
      )
      if (started) { sendStartedChallengeToBq(startedChallenge) }
      return startedChallenge
    } else {
      return userChallenge
    }
  }

  private suspend fun sendStartedEventToBq(event: ChallengeEvent, userId: String) {
      bigQueryEventPublisher.publish(
        UserChallengeEventUpdatedBqDto(
          userId = userId,
          challengeEventId = event.id,
          state = ChallengeEventState.IN_PROGRESS,
          earnings = BigDecimal.ZERO,
          applovinNonBannerRevenue = BigDecimal.ZERO,
          eventStart = event.dateFrom,
          eventEnd = event.dateTo,
          createdAt = timeService.now()
        )
      )
  }

  private suspend fun sendCompletedEventToBq(userEvent: UserChallengeEvent, userId: String, currentEvent: ChallengeEvent) {
      bigQueryEventPublisher.publish(
        UserChallengeEventUpdatedBqDto(
          userId = userId,
          challengeEventId = userEvent.eventId,
          state = ChallengeEventState.COMPLETED,
          earnings = userEvent.earnings,
          applovinNonBannerRevenue = userEvent.applovinNonBannerRevenue ?: BigDecimal.ZERO,
          eventStart = currentEvent.dateFrom,
          eventEnd = currentEvent.dateTo,
          createdAt = timeService.now()
        )
      )

  }


  private suspend fun updateChallengeEvent(
    updateUserChallengeResult: UserChallengeResult,
    userEvent: UserChallengeEvent,
    currentEvent: ChallengeEvent
  ) {
    val userChallenge = updateUserChallengeResult.userChallenge
    if (userEvent.state.isFinal() ||
      !updateUserChallengeResult.updated ||
      !userChallenge.state.isFinal() ||
      userChallenge.challenge.challengeType == ChallengeType.SPECIAL) {
      return
    }
    val eventId = userEvent.eventId
    val userId = userEvent.userId
    val allRegularChallengesAreCompleted = userChallenge.state.isFinal()
      && challengeService.getUserChallenges(userId, eventId)
      .filter { it.challenge.challengeType == ChallengeType.REGULAR }
      .all { it.state.isFinal() }
    if (allRegularChallengesAreCompleted) {
      val completed = challengeService.completeEvent(eventId, userId)
      if (completed) sendCompletedEventToBq(userEvent, userId, currentEvent)
    }
  }

  private suspend fun updateProgress(
    userChallenge: UserChallenge,
    userChallengeProgressDto: UserChallengeProgressDto,
    progressCalculator: ObjectiveProgressCalculator
  ): UserChallengeResult {
    logger().debug("Updating challenge progress {}", userChallengeProgressDto)
    if (userChallenge.state.isFinal()) {
      logger().debug("Challenge has been completed already {}", userChallenge)
      return UserChallengeResult(userChallenge)
    }
    val challenge = userChallenge.challenge
    val progressMax = userChallenge.challenge.progressMax
    val calculatedProgress = progressCalculator.calculateProgress(userChallengeProgressDto, userChallenge)
    val progress = capProgress(calculatedProgress, progressMax)
    if (progress.progress <= userChallenge.progress) {
      logger().debug("New progress (${progress.progress}) is less or equal to old progress (${userChallenge.progress} for userId = ${userChallenge.userId})")
      return UserChallengeResult(userChallenge)
    }
    val state = if (progress.progress == progressMax) {
      ChallengeState.COMPLETED
    } else {
      ChallengeState.IN_PROGRESS
    }
    val updated = challengeService.updateChallengeProgress(userChallenge, progress, state)
    if (updated) {
      completeChallengeEffects(userChallenge.userId, state, userChallenge)
      return UserChallengeResult(userChallenge.copy(state = state, progress = progress.progress), true)
    } else {
      val updatedInOtherThread = challengeService.getUserChallenge(challenge.id, userChallenge.userId) ?: userChallenge
      return UserChallengeResult(updatedInOtherThread)
    }
  }

  private suspend fun sendStartedChallengeToBq(userChallenge: UserChallenge) {
    bigQueryEventPublisher.publish(UserChallengeUpdatedBqDto(
      userId = userChallenge.userId,
      challengeId = userChallenge.challenge.id,
      challengeEventId = userChallenge.challenge.eventId,
      state = ChallengeState.IN_PROGRESS,
      gameId = userChallenge.challenge.gameId,
      completedAt = null,
      createdAt = timeService.now()
    ))
  }

  private fun capProgress(dto: CalculatedObjectiveProgress, progressMax: Int): CalculatedObjectiveProgress {
    return if (dto.progress > progressMax) {
      dto.copy(progress = progressMax)
    } else {
      dto
    }
  }

  private fun completeChallengeEffects(userId: String, state: ChallengeState, userChallenge: UserChallenge) {
    if (state != ChallengeState.COMPLETED) return

    val challenge = userChallenge.challenge
    messageBus.publishAsync(
      ChallengeCompletedEffect(
        userId = userId,
        challengeId = challenge.id,
        challengeEventId = challenge.eventId,
        gameId = challenge.gameId,
        challengeType = challenge.challengeType,
      )
    )
  }
}

data class UserChallengeResult(
  val userChallenge: UserChallenge,
  val updated: Boolean = false,
)