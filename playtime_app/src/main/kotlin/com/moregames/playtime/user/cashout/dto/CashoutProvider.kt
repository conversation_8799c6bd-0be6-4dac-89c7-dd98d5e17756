package com.moregames.playtime.user.cashout.dto

import com.moregames.base.app.PaymentProviderType
import com.moregames.base.app.UserIdentifierType
import java.math.BigDecimal

data class CashoutProvider(
  val displayName: String,
  val url: String,
  val videoUrl: String?,
  val iconFilename: String,
  val largeIconFilename: String,
  val smallIconFilename: String,
  val text: String,
  val shortText: String? = null,
  val providerType: PaymentProviderType,
  val disclaimer: String?,
  val emailHint: String?,
  val minimumAmount: BigDecimal?,
  val maximumAmount: BigDecimal?,
  val enabled: Boolean = true,
  val orderKey: Int,
  val identifierType: UserIdentifierType? = null,
  val identifierHint: String?
)
