package com.moregames.playtime.user.table

import org.jetbrains.exposed.sql.Table

object UserFirebasePropertiesForceAssignmentsTable : Table("playtime.user_firebase_properties_force_assignments") {
  val userId = varchar("user_id", 36)
  val firebaseAppInstanceId = varchar("firebase_app_instance_id", 32)
  val appPlatform = varchar("app_platform", 36).nullable()

  override val primaryKey = PrimaryKey(userId)
}