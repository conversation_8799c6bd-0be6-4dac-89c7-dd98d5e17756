package com.moregames.playtime.user.cashout.dto

import com.moregames.base.util.ClientVersionsSupport.ANDROID_CASHOUT_BONUS_STATUS_UNUSED_APP_VERSION
import com.moregames.base.util.ClientVersionsSupport.ANDROID_CASHOUT_FORM_TYPE_UNUSED_APP_VERSION
import com.moregames.base.util.ClientVersionsSupport.ANDROID_ONE_CLICK_CASHOUT_UNUSED_APP_VERSION
import com.moregames.base.util.ClientVersionsSupport.ANDROID_SHOW_AD_AFTER_CASHOUT_APP_VERSION
import com.moregames.base.util.ClientVersionsSupport.IOS_CASHOUT_BONUS_STATUS_UNUSED_APP_VERSION
import com.moregames.base.util.InstantAsString
import com.moregames.playtime.user.cashout.dto.CashoutBonusStatusApiDto.Companion.BACK_COMPATIBILITY_MOCK
import com.moregames.playtime.user.dto.CashoutButtonStyle
import com.moregames.playtime.user.dto.PaymentProviderSurvey
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CashoutApiDto(
  val isEnabled: Boolean,
  val headerText: String,
  val iconUrl: String,
  val nextCashoutTimestamp: InstantAsString,
  val amountText: String? = null,
  val bonusAmountText: String? = null,
  val cashoutAmountBefore: String? = null,
  @SerialName("options")
  val providers: List<CashoutProviderApiDto>,
  @Deprecated("Can be removed once minimum app version is $ANDROID_CASHOUT_BONUS_STATUS_UNUSED_APP_VERSION and $IOS_CASHOUT_BONUS_STATUS_UNUSED_APP_VERSION")
  val bonusStatus: CashoutBonusStatusApiDto = BACK_COMPATIBILITY_MOCK,
  val disclaimer: String,
  val showRewards: Boolean,
  val providersImageList: List<String>,
  val timestamp: InstantAsString,
  @Deprecated("Unused from version $ANDROID_CASHOUT_FORM_TYPE_UNUSED_APP_VERSION")
  val cashoutFormType: String? = "HIDE_ADDRESS",
  val cashoutFormStyle: CashoutFormStyle?,
  val consentedToAnalytics: Boolean,
  @Deprecated("Unused from version $ANDROID_ONE_CLICK_CASHOUT_UNUSED_APP_VERSION")
  val oneClickType: String? = "ONE_CLICK_ONLY",
  @Deprecated("Unused from version $ANDROID_SHOW_AD_AFTER_CASHOUT_APP_VERSION")
  val videoAdType: String? = "INTERSTITIAL",
  val cashoutButtonStyle: CashoutButtonStyle?,
  val giftBoxInsteadOfEarnings: Boolean,
  @Deprecated("Unused by Android, still used by IOS")
  val paymentProviderSurvey: PaymentProviderSurvey? = null,
  val cashoutProgressBarMode: AndroidCashoutProgressBarMode? = null,
  val cashoutOffers: List<CashoutOfferApiDto>?,
  val cashoutOffersConfig: CashoutOfferConfigApiDto,
  val highlightedGamesOnCashoutCancel: Boolean? = null,
  val boostedMode: CashoutBoostedModeApiDto? = null,
  val offerwallsDiscover: List<OfferwallDiscoverApiDto>? = null,
)