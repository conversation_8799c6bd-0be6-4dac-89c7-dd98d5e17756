package com.moregames.playtime.user.toprunningbar

import com.google.inject.Inject
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment.SHOW_TOP_RUNNING_BAR
import com.moregames.base.abtesting.Variations
import com.moregames.base.abtesting.Variations.*
import com.moregames.base.util.RandomGenerator
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.decodeFromStream
import javax.inject.Singleton

@OptIn(ExperimentalSerializationApi::class)
@Singleton
class TopRunningBarService @Inject constructor(
  private val abTestingService: AbTestingService,
  private val dao: TopRunningBarPersistenceService,
  private val randomGenerator: RandomGenerator,
) {
  private val names: List<String>

  init {
    val stream = this::class.java.getResourceAsStream("/top-bar-exp-names.json")
      ?: throw IllegalArgumentException("Can't find top-bar-exp-names.json file in classpath!!")
    names = Json.decodeFromStream<List<String>>(stream)
  }

  suspend fun getTopRunningBarConfig(userId: String): TopRunningBarDto? {
    val variation = abTestingService.assignedVariationValue(userId, SHOW_TOP_RUNNING_BAR)
    if (variation !is Variations) {
      return null
    }
    val topRunningBarConfig = dao.getTopRunningBarConfig(variation.variationKey) ?: run {
      logger().alert("TopRunningBar configuration not found for variationKey: ${variation.variationKey}, user: $userId")
      return null
    }
    return when (variation) {
      TOP_RUNNING_BAR_BLUE_WHITE -> topRunningBarConfig
      TOP_RUNNING_BAR_TEMPLATED, IOS_TOP_RUNNING_BAR_TEMPLATED, TOP_RUNNING_BAR_TEMPLATED_DECREASED, TOP_RUNNING_BAR_TEMPLATED_MAX -> processTemplatedBar(
        topRunningBarConfig,
        variation
      )

      IOS_TOP_RUNNING_BAR_TEMPLATED_AND_EDUCATIONAL, TOP_RUNNING_BAR_TEMPLATED_AND_EDUCATIONAL -> processTemplatedAndEducationalBar(topRunningBarConfig)
      else -> null
    }
  }

  private fun processTemplatedAndEducationalBar(topRunningBarConfig: TopRunningBarDto): TopRunningBarDto {
    val educationalMessages = topRunningBarConfig.values.filterNot { it.contains(templateRegex) }
    val template = topRunningBarConfig.values.find { it.contains(templateRegex) }.orEmpty().let { TemplateString(it) }
    val result = generateSequence {
      val percent = randomGenerator.intRangeRandom(1..100)
      when (percent) {
        in 1..40 -> template.generateMessage(names, randomGenerator.intRangeRandom(11..15))
        in 41..60 -> template.generateMessage(names, randomGenerator.intRangeRandom(16..20))
        in 61..80 -> template.generateMessage(names, randomGenerator.intRangeRandom(21..30))
        else -> educationalMessages.random()
      }
    }.filter { it.isNotEmpty() }.distinct().take(TEMPLATED_MESSAGES_COUNT).toList()
    return topRunningBarConfig.copy(values = result)
  }

  private fun processTemplatedBar(topRunningBarDto: TopRunningBarDto, variation: Variations): TopRunningBarDto {
    val messages = topRunningBarDto.values.flatMap {
      if (it.contains(templateRegex)) {
        val numberGeneratorFunction = generateNumberMethodMap.getValue(variation)
        generateSequence { TemplateString(it).generateMessage(names, numberGeneratorFunction()) }
          .distinct()
          .take(TEMPLATED_MESSAGES_COUNT)
          .toList()
      } else {
        listOf(it)
      }
    }
    return topRunningBarDto.copy(values = messages)
  }

  private val generateNumberMethodMap = mapOf(
    TOP_RUNNING_BAR_TEMPLATED_DECREASED to ::generateNumberForDecreasedVariation,
    TOP_RUNNING_BAR_TEMPLATED_MAX to ::generateNumberForMaxVariation
  ).withDefault { return@withDefault ::generateNumberForOldVariation }

  private fun generateNumberForDecreasedVariation(): Int = with(randomGenerator) {
    val percent = intRangeRandom(1..100)
    return when (percent) {
      in 1..75 -> intRangeRandom(1..4)
      in 76..95 -> intRangeRandom(5..8)
      in 96..99 -> intRangeRandom(9..25)
      else -> intRangeRandom(25..30)
    }
  }

  private fun generateNumberForMaxVariation(): Int = with(randomGenerator) {
    val percent = intRangeRandom(1..100)
    return when (percent) {
      in 1..70 -> intRangeRandom(11..15)
      in 71..95 -> intRangeRandom(16..20)
      else -> intRangeRandom(21..30)
    }
  }

  private fun generateNumberForOldVariation(): Int = with(randomGenerator) {
    val percent = intRangeRandom(1..100)
    return when (percent) {
      in 1..75 -> intRangeRandom(1..10)
      in 76..95 -> intRangeRandom(11..15)
      in 96..99 -> intRangeRandom(16..20)
      else -> intRangeRandom(21..30)
    }
  }

  @JvmInline
  value class TemplateString(val string: String) {
    fun generateMessage(names: List<String>, number: Int): String {
      return string.replace("{name}", names.random()).replace("{number}", number.toString())
    }
  }

  companion object {
    const val TEMPLATED_MESSAGES_COUNT = 30
    val templateRegex = """^.*\{.*}.*""".toRegex()
  }
}
