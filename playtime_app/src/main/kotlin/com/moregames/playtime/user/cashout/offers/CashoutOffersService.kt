package com.moregames.playtime.user.cashout.offers

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.variations.SpecialCashoutOffersVariation
import com.moregames.base.abtesting.variations.SpecialCashoutOffersVariation.*
import com.moregames.base.app.OfferWallType
import com.moregames.base.bus.MessageBus
import com.moregames.base.db.Transactor
import com.moregames.base.dto.AppPlatform
import com.moregames.base.exceptions.BaseException
import com.moregames.base.exceptions.ErrorCode
import com.moregames.base.exceptions.ErrorType
import com.moregames.base.exceptions.PlaytimeErrorCodes
import com.moregames.base.util.TimeService
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.buseffects.CashoutOfferActivatedEvent
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.user.UserDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutPersistenceService
import com.moregames.playtime.user.cashout.dto.CashoutOfferApiDto
import com.moregames.playtime.user.cashout.dto.OfferwallDiscoverApiDto
import com.moregames.playtime.user.cashout.offers.CashoutOffersDao.CashoutOfferSetEntity.CashoutOfferEntity
import com.moregames.playtime.user.offer.AndroidInstallationLinkProvider
import com.moregames.playtime.user.offer.AndroidOfferwallPlacementIdService
import com.moregames.playtime.util.plus
import java.math.BigDecimal
import java.time.Duration
import java.time.Instant
import javax.inject.Inject
import javax.inject.Provider
import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.contract
import kotlin.time.Duration.Companion.hours

class CashoutOffersService @Inject constructor(
  private val cashoutOffersDao: CashoutOffersDao,
  private val userService: Provider<UserService>,
  private val timeService: TimeService,
  private val transactor: Transactor,
  private val messageBus: MessageBus,
  private val gamesService: GamesService,
  private val abTestingService: AbTestingService,
  private val androidOfferwallPlacementIdService: AndroidOfferwallPlacementIdService,
  private val imageService: ImageService,
  private val cashoutPersistenceService: CashoutPersistenceService,
  private val androidInstallationLinkProvider: AndroidInstallationLinkProvider
) {

  suspend fun getOfferSet(userId: String): CashoutOfferSet {
    val lastOffer = cashoutOffersDao.findLastOfferSet(userId) ?: return CashoutOfferSet.NotCreated

    return if (lastOffer.closedAt != null) {
      CashoutOfferSet.Cooldown(lastOffer.closedAt)
    } else {
      CashoutOfferSet.Active(lastOffer.id, lastOffer.userId, lastOffer.offers.toDomain(timeService.now()))
    }
  }

  suspend fun createSetIfNeeded(userId: String) {
    when (val set = getOfferSet(userId)) {
      is CashoutOfferSet.Active -> return
      is CashoutOfferSet.Cooldown -> {
        if (Duration.between(set.closedAt, timeService.now()).toHours() >= 24) {
          createNewSet(userId)
        }
      }

      CashoutOfferSet.NotCreated -> createNewSet(userId)
    }
  }

  suspend fun closeSetIfNeeded(userId: String) {
    when (val set = getOfferSet(userId)) {
      is CashoutOfferSet.Active -> cashoutOffersDao.closeSet(set.setId)
      is CashoutOfferSet.Cooldown, CashoutOfferSet.NotCreated -> {} // do nothing here
    }
  }

  private suspend fun createNewSet(userId: String) {
    val variation = abTestingService.assignedVariationValue(userId, ClientExperiment.SPECIAL_CASHOUT_OFFERS) as? SpecialCashoutOffersVariation ?: return

    val gamesInSet = when (variation) {
      ThreeCashoutOffers -> {
        val userLastPlayedGames = userService.get().loadUserGameCoins(userId)
          .asSequence()
          .map { it.value }
          .filter { it.lastPlayedAt != null }
          .sortedByDescending { it.lastPlayedAt }
          .map { it.gameId }
          .take(3)
          .toMutableList()
        if (userLastPlayedGames.size < 3) {
          userLastPlayedGames += fallbackGames.filter { it !in userLastPlayedGames }.take(3 - userLastPlayedGames.size)
        }
        userLastPlayedGames
      }

      ThreeRandomCashoutOffers,
      ThreeRandom1EarningCashoutOffers,
      ThreeRandom025EarningCashoutOffers,
      ThreeRandom05EarningCashoutOffers,
      TJDiscoveryAlways05,
      TJDiscoveryFirst05,
      TJDiscoveryLater05,
      TJDiscoveryAlways025,
      TJDiscoveryFirst025,
      TJDiscoveryLater025,
      ThreeRandom025EarningCashoutOffersNoPopup,
      ThreeRandom05EarningCashoutOffersNoPopup -> randomGamesSet.shuffled().take(3)
    }

    cashoutOffersDao.createNewSet(userId, gamesInSet)
  }

  suspend fun activateOffer(userId: String, offerId: String): Instant = transactor.inTransaction {
    val set = getOfferSet(userId)
    validateOfferActivation(set, offerId)

    val activeUntilDate = timeService.now() + 1.hours
    cashoutOffersDao.activateOffer(offerId, activeUntilDate)
    val game = set.offers.first { it.id == offerId }.gameId.let { gamesService.getGameById(AppPlatform.ANDROID, it) } ?: return@inTransaction activeUntilDate
    messageBus.publish(CashoutOfferActivatedEvent(userId, game.id, game.name, activeUntilDate))
    activeUntilDate
  }

  suspend fun applySpecialCashoutOffersExperiment(user: UserDto, earningsAmount: BigDecimal): SpecialCashoutOffers {
    val assignedVariation = abTestingService.assignedVariationValue(user.id, ClientExperiment.SPECIAL_CASHOUT_OFFERS)
    if (assignedVariation == DEFAULT) return SpecialCashoutOffers()

    if (user.offerWallTypes?.contains(OfferWallType.TAPJOY) != true || earningsAmount == BigDecimal.ZERO || earningsAmount >= BigDecimal.ONE)
      return SpecialCashoutOffers(
        cashoutOffers = getCashoutOffers(user.id),
        offerwallsDiscover = null
      )

    val successfulCashouts = cashoutPersistenceService.successfulUserCashouts(user.id)

    val shouldShowOfferwallDiscover: Boolean =
      (assignedVariation as? SpecialCashoutOffersVariation)?.let { variation ->

        return@let shouldShowOfferwallDiscover(
          variation,
          earningsAmount,
          successfulCashouts.toInt()
        )
      } ?: false

    if (shouldShowOfferwallDiscover) {
      return SpecialCashoutOffers(
        cashoutOffers = null,
        offerwallsDiscover = listOf(
          OfferwallDiscoverApiDto(
            offerWallPlacementId = androidOfferwallPlacementIdService.calculateOfferwallPlacementId(OfferWallType.TAPJOY, user.id),
            offerWallType = OfferWallType.TAPJOY
          )
        )
      )
    } else {
      return SpecialCashoutOffers(
        cashoutOffers = getCashoutOffers(user.id),
        offerwallsDiscover = null
      )
    }
  }

  private fun shouldShowOfferwallDiscover(
    variation: SpecialCashoutOffersVariation,
    earningsAmount: BigDecimal,
    successfulCashouts: Int
  ): Boolean {
    //Earnings already exceed the variation’s acceptable earnings
    if (earningsAmount >= variation.acceptableAmount) return false

    // “always / even / odd”
    return when (variation) {
      // Always show
      TJDiscoveryAlways025,
      TJDiscoveryAlways05 -> true

      // Even cash-out counts (0 , 2 , 4 …)
      TJDiscoveryFirst025,
      TJDiscoveryFirst05 -> successfulCashouts % 2 == 0

      // Odd cash-out counts (1 , 3 , 5 …)
      TJDiscoveryLater025,
      TJDiscoveryLater05 -> successfulCashouts % 2 == 1

      else -> false
    }
  }

  private suspend fun getCashoutOffers(userId: String): List<CashoutOfferApiDto>? {
    if (!abTestingService.isUserExperimentParticipant(userId, ClientExperiment.SPECIAL_CASHOUT_OFFERS)) return null

    return when (val offerSet = getOfferSet(userId)) {
      is CashoutOfferSet.Active -> {
        val games = offerSet.offers.mapNotNull { gamesService.getGameById(AppPlatform.ANDROID, it.gameId) }
          .associateBy { it.id } // getGameById is cached
        offerSet.offers.mapNotNull {
          val game = games[it.gameId] ?: run {
            logger().alert("Can't find game for Cashout Offer: ${it.gameId}")
            return@mapNotNull null
          }
          CashoutOfferApiDto(
            cashoutOfferId = it.id,
            iconUrl = imageService.toUrl(game.iconFilename),
            applicationId = game.applicationId,
            activityName = game.activityName,
            installationLink = androidInstallationLinkProvider.provide(game.applicationId, userId),
            activeUntilDate = (it as? CashoutOffer.Active)?.activeUntilDate,
            status = when (it) {
              is CashoutOffer.Active -> CashoutOfferApiDto.Status.ACTIVE
              is CashoutOffer.Claimed -> CashoutOfferApiDto.Status.CLAIMED
              is CashoutOffer.Unclaimed -> CashoutOfferApiDto.Status.UNCLAIMED
            }
          )
        }
      }

      is CashoutOfferSet.Cooldown, CashoutOfferSet.NotCreated -> emptyList()
    }
  }

  @OptIn(ExperimentalContracts::class)
  private fun validateOfferActivation(set: CashoutOfferSet, offerId: String) {
    contract {
      returns() implies (set is CashoutOfferSet.Active)
    }
    when (set) {
      is CashoutOfferSet.Active -> {
        if (set.offers.filterIsInstance<CashoutOffer.Active>().isNotEmpty())
          throw CashoutOfferAlreadyActiveException(set.userId)

        if (offerId !in set.offers.map { it.id })
          throw IllegalArgumentException("Offer $offerId not found in the active set")
      }

      is CashoutOfferSet.Cooldown, CashoutOfferSet.NotCreated -> throw IllegalStateException("No active offer set for user")
    }
  }

  private fun List<CashoutOfferEntity>.toDomain(now: Instant) = map {
    when {
      it.activeUntilDate == null -> CashoutOffer.Unclaimed(it.id, it.gameId)
      it.activeUntilDate > now -> CashoutOffer.Active(it.id, it.gameId, it.activeUntilDate)
      else -> CashoutOffer.Claimed(it.id, it.gameId)
    }
  }

  suspend fun hasActiveOfferForGame(userId: String, gameId: Int): Boolean {
    val set = (getOfferSet(userId) as? CashoutOfferSet.Active) ?: return false

    return set.offers.any { it.gameId == gameId && it is CashoutOffer.Active }
  }

  sealed interface CashoutOfferSet {
    data object NotCreated : CashoutOfferSet
    data class Cooldown(val closedAt: Instant) : CashoutOfferSet
    data class Active(val setId: Int, val userId: String, val offers: List<CashoutOffer>) : CashoutOfferSet
  }

  sealed interface CashoutOffer {
    val id: String
    val gameId: Int

    data class Unclaimed(override val id: String, override val gameId: Int) : CashoutOffer
    data class Active(override val id: String, override val gameId: Int, val activeUntilDate: Instant) : CashoutOffer
    data class Claimed(override val id: String, override val gameId: Int) : CashoutOffer
  }

  companion object {
    val fallbackGames = listOf(
      200039, // TM
      200051, // zentiles
      200044, // solitaire verse
    )
    val randomGamesSet = setOf(
      200039, // TM
      200051, // zentiles
      200044, // solitaire verse
      200068, // tile match pro
      200072, // Tangram Heaven
      200062, // Water Sorter
      200046, // Word Seeker,
      200069, // Space Connect
    )
  }

  class CashoutOfferAlreadyActiveException(userId: String) : BaseException(
    internalMessage = "User $userId already has an active offer",
    externalMessage = "You can only have 1 active offer at a time",
  ) {
    override val errorCode: ErrorCode = PlaytimeErrorCodes.CASHOUT_OFFER_ALREADY_ACTIVE
    override val errorType: ErrorType = ErrorType.INPUT_ERROR
  }

  data class SpecialCashoutOffers(
    val cashoutOffers: List<CashoutOfferApiDto>? = null,
    val offerwallsDiscover: List<OfferwallDiscoverApiDto>? = null,
  )
}