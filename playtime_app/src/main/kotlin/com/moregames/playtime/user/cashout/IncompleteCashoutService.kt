package com.moregames.playtime.user.cashout

import com.google.inject.Inject
import com.google.inject.Singleton

@Singleton
class IncompleteCashoutService @Inject constructor(
  private val incompleteCashoutPersistenceService: IncompleteCashoutPersistenceService,
) {

  suspend fun trackCashoutInitiated(userId: String) {
    incompleteCashoutPersistenceService.trackCashoutInitiated(userId)
  }

  suspend fun removeTrackedInitiatedCashouts(userId: String) {
    incompleteCashoutPersistenceService.removeTrackedInitiatedCashouts(userId)
  }

  suspend fun hasTrackedInitiatedCashouts(userId: String) =
    incompleteCashoutPersistenceService.hasTrackedInitiatedCashouts(userId)
}