package com.moregames.playtime.user.verification.table

import org.jetbrains.exposed.sql.Table

object UserFaceCheckAttemptsTable : Table("playtime.user_face_check_attempt") {
  val userId = varchar("user_id", 36)
  val sessionId = varchar("session_id", 100)
  val faceScanLivenessCheckSucceeded = bool("face_scan_liveness_check_succeeded").nullable()
  val auditTrailVerificationCheckSucceeded = bool("audit_trail_verification_check_succeeded").nullable()
  val replayCheckSucceeded = bool("replay_check_succeeded").nullable()
  val sessionTokenCheckSucceeded = bool("session_token_check_succeeded").nullable()
  val uniquenessCheckSucceeded = bool("uniquness_check_succeeded").nullable()
}