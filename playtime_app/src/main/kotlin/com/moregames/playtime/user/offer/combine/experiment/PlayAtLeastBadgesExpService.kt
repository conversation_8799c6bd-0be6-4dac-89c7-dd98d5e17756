package com.moregames.playtime.user.offer.combine.experiment

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.BaseVariation
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.Variations
import com.moregames.base.app.BuildVariant
import com.moregames.base.util.ApplicationId.BALL_BOUNCE_APP_ID
import com.moregames.base.util.ApplicationId.MAD_SMASH_APP_ID
import com.moregames.base.util.ApplicationId.MERGE_BLAST_APP_ID
import com.moregames.base.util.ApplicationId.MIX_BLOX_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_VERSE_APP_ID
import com.moregames.base.util.ApplicationId.SPACE_CONNECT_APP_ID
import com.moregames.base.util.ApplicationId.SUDOKU_APP_ID
import com.moregames.base.util.ApplicationId.SUGAR_RUSH_APP_ID
import com.moregames.base.util.ApplicationId.TANGRAM_APP_ID
import com.moregames.base.util.ApplicationId.TILE_MATCH_PRO_APP_ID
import com.moregames.base.util.ApplicationId.TREASURE_MASTER_APP_ID
import com.moregames.base.util.ApplicationId.WATER_SORTER_APP_ID
import com.moregames.base.util.ApplicationId.WOODEN_PUZZLE_APP_ID
import com.moregames.base.util.ApplicationId.WORD_SEEKER_APP_ID
import com.moregames.base.util.IoCoroutineScope
import com.moregames.base.util.buildCache
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.offer.OfferItemGameApiDto
import kotlinx.coroutines.async
import javax.inject.Singleton
import kotlin.time.Duration.Companion.minutes

@Singleton
class PlayAtLeastBadgesExpService @Inject constructor(
  private val abTestingService: AbTestingService,
  private val imageService: ImageService,
  private val rewardingFacade: RewardingFacade,
  buildVariantProvider: Provider<BuildVariant>,
  private val coroutineScope: Provider<IoCoroutineScope>,
) {

  companion object {
    val badgedImages = mapOf(
      SOLITAIRE_VERSE_APP_ID to "1_solitaire_verse.jpg",
      TREASURE_MASTER_APP_ID to "2_treasure_master.jpg",
      TILE_MATCH_PRO_APP_ID to "3_tile_match_pro.jpg",
      MAD_SMASH_APP_ID to "4_mad_smash.jpg",
      BALL_BOUNCE_APP_ID to "5_ball_bounce.jpg",
      MERGE_BLAST_APP_ID to "6_merge_blast.jpg",
      SUGAR_RUSH_APP_ID to "7_sugar_rush_adventure.jpg",
      WOODEN_PUZZLE_APP_ID to "8_wooden_puzzle_bliss.jpg",
      TANGRAM_APP_ID to "9_tangram_heaven.jpg",
      WATER_SORTER_APP_ID to "10_water_sorter.jpg",
      WORD_SEEKER_APP_ID to "11_word_seeker.jpg",
      SPACE_CONNECT_APP_ID to "12_space_connect.jpg",
      SUDOKU_APP_ID to "13_sudoku.jpg",
      MIX_BLOX_APP_ID to "14_mix_blox.jpg",
    )
    val variationsFolders = mapOf(
      Variations.ALL_GAMES_30MIN_EARN to "all_games_30min_earn",
      Variations.EACH_GAME_UNIQUE to "each_game_unique",
      Variations.ALL_GAMES_30MIN_DAILY to "all_games_30min_daily"
    )
    val CACHE_DURATION = 1L.minutes
  }

  private val userHadEarnings = buildCache(buildVariantProvider.get(), CACHE_DURATION) { userId: String ->
    coroutineScope.get().async { rewardingFacade.userEverHadEarnings(userId) }
  }

  suspend fun applyExperiment(userId: String, offer: OfferItemGameApiDto): OfferItemGameApiDto {
    if (userHadEarnings.get(userId).await())
      return offer

    return getExpImageUrlOrNull(
      variation = abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_PLAY_AT_LEAST_BADGES),
      applicationId = offer.applicationId
    )
      ?.let { offer.copy(imageUrl = imageService.toUrl(it)) }
      ?: offer
  }

  private fun getExpImageUrlOrNull(variation: BaseVariation, applicationId: String): String? =
    variationsFolders[variation]
      ?.let { folder ->
        badgedImages[applicationId]
          ?.let {
            "$folder/$it"
          }
      }
}