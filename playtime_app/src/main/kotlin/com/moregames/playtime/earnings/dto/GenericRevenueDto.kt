package com.moregames.playtime.earnings.dto

import com.moregames.base.applovin.JP_VIDEO_AD_ITEM_IDS
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.messaging.dto.RevenueReceivedEventDto.RevenueSource
import com.moregames.base.messaging.dto.RevenueReceivedEventDto.RevenueSource.Companion.offerwallRevenueSources
import java.math.BigDecimal
import java.time.Instant

data class GenericRevenueDto(
  val id: String,
  val userId: String,
  val source: RevenueSource,
  val timestamp: Instant,
  val amount: BigDecimal,
  val amountExtra: BigDecimal?,
  val gameId: Int?,
  val taskId: String? = null,
)

fun List<GenericRevenueDto>.gamesRevenues(): List<GenericRevenueDto> =
  this.filter { it.source == RevenueReceivedEventDto.RevenueSource.APPLOVIN && it.gameId !in JP_VIDEO_AD_ITEM_IDS }

fun List<GenericRevenueDto>.offerWallRevenues(): List<GenericRevenueDto> =
  this.filter { it.source in offerwallRevenueSources() }

fun List<GenericRevenueDto>.sumNoExtra(): BigDecimal = this.sumOf { it.amount }
fun List<GenericRevenueDto>.sumWithExtra(): BigDecimal = this.sumOf { it.amount + (it.amountExtra ?: BigDecimal.ZERO) }
