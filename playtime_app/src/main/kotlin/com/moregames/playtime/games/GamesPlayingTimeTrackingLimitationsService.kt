package com.moregames.playtime.games

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.util.IoCoroutineScope
import com.moregames.base.util.logger
import com.moregames.playtime.tracking.AdMarketEventComputationService.Companion.anyGameBannersShownThresholds
import com.moregames.playtime.user.UserService
import kotlinx.coroutines.withContext
import redis.clients.jedis.JedisPool
import redis.clients.jedis.params.SetParams
import javax.inject.Singleton

@Singleton
class GamesPlayingTimeTrackingLimitationsService @Inject constructor(
  private val jedisPool: JedisPool,
  private val coroutineScope: Provider<IoCoroutineScope>,
  private val userService: UserService
) {

  companion object {
    const val USER_GAME_BANNERS_STOP_COUNTING_PREFIX = "userGamesBannersStopCounting:%s##%s"
    const val STUB = "stub"
    private val WITH_LONG_EXPIRATION = SetParams().ex(7 * 24 * 60 * 60L)// 7 days in seconds
  }

  suspend fun shouldStopTrackingTimePlayed(userId: String, gameId: Int): Boolean {
    return isCountingStopped(userId, gameId)
    // todo we can also check user creation date and skip tracking if a user older than 2 days
  }

  suspend fun onBannersShown(userId: String, gameId: Int, timeElapsedInSeconds: Int) {
    val user = userService.getUser(userId)
    if (timeElapsedInSeconds >= anyGameBannersShownThresholds.getValue(user.appPlatform).flatMap { it.value }.maxOf { it.range.first }) {
      cacheStopCountingBanners(userId, gameId)
    }
  }

  private suspend fun cacheStopCountingBanners(userId: String, gameId: Int) {
    try {
      withContext(coroutineScope.get().coroutineContext) {
        jedisPool.resource.use { jedis ->
          jedis.set(
            /* key = */ USER_GAME_BANNERS_STOP_COUNTING_PREFIX.format(userId, gameId),
            /* value = */ STUB,
            /* params = */ WITH_LONG_EXPIRATION
          )
        }
      }
    } catch (e: Exception) {
      logger().warn("Error during saving value to redis cache", e)
    }
  }

  private suspend fun isCountingStopped(userId: String, gameId: Int): Boolean =
    try {
      withContext(coroutineScope.get().coroutineContext) {
        jedisPool.resource.use { jedis ->
          jedis.exists(USER_GAME_BANNERS_STOP_COUNTING_PREFIX.format(userId, gameId))
        }
      }
    } catch (e: Exception) {
      logger().warn("Got error on getting value from redis cache", e)
      false
    }
}
