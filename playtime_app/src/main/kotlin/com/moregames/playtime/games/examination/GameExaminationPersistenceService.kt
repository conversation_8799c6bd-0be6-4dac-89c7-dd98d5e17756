package com.moregames.playtime.games.examination

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.util.TimeService
import com.moregames.playtime.checks.dto.AttestationStatementDto
import com.moregames.playtime.checks.dto.NonceData
import com.moregames.playtime.games.examination.table.GameDeviceExaminationTable
import org.jetbrains.exposed.sql.*
import java.time.Instant

@Singleton
class GameExaminationPersistenceService @Inject constructor(
  database: Database,
  val timeService: TimeService
) : BasePersistenceService(database) {
  suspend fun initiateDeviceExamination(userId: String, gameId: Int, nonce: String, validUntil: Instant) = dbQuery {
    GameDeviceExaminationTable.insert {
      it[GameDeviceExaminationTable.nonce] = nonce
      it[GameDeviceExaminationTable.userId] = userId
      it[GameDeviceExaminationTable.gameId] = gameId
      it[GameDeviceExaminationTable.validUntil] = validUntil
      it[checkStatus] = GameExaminationStatus.PENDING.name
    }
  }

  suspend fun successfulExaminationExists(userId: String, gameId: Int): Boolean = dbQuery {
    GameDeviceExaminationTable
      .select {
        (GameDeviceExaminationTable.userId eq userId) and
          (GameDeviceExaminationTable.gameId eq gameId) and
          ((GameDeviceExaminationTable.checkStatus eq GameExaminationStatus.SUCCESS.name))
      }
      .count() > 0
  }

  suspend fun getNonceData(nonce: String): NonceData? = dbQuery {
    GameDeviceExaminationTable
      .slice(
        GameDeviceExaminationTable.userId,
        GameDeviceExaminationTable.validUntil,
        GameDeviceExaminationTable.examinedAt
      )
      .select { (GameDeviceExaminationTable.nonce eq nonce) }
      .map {
        NonceData(
          userId = it[GameDeviceExaminationTable.userId],
          validUntil = it[GameDeviceExaminationTable.validUntil],
          examinedAt = it[GameDeviceExaminationTable.examinedAt]
        )
      }
      .firstOrNull()
  }

  suspend fun saveAttestationPayload(payload: AttestationStatementDto, status: GameExaminationStatus): Int =
    dbQuery {
      GameDeviceExaminationTable
        .update({ GameDeviceExaminationTable.nonce eq payload.decodedNonce() }) {
          it[examinedAt] = Instant.ofEpochMilli(payload.timestampMs)
          it[apkPackageName] = payload.apkPackageName
          it[apkCertificateDigestSha256] = payload.decodedApkCertificateDigestSha256()
          it[apkDigestSha256] = payload.decodedApkDigestSha256()
          it[ctsProfileMatch] = payload.ctsProfileMatch
          it[basicIntegrity] = payload.basicIntegrity
          it[evaluationType] = payload.evaluationType
          it[advice] = payload.advice
          it[error] = payload.error
          it[versionCode] = payload.versionCode?.toInt()
          it[appLicensingVerdict] = payload.appLicensingVerdict
          it[appRecognitionVerdict] = payload.appRecognitionVerdict
          it[deviceRecognitionVerdict] = payload.deviceRecognitionVerdict?.toString()
          it[checkStatus] = status.name
        }
    }

  suspend fun saveErroneousAttestationPayload(nonce: String, payload: AttestationStatementDto): Int =
    dbQuery {
      GameDeviceExaminationTable
        .update({ GameDeviceExaminationTable.nonce eq nonce }) {
          it[examinedAt] = timeService.now()
          it[apkPackageName] = payload.apkPackageName
          it[apkCertificateDigestSha256] = payload.decodedApkCertificateDigestSha256()
          it[apkDigestSha256] = payload.decodedApkDigestSha256()
          it[ctsProfileMatch] = payload.ctsProfileMatch
          it[basicIntegrity] = payload.basicIntegrity
          it[evaluationType] = payload.evaluationType
          it[advice] = payload.advice
          it[error] = payload.error
          it[versionCode] = payload.versionCode?.toInt()
          it[appLicensingVerdict] = payload.appLicensingVerdict
          it[appRecognitionVerdict] = payload.appRecognitionVerdict
          it[deviceRecognitionVerdict] = payload.deviceRecognitionVerdict?.toString()
          it[checkStatus] = GameExaminationStatus.FAIL.name
        }
    }

}