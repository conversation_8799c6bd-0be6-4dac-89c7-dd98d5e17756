package com.moregames.playtime.subscribers

import com.google.inject.Inject
import com.justplayapps.service.rewarding.earnings.proto.convertRevenueToEarningsMessage
import com.moregames.base.bus.MessageBus
import com.moregames.base.messaging.dto.CashoutPeriodEndedEventDto
import com.moregames.base.messaging.push.GenericPushSubscriber
import com.moregames.base.util.toProto
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutPeriodsService

class CashoutPeriodEndedMessagePushSubscriber @Inject constructor(
  private val cashoutPeriodsService: CashoutPeriodsService,
  private val userService: UserService,
  private val messageBus: MessageBus,
  private val rewardingFacade: RewardingFacade,
) : GenericPushSubscriber<CashoutPeriodEndedEventDto>(CashoutPeriodEndedEventDto::class) {

  override suspend fun handle(message: CashoutPeriodEndedEventDto) {
    val userId = message.userId
    if (!userService.userExists(userId)) return

    // need to calculate goal before creating earnings, as goal is earnings-dependent
    val coinGoalUser = userService.loadCoinGoalUser(userId)
    val coinsBalance = rewardingFacade.getUserCurrentCoinsBalance(userId, coinGoalUser.appPlatform)
    val coinGoalReached = coinsBalance.goalCoins >= coinGoalUser.coinsGoal
    cashoutPeriodsService.trackLastPeriodGoalReached(userId, coinGoalReached)

    messageBus.publish(
      convertRevenueToEarningsMessage {
        this.userId = userId
        this.periodStart = message.periodStart.toProto()
        this.periodEnd = message.periodEnd.toProto()
        this.coinsGoal = coinGoalUser.coinsGoal
      }
    )
  }

  override val url: String = "cashout-period-ended"
}