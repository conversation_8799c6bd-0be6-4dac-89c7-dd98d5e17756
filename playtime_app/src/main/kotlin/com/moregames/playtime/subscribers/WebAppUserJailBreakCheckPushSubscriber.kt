package com.moregames.playtime.subscribers

import com.google.inject.Inject
import com.moregames.base.messaging.dto.WebAppUserJailBreakCheckEventDto
import com.moregames.base.messaging.push.GenericPushSubscriber
import com.moregames.playtime.web.WebUserService

class WebAppUserJailBreakCheckPushSubscriber @Inject constructor(
  private val webUserService: WebUserService,
) : GenericPushSubscriber<WebAppUserJailBreakCheckEventDto>(WebAppUserJailBreakCheckEventDto::class) {

  override suspend fun handle(message: WebAppUserJailBreakCheckEventDto) {
    webUserService.onJailBreakCheck(message)
  }


  override val url: String
    get() = "webapp-jail-break-check"
}