package com.moregames.playtime.security

import com.google.inject.Inject
import com.moregames.base.messaging.dto.SignatureProvider
import com.moregames.base.util.SignatureVerificationResult
import com.moregames.base.util.createPublicKey
import com.moregames.base.util.verifySignature
import java.security.PublicKey
import javax.inject.Singleton

@Singleton
class UserCryptoService @Inject constructor(
  private val userCryptoPersistenceService: UserCryptoPersistenceService
) {

  suspend fun saveUserPublicKey(userId: String, publicKeyText: String) {
    createPublicKey(publicKeyText)?.also {
      userCryptoPersistenceService.saveUserPublicKey(userId, publicKeyText)
    }
  }

  suspend fun verifySignedMessage(message: SignatureProvider<*>, userId: String): SignatureVerificationResult =
    loadUserPublicKey(userId)?.let { verifySignedMessageByLoadedPublicKey(message, publicKey = it) }
      ?: SignatureVerificationResult.invalid()

  fun verifySignedMessageByPublicKey(message: SignatureProvider<*>, publicKey: String): SignatureVerificationResult =
    createPublicKey(publicKey)?.let { verifySignedMessageByLoadedPublicKey(message, publicKey = it) }
      ?: SignatureVerificationResult.invalid()

  private fun verifySignedMessageByLoadedPublicKey(message: SignatureProvider<*>, publicKey: PublicKey): SignatureVerificationResult =
    message.signature?.let { verifySignature(publicKey, message.payload.getMessageForSignature(), signature = it) }
      ?: SignatureVerificationResult.invalidSignature()

  private suspend fun loadUserPublicKey(userId: String): PublicKey? =
    userCryptoPersistenceService.getUserPublicKey(userId)?.let { createPublicKey(keyText = it) }
}

