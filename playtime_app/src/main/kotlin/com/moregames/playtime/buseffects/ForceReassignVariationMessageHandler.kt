package com.moregames.playtime.buseffects

import com.moregames.base.abtesting.BaseVariation
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.bus.Message
import com.moregames.base.bus.MessageHandler
import com.moregames.base.util.TimeService
import com.moregames.playtime.user.abmigration.AbTestingMigrationPersistenceService
import javax.inject.Inject

class ForceReassignVariationMessageHandler @Inject constructor(
  private val abTestingMigrationPersistenceService: AbTestingMigrationPersistenceService,
  private val timeService: TimeService
) {
  @MessageHandler
  suspend fun handleForceReassignVariation(message: ForceReassignVariationMessage) = with(message) {
    abTestingMigrationPersistenceService.forceAssignVariationAndTrackMigration(userId, experiment, variation, timeService.now())
  }
}

data class ForceReassignVariationMessage(
  val userId: String,
  val experiment: ClientExperiment,
  val variation: BaseVariation,
) : Message