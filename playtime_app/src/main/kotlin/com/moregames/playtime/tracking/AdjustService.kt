package com.moregames.playtime.tracking

import com.google.inject.Inject
import com.moregames.base.dto.AppPlatform.*
import com.moregames.base.dto.TrackingDataType.IDFA
import com.moregames.base.dto.TrackingDataType.IDFV
import com.moregames.base.messaging.dto.AdMarketEvent
import com.moregames.base.util.logger
import com.moregames.base.util.redis.RedisCachedBuilder
import com.moregames.playtime.app.messaging.dto.AdjustCustomEvent
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.dto.UserExternalIds
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.offer.OfferwallCampaignService
import com.moregames.playtime.webhook.adjust.AdjustEventPersistenceService
import com.moregames.playtime.webhook.adjust.dto.AdjustInstallation
import redis.clients.jedis.params.SetParams
import javax.inject.Singleton

@Singleton
class AdjustService @Inject constructor(
  private val adjustApiClient: AdjustApiClient,
  private val adjustEventPersistenceService: AdjustEventPersistenceService,
  private val userPersistenceService: UserPersistenceService,
  redisCachedBuilder: RedisCachedBuilder,
  private val userService: UserService,
  private val fraudScoreService: FraudScoreService,
  private val offerwallCampaignService: OfferwallCampaignService,
) : AdMarketEventSenderBase {

  companion object {
    const val USER_DATA_CACHE_SEC = 60L * 60L * 24L
  }

  private val userAgentCache = redisCachedBuilder.build(
    keyPrefix = "UserAgent:",
    setParams = SetParams.setParams().ex(USER_DATA_CACHE_SEC)
  ) {
    adjustEventPersistenceService.loadLastUserAgentValueByUserId(it).orEmpty()
  }

  private val userIpCache = redisCachedBuilder.build(
    keyPrefix = "UserIp:",
    setParams = SetParams.setParams().ex(USER_DATA_CACHE_SEC)
  ) {
    userPersistenceService.getLastIpV4ByUserId(it).orEmpty()
  }

  override suspend fun sendAdMarketEvent(event: AdMarketEvent) {
    adjustApiClient.sendCustomEvent(
      AdjustCustomEvent(
        eventType = event.eventType,
        googleAdId = event.googleAdId,
        idfa = event.idfa,
        adjustId = event.adjustId,
        trackingId = event.trackingId,
        trackingType = event.trackingType,
        appPlatform = event.appPlatform,
        createdAt = event.createdAt,
        userAgent = getUserAgent(event.userId),
        ipAddress = getUserIp(event.userId)
      ),
      extraParams = event.extraParams.orEmpty(),
      partnerParams = event.partnerParams.orEmpty(),
    )
  }

  suspend fun processInstallEvent(installation: AdjustInstallation) {
    adjustEventPersistenceService.saveInstallation(installation).let { isNewInstallation ->
      if (isNewInstallation) {
        fraudScoreService.onAdjustDataReceived(installation)
        offerwallCampaignService.onAdjustInstall(installation)
        if (isInstallationContainsExternalIds(installation)) {
          tryUpdateExternalIds(UserExternalIds.fromAdjustInstallation(installation))
        }
      }
    }
  }

  suspend fun processUpdatedAttributionEvent(installation: AdjustInstallation) {
    adjustEventPersistenceService.updateInstallation(installation)?.let { updatedInstallation ->
      fraudScoreService.onAdjustDataReceived(updatedInstallation)
      if (isInstallationContainsExternalIds(updatedInstallation)) {
        tryUpdateExternalIds(UserExternalIds.fromAdjustInstallation(updatedInstallation))
      }
    }
  }

  suspend fun getUserAgent(userId: String): String =
    userAgentCache.get(userId)

  suspend fun getUserIp(userId: String): String =
    userIpCache.get(userId)

  suspend fun isOrganic(userId: String): Boolean? = adjustEventPersistenceService.loadLastAdjustInstallationByUserId(userId)?.isOrganic()

  private fun isInstallationContainsExternalIds(installation: AdjustInstallation) =
    !installation.googleAdId.isNullOrBlank() || !installation.idfv.isNullOrBlank() || !installation.adjustId.isNullOrBlank()

  private suspend fun tryUpdateExternalIds(adjustExternalIds: UserExternalIds) {
    val dbExternalIds = userService.fetchExternalIds(adjustExternalIds.userId) ?: let {
      logger().warn("Unknown userId in Adjust install event: $adjustExternalIds")
      return
    }

    tryUpdateAdjustId(dbExternalIds, adjustExternalIds)

    val adjustTrackingData = adjustExternalIds.trackingData ?: return

    when (adjustTrackingData.platform) {
      ANDROID -> if (adjustTrackingData.type == IDFA) tryUpdateGoogleAdId(dbExternalIds, adjustExternalIds)
      IOS -> if (adjustTrackingData.type == IDFV && dbExternalIds.trackingData?.platform != IOS_WEB)
        tryUpdateTrackingData(dbExternalIds, adjustExternalIds)

      IOS_WEB -> {}
    }
  }

  private suspend fun tryUpdateAdjustId(dbExternalIds: UserExternalIds, adjustExternalIds: UserExternalIds) {
    if (adjustExternalIds.adjustId != null && dbExternalIds.adjustId == null) {
      userService.updateAdjustId(adjustExternalIds.userId, adjustExternalIds.adjustId)
    }
  }

  private suspend fun tryUpdateGoogleAdId(dbExternalIds: UserExternalIds, adjustExternalIds: UserExternalIds) {
    if (dbExternalIds.googleAdId != null) {
      if (dbExternalIds.googleAdId != adjustExternalIds.googleAdId) {
        logger().warn(
          "User '${dbExternalIds.userId}' have different googleAdId in DB (${dbExternalIds.googleAdId}) and Adjust event (${adjustExternalIds.googleAdId})"
        )
      }
    } else {
      userService.updateValidGoogleAdId(dbExternalIds.userId, adjustExternalIds.googleAdId!!, null)
    }
  }

  private suspend fun tryUpdateTrackingData(dbExternalIds: UserExternalIds, adjustExternalIds: UserExternalIds) {
    if (dbExternalIds.trackingData != null) {
      if (dbExternalIds.trackingData != adjustExternalIds.trackingData) {
        logger().warn(
          "User '${dbExternalIds.userId}' have different trackingData in DB (${dbExternalIds.trackingData}) and Adjust event (${adjustExternalIds.trackingData})"
        )
      }
    } else {
      userService.updateValidTrackingData(adjustExternalIds.userId, adjustExternalIds.trackingData!!)
    }
  }

}