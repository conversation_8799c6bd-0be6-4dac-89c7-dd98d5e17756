package com.moregames.playtime.carousel

import com.justplayapps.playtime.carousel.firstTaskCompletedEvent
import com.moregames.base.bus.*
import com.moregames.playtime.carousel.domain.UserCarouselTask.Claimed
import java.util.*
import javax.inject.Inject

class CarouselTaskLifecycleHandlers @Inject constructor(
  private val carouselService: CarouselService,
  private val messageBus: MessageBus,
) {
  @MessageHandler
  suspend fun handleCarouselTaskFinishedEvent(event: CarouselTaskFinishedEvent) {
    val count = carouselService.countTasksInTerminalStates(event.userId)
    if (count == 1L) {
      messageBus.publish(firstTaskCompletedEvent {
        taskId = event.taskId.toString()
      })
    }
  }

  @MessageHandler
  suspend fun handleCarouselRecreateTaskCommand(command: CarouselRecreateTaskCommand) {
    val previousTask = carouselService.getTask(command.taskId)
    if (previousTask !is Claimed) return

    carouselService.completeTask(previousTask.taskId)

    if (!previousTask.definition.enabled) return

    carouselService.createTask(previousTask.userId, previousTask.definition.id)
  }

  @EffectHandler
  suspend fun handleGenerateInitialTasksListCommand(command: GenerateInitialTasksListCommand) {
    carouselService.generateInitialTasksList(command.userId)
  }
}

data class CarouselTaskFinishedEvent(val taskId: UUID, val userId: String) : Message
data class CarouselRecreateTaskCommand(val taskId: UUID) : Message
data class GenerateInitialTasksListCommand(val userId: String) : Effect