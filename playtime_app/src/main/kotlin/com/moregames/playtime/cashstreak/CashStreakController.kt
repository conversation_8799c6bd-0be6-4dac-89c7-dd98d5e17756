package com.moregames.playtime.cashstreak

import com.moregames.base.util.logger
import com.moregames.playtime.user.UserController.Companion.userId
import com.moregames.playtime.util.getLocale
import io.ktor.application.*
import io.ktor.response.*
import io.ktor.routing.*
import kotlinx.serialization.ExperimentalSerializationApi
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
@ExperimentalSerializationApi
class CashStreakController @Inject constructor(
  private val cashStreakService: CashStreakService
) {

  fun startRouting(root: Route) {
    root.route("/cash-streak") {

      get("/widget") {
        call.respond(
          cashStreakService.getCashStreakWidget(userId(), getLocale(logger()))
        )
      }

      post("/claim") {
        cashStreakService.claimRewards(userId())
        call.respond(cashStreakService.getCashStreakWidget(userId(), getLocale(logger())))
      }

      get("/status") {
        call.respond(
          cashStreakService.getCashStreakStatus(userId(), getLocale(logger()))
        )
      }

    }
  }
}