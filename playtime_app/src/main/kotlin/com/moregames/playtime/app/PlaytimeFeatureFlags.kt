package com.moregames.playtime.app

import com.moregames.base.featureflags.FeatureFlag
import com.moregames.base.featureflags.FeatureFlagsFacade

enum class PlaytimeFeatureFlags(override val key: String) : FeatureFlag {
  FACE_VERIFICATION_RETRIES("face-verification-retries"),
  USERS_TEMPORAL_RESTRICTIONS("users-temporal-restrictions"),
  ENABLE_ADJUST_CB("enable-adjust-circuit-breaker"),
  SEON_DAILY_HARD_LIMIT("seon-daily-hard-limit"),
  ENABLE_ADJUST_LATEST_SERVER_ENDPOINT("enable-adjust-latest-server-endpoint"),
  SEND_AD_MONETIZATION_EVENTS("send-ad-monetization-events"),
  IOS_USER_INTERVIEW_DISABLED("ios-user-interview-disabled"),
  IOS_USER_SURVEY_DISABLED("ios-user-survey-disabled"),
  IOS_ALLOW_REVIEWER_MODE("ios-allow-reviewer-mode"),
  ANDROID_ALLOW_LAZY_APPLOVIN_INITIALIZATION("android-allow-lazy-applovin-initialization"),
  ENABLE_MOLOCO_INTEGRATION("enable-moloco-integration"),
  DO_NOT_SEND_AMPLITUDE_EVENTS_AFTER_X_DAYS("do_not_send_amplitude_events_after_x_days"),
  FORBID_IOS_BIPA_USER_CREATION("forbid_ios_bipa_user_creation"),
  FORBID_IOS_BIPA_CASHOUT_WITH_FACE_VERIFICATION("forbid_ios_bipa_cashout_with_face_verification"),
  PUSH_NOTIFICATIONS_TRACKING_ENABLED("push-notifications-tracking-enabled"),
  USE_INCREASED_EARNINGS_THRESHOLD("use-increased-earnings-threshold"),
  SHARE_OF_TAPJOY_OFFERWALL_USERS("share-of-tapjoy-offerwall-users"),
  SHARE_OF_FYBER_OFFERWALL_USERS("share-of-fyber-offerwall-users"),
  SEND_GAMES_WITH_REVENUE_EVENTS("send-games-with-revenue-events"),
  CHALLENGE_EVENT_REVENUE_SHARE("challenge-event-revenue-share"),
  SPECIAL_CHALLENGE_REVENUE_SHARE("special-challenge-revenue-share"),
  USE_NEW_FACETEC_SERVER("use-new-facetec-server"),
  ANDROID_BLOCK_BUSTER_3RD("android-block-buster-3rd"),
  DISABLE_CASHOUT_FOR_CTS_PROFILE_MATCH_FALSE("disable-cashout-for-cts-profile-match-false"),
  USER_REVENUE_MIN_X_CACHE_TTL_DAYS("user_revenue_min_x_cache_ttl_days"),
  TAPJOY_CURRENCY_SALE_MULTIPLIER("tapjoy-currency-sale-parameter"),
  LIMIT_EARNINGS_FOR_USERS_CREATED_AFTER_MAY292025("limit-earnings-for-users-created-after-may292025"),
  USE_SEON_V3("use-seon-v3"),
}

fun FeatureFlagsFacade.iosUserInterviewDisabled() = boolValue(PlaytimeFeatureFlags.IOS_USER_INTERVIEW_DISABLED)

fun FeatureFlagsFacade.iosUserSurveyDisabled() = boolValue(PlaytimeFeatureFlags.IOS_USER_SURVEY_DISABLED)

fun FeatureFlagsFacade.adjustCbEnabled() = boolValue(PlaytimeFeatureFlags.ENABLE_ADJUST_CB)

fun FeatureFlagsFacade.seonDailyHardLimit() = intValue(PlaytimeFeatureFlags.SEON_DAILY_HARD_LIMIT, 700)

fun FeatureFlagsFacade.adjustLatestServerEndpointEnabled() = boolValue(PlaytimeFeatureFlags.ENABLE_ADJUST_LATEST_SERVER_ENDPOINT)

fun FeatureFlagsFacade.sendAdMonetizationEvents() = boolValue(PlaytimeFeatureFlags.SEND_AD_MONETIZATION_EVENTS)

fun FeatureFlagsFacade.allowIosReviewerMode() = boolValue(PlaytimeFeatureFlags.IOS_ALLOW_REVIEWER_MODE)

fun FeatureFlagsFacade.androidAllowLazyApplovinInitialization() = boolValue(PlaytimeFeatureFlags.ANDROID_ALLOW_LAZY_APPLOVIN_INITIALIZATION)

fun FeatureFlagsFacade.enableMolocoIntegration() = boolValue(PlaytimeFeatureFlags.ENABLE_MOLOCO_INTEGRATION)

fun FeatureFlagsFacade.maxUserAgeForAmplitudeAnalytics() = intValue(PlaytimeFeatureFlags.DO_NOT_SEND_AMPLITUDE_EVENTS_AFTER_X_DAYS, 0)

fun FeatureFlagsFacade.forbidIosBipaUserCreation() = boolValue(PlaytimeFeatureFlags.FORBID_IOS_BIPA_USER_CREATION, false)

fun FeatureFlagsFacade.forbidIosBipaCashoutWithFaceVerification() = boolValue(PlaytimeFeatureFlags.FORBID_IOS_BIPA_CASHOUT_WITH_FACE_VERIFICATION, false)

fun FeatureFlagsFacade.isPushNotificationsTrackingEnabled() = boolValue(PlaytimeFeatureFlags.PUSH_NOTIFICATIONS_TRACKING_ENABLED, false)

fun FeatureFlagsFacade.useIncreasedEarningsThreshold() = boolValue(PlaytimeFeatureFlags.USE_INCREASED_EARNINGS_THRESHOLD, false)

fun FeatureFlagsFacade.shareOfTapjoyOfferwallUsers() = doubleValue(PlaytimeFeatureFlags.SHARE_OF_TAPJOY_OFFERWALL_USERS, 1.0)

fun FeatureFlagsFacade.shareOfFyberOfferwallUsers() = doubleValue(PlaytimeFeatureFlags.SHARE_OF_FYBER_OFFERWALL_USERS, 1.0)

fun FeatureFlagsFacade.sendGamesWithRevenueEvents() = boolValue(PlaytimeFeatureFlags.SEND_GAMES_WITH_REVENUE_EVENTS)

fun FeatureFlagsFacade.challengeEventRevenueShare(fallback: Double) = doubleValue(PlaytimeFeatureFlags.CHALLENGE_EVENT_REVENUE_SHARE, fallback)
fun FeatureFlagsFacade.specialChallengeRevenueShare(fallback: Double) = doubleValue(PlaytimeFeatureFlags.SPECIAL_CHALLENGE_REVENUE_SHARE, fallback)


fun FeatureFlagsFacade.useNewFacetecServer() = boolValue(PlaytimeFeatureFlags.USE_NEW_FACETEC_SERVER)

fun FeatureFlagsFacade.isAndroidBlockBuster3rdEnabled() = boolValue(PlaytimeFeatureFlags.ANDROID_BLOCK_BUSTER_3RD, false)

fun FeatureFlagsFacade.isCashoutDisabledForCtsProfileMatchFalse() = boolValue(PlaytimeFeatureFlags.DISABLE_CASHOUT_FOR_CTS_PROFILE_MATCH_FALSE, false)

fun FeatureFlagsFacade.tapjoyCurrencySaleMultiplier() = doubleValue(PlaytimeFeatureFlags.TAPJOY_CURRENCY_SALE_MULTIPLIER, 1.5)

fun FeatureFlagsFacade.userRevenueMinCacheTtlDays() = intValue(PlaytimeFeatureFlags.USER_REVENUE_MIN_X_CACHE_TTL_DAYS, 1)

fun FeatureFlagsFacade.limitEarningsForUsersCreatedAfterMay292025() = boolValue(PlaytimeFeatureFlags.LIMIT_EARNINGS_FOR_USERS_CREATED_AFTER_MAY292025, true)

