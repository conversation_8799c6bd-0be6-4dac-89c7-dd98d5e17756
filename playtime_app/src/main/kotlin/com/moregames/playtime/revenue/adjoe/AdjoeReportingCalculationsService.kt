package com.moregames.playtime.revenue.adjoe

import com.google.inject.Inject
import com.moregames.playtime.earnings.CashoutSettingsService
import com.moregames.playtime.rewarding.RewardingFacade
import java.math.BigDecimal
import java.math.RoundingMode
import javax.inject.Singleton

@Singleton
class AdjoeReportingCalculationsService @Inject constructor(
  private val rewardingFacade: RewardingFacade,
  private val cashoutSettingsService: CashoutSettingsService,
) {

  suspend fun calculateCoinsAndDollars(coinsReport: AdjoeCoinsReportDto): AdjoeCoinsAndDollarsCalculationResult = with(coinsReport) {
    val abInflatingCoinsMultiplier = rewardingFacade.inflatingCoinsMultiplier(userId)
    val tapjoyCoinsToUsdConversionRatio = cashoutSettingsService.getAdjoeCoinsToUsdConversionRatio()

    val usdAmount = coins.toBigDecimal()
      .divide(abInflatingCoinsMultiplier.toBigDecimal())
      .divide(tapjoyCoinsToUsdConversionRatio, 6, RoundingMode.HALF_UP)

    return AdjoeCoinsAndDollarsCalculationResult(usdAmount, coins)
  }
}

data class AdjoeCoinsAndDollarsCalculationResult(
  val usdAmount: BigDecimal,
  val coins: Int
)