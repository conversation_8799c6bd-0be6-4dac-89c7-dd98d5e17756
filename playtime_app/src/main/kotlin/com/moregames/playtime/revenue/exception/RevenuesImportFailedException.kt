package com.moregames.playtime.revenue.exception

import com.moregames.base.exceptions.BaseException
import com.moregames.base.exceptions.ErrorType
import com.moregames.base.exceptions.PlaytimeErrorCodes

class RevenuesImportFailedException : BaseException("Revenues import failed") {
  override val errorCode = PlaytimeErrorCodes.REVENUES_IMPORT_FAILED
  override val errorType = ErrorType.SERVER_ERROR
}
