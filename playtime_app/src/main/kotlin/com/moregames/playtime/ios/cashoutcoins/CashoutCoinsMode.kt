package com.moregames.playtime.ios.cashoutcoins

import com.moregames.base.abtesting.BaseVariation
import com.moregames.base.abtesting.Variations
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
enum class CashoutCoinsMode {

  @SerialName("cashoutNow")
  CASHOUT_NOW,

  @SerialName("cashoutForCoins")
  CASHOUT_FOR_COINS,

  @SerialName("homeScreenCoins")
  HOME_SCREEN_COINS,

  @SerialName("deadline15Minutes")
  DEADLINE_15_MINUTES,

  @SerialName("deadline30Minutes")
  DEADLINE_30_MINUTES,

  @SerialName("cashoutQuitConfirmation")
  CASHOUT_QUIT_CONFIRMATION,
  ;

  companion object {
    fun fromVariationOrNull(variation: BaseVariation) =
      when (variation) {
        Variations.ANDROID_CASHOUT_NOW -> CASHOUT_NOW
        Variations.ANDROID_CASHOUT_COINS -> CASHOUT_FOR_COINS
        Variations.ANDROID_HOME_SCREEN_COINS -> HOME_SCREEN_COINS
        Variations.ANDROID_DEADLINE_15_MINUTES -> DEADLINE_15_MINUTES
        Variations.ANDROID_DEADLINE_30_MINUTES -> DEADLINE_30_MINUTES
        Variations.ANDROID_CASHOUT_QUIT_CONFIRMATION -> CASHOUT_QUIT_CONFIRMATION
        else -> null
      }
  }
}