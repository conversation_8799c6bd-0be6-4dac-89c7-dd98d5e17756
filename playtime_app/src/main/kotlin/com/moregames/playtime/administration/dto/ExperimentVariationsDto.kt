package com.moregames.playtime.administration.dto

import com.moregames.base.abtesting.dto.Variation
import com.papsign.ktor.openapigen.annotations.Request
import com.papsign.ktor.openapigen.annotations.Response
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import java.math.BigDecimal
import java.time.Instant

@Request("Experiment variations")
@Response("Experiment variations")
@Serializable
data class ExperimentVariationsDto(
  @Contextual val startedAt: Instant?,
  @Contextual val finishedAt: Instant?,
  val minimumAppVersion: Int?,
  val variations: Map<String, @Contextual BigDecimal>
) {
  companion object {
    fun from(variations: List<Variation>): ExperimentVariationsDto {
      if (variations.map { it.experiment.key }.distinct().size > 1)
        throw IllegalStateException("Expected variations only of one Experiment, but more found: $variations")

      val experiment = variations.firstOrNull()?.experiment

      return ExperimentVariationsDto(
        startedAt = experiment?.startedAt,
        finishedAt = experiment?.finishedAt,
        minimumAppVersion = experiment?.minimumAppVersion,
        variations = variations.associate { it.key to it.allocation }
      )
    }
  }
}

