plugins {
  id 'application'
  id 'org.jetbrains.kotlin.jvm'
  id 'com.github.johnrengelman.shadow'
  id 'org.jetbrains.kotlin.kapt'
  id 'org.jetbrains.kotlin.plugin.serialization' version "${kotlin_version}"
  id 'com.google.cloud.tools.jib'
  id 'com.bmuschko.docker-remote-api'
}

ext.dockerImageName = 'accounting'

jib {
  to {
    image = "us.gcr.io/${docker_image_project}/${project.ext.dockerImageName}:${docker_image_tag}"
  }
  from {
    image = "europe-west2-docker.pkg.dev/serverless-runtimes/google-22/runtimes/java21"
  }
  container {
    mainClass = "io.ktor.server.netty.EngineMain"
  }
}

evaluationDependsOn(':base')

apply from: 'dependencies.gradle'

application {
  mainClassName = "io.ktor.server.netty.EngineMain"
}

jar {
  manifest {
    attributes 'Class-Path': 'opentelemetry-javaagent.jar exporter-auto-0.27.0-alpha-shaded.jar propagators-gcp-0.27.0-alpha.jar'
  }
}

shadowJar {
  zip64 true
  mergeServiceFiles()
}

build {
  copy {
    from { files("../base/libs/opentelemetry-javaagent.jar", "../base/libs/exporter-auto-0.27.0-alpha-shaded.jar", "../base/libs/propagators-gcp-0.27.0-alpha.jar") }
    into { "${buildDir}/libs" }
  }
}

tasks.run {
  doFirst {
    environment "NODE_ENV", "local"
  }
}

tasks.assemble {
  dependsOn(tasks.shadowJar)
}

tasks.testClasses {
  dependsOn(tasks.getByPath(':base:testClasses'))
}

compileKotlin {
  kotlinOptions {
    jvmTarget = JavaVersion.VERSION_21.toString()
  }
}

compileTestKotlin {
  kotlinOptions {
    jvmTarget = JavaVersion.VERSION_21.toString()
  }
}

test {
  useJUnitPlatform()
  minHeapSize = "1024m"
  maxHeapSize = "4096m"
  systemProperties 'noDocker': System.getProperty('noDocker') // bypass for tests for switch in DatabaseTestBase
  testLogging {
    showStandardStreams = true
  }
  jvmArgs "--add-opens=java.base/java.lang=ALL-UNNAMED"
}
