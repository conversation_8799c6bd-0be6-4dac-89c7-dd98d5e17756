package com.justplayapps.service.reporting.revenue.applovin

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.justplayapps.service.reporting.revenue.applovin.RevenueImportService.Companion.REPORTING_URL
import com.justplayapps.service.reporting.revenue.applovin.dto.ApplovinAggregatedRevenueDto
import com.justplayapps.service.reporting.revenue.applovin.dto.ApplovinRequest
import com.justplayapps.service.reporting.revenue.applovin.exception.ApplovinDataLoadInvalidException
import com.justplayapps.service.reporting.revenue.applovin.exception.ApplovinRequestException
import com.justplayapps.service.reporting.revenue.applovin.exception.ApplovingParseResponseException
import com.justplayapps.service.reporting.utils.getDefaultJsonConverter
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.GenericMessagePublisher
import com.moregames.base.util.TimeService
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.client.features.json.*
import io.ktor.client.features.json.serializer.*
import io.ktor.http.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import kotlinx.serialization.ExperimentalSerializationApi
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import kotlin.test.assertFailsWith

@OptIn(ExperimentalSerializationApi::class)
class RevenueImportServiceTest {

  private val httpClient = mockHttpClient()
  private val genericMessagePublisher: GenericMessagePublisher = mock()
  private val testScope = TestScope()
  private val timeService: TimeService = mock()

  private val service = RevenueImportService(
    httpClient,
    jsonConverter = getDefaultJsonConverter(),
    genericMessagePublisher = genericMessagePublisher,
    timeService = timeService
  )

  private companion object {
    const val apiKey = "apiKey"
    const val dayString = "2021-05-06"
    const val applicationId = "applicationId"
    const val noDataApplicationId = "noDataApplicationId"
    const val errorDataApplicationId = "errorDataApplicationId"
    const val wrongDataApplicationId = "wrongDataApplicationId"
    const val unparsableDataApplicationId = "unparsableDataApplicationId"
    const val errorResponseBody = "unhandled exception"
    const val errorHeaderContent = "error header"
    val batchSize = 100
  }

  private val now = Instant.now()

  @BeforeEach
  fun init() {
    whenever(timeService.now()).thenReturn(now)
    Dispatchers.setMain(StandardTestDispatcher())
  }

  @Test
  fun `SHOULD load applovin revenue ON fetchApplovinRevenues`() = testScope.runTest {
    val result =
      service.fetchApplovinRevenues(
        ApplovinRequest(
          applicationId = applicationId,
          apiKey = apiKey,
          platform = AppPlatform.ANDROID
        ),
        LocalDate.parse(dayString),
        batchSize
      )
    assertThat(result).isEqualTo(2)
    val batch = listOf(
      ApplovinAggregatedRevenueDto(
        adUnitId = "396de120011b4732",
        idfa = "bdfbcb05-f3f9-43a4-9604-f1e44f34616f",
        idfv = "82cae1de-66d9-c09a-d959-2aa330a86dfa",
        userId = "ac05903c-6a97-4745-b9b8-c414465d523f",
        revenueUsd = BigDecimal("0.134111"),
        platform = AppPlatform.ANDROID,
        createdAt = timeService.now()
      ),
      ApplovinAggregatedRevenueDto(
        adUnitId = "396de120011b4732",
        idfa = "9281258b-2c32-4cfd-9a40-b7da26fd81a2",
        idfv = "902c2718-dcc5-f3cb-12c6-23cbaa2a418a",
        userId = "5c559a2b-5866-4e02-9fa8-62ae37781d7c",
        revenueUsd = BigDecimal("0.524037"),
        platform = AppPlatform.ANDROID,
        createdAt = timeService.now()
      )
    )
    verifyBlocking(genericMessagePublisher, times(1)) {
      publish(batch)
    }
  }

  @Test
  fun `SHOULD skip applovin revenue ON fetchApplovinRevenues WHEN http error and message starts with Data does not exist for specified date`() =
    testScope.runTest {
      val actual =
        service.fetchApplovinRevenues(
          ApplovinRequest(
            applicationId = noDataApplicationId,
            apiKey = apiKey,
            platform = AppPlatform.ANDROID
          ),
          LocalDate.parse(dayString),
          batchSize
        )
      assertThat(actual).isEqualTo(0)
    }

  @Test
  fun `SHOULD throw ApplovinRequestException ON fetchApplovinRevenues WHEN http error`() {
    assertFailsWith(ApplovinRequestException::class) {
      runBlocking {
        service.fetchApplovinRevenues(
          ApplovinRequest(
            applicationId = errorDataApplicationId,
            apiKey = apiKey,
            platform = AppPlatform.ANDROID
          ),
          LocalDate.parse(dayString),
          batchSize
        )
      }
    }.let {
      assertThat(it.internalMessage).equals("""Applovin import request for $errorDataApplicationId -> $dayString failed with "$errorResponseBody" ($errorHeaderContent)" """)
    }
  }

  @Test
  fun `SHOULD fail parsing response with ApplovingParseResponseException ON fetchApplovinRevenues WHEN correct response is not returned`() {
    assertFailsWith(ApplovingParseResponseException::class) {
      runBlocking {
        service.fetchApplovinRevenues(
          ApplovinRequest(
            applicationId = unparsableDataApplicationId,
            apiKey = apiKey,
            platform = AppPlatform.ANDROID
          ),
          LocalDate.parse(dayString),
          batchSize
        )
      }
    }
  }

  @Test
  fun `SHOULD fail response with ApplovinDataLoadInvalidException ON fetchApplovinRevenues WHEN returned data signals error`() {
    assertFailsWith(ApplovinDataLoadInvalidException::class) {
      runBlocking {
        service.fetchApplovinRevenues(
          ApplovinRequest(
            applicationId = wrongDataApplicationId,
            apiKey = apiKey,
            platform = AppPlatform.ANDROID
          ),
          LocalDate.parse(dayString),
          batchSize
        )
      }
    }
  }


  private fun mockHttpClient() = HttpClient(MockEngine) {
    expectSuccess = false
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }
      serializer = KotlinxSerializer(json)
    }
    engine {
      addHandler { request ->
        when (request.url.toString()) {
          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$applicationId&aggregated=true" -> {
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            respond(
              """{
                |"status":200,
                |"url":"${javaClass.getResource("/applovin/limitedAggregatedReport.csv")!!.toExternalForm()}",
                |"ad_revenue_report_url":"${javaClass.getResource("/applovin/aggregatedReport.csv")!!.toExternalForm()}"
                |}""".trimMargin(),
              HttpStatusCode.OK,
              headersOf("Content-Type", ContentType.Application.Json.toString())
            )
          }

          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$applicationId&aggregated=false" -> {
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            respond(
              """{
                |"status":200,
                |"url":"${javaClass.getResource("/applovin/limitedNonAggregatedReport.csv")!!.toExternalForm()}",
                |"ad_revenue_report_url":"${javaClass.getResource("/applovin/nonAggregatedReport.csv")!!.toExternalForm()}"
                |}""".trimMargin(),
              HttpStatusCode.OK,
              headersOf("Content-Type", ContentType.Application.Json.toString())
            )
          }

          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$noDataApplicationId&aggregated=false",
          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$noDataApplicationId&aggregated=true" -> {
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            respond(
              """Data does not exist for specified date 29.11.2021""".trimMargin(),
              HttpStatusCode.NotFound,
              headersOf("Content-Type", ContentType.Application.Json.toString())
            )
          }

          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$errorDataApplicationId&aggregated=false",
          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$errorDataApplicationId&aggregated=true" -> {
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            respond(
              errorResponseBody,
              HttpStatusCode.BadRequest,
              headersOf(
                "Content-Type" to listOf(ContentType.Application.Json.toString()),
                "Applovin-Error" to listOf(errorHeaderContent),
              )
            )
          }

          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$unparsableDataApplicationId&aggregated=false",
          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$unparsableDataApplicationId&aggregated=true" -> {
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            respond(
              "unparsable json",
              // 202 - Accepted
              HttpStatusCode.fromValue(202),
              headersOf(
                "Content-Type" to listOf(ContentType.Application.Json.toString()),
              )
            )
          }

          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$wrongDataApplicationId&aggregated=false",
          "$REPORTING_URL?api_key=$apiKey&date=$dayString&platform=android&application=$wrongDataApplicationId&aggregated=true" -> {
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            respond(
              """{"status": 202, "url": "", "ad_revenue_report_url": ""}""",
              HttpStatusCode.OK,
              headersOf(
                "Content-Type" to listOf(ContentType.Application.Json.toString()),
              )
            )
          }

          else -> error("Unhandled ${request.url.fullPath}")
        }
      }
    }
  }
}