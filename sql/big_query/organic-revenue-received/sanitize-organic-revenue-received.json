{"dataSourceId": "scheduled_query", "datasetRegion": "us-central1", "destinationDatasetId": "analytics_sanitized", "displayName": "organic-revenue-received", "emailPreferences": {}, "name": "<generated on creation>", "nextRunTime": "<generated on creation>", "ownerInfo": {"email": "<generated on creation>"}, "params": {"destination_table_name_template": "organic-revenue-received", "query": "SELECT eventId, timestamp, source, amount, createdAt, networkId, amountExtra, adUnitFormat, countryCode, network, idfv, idfa, eventToken, ip, platform, packageName, adPlacement, adUnitId, adUnitName, applovinRevenue FROM `playspot-server-dev.analytics.organic-revenue-received` WHERE DATE(createdAt) = DATE_SUB(@run_date, INTERVAL 1 DAY) GROUP BY eventId, timestamp, source, amount, createdAt, networkId, amountExtra, adUnitFormat, countryCode, network, idfv, idfa, eventToken, ip, platform, packageName, adPlacement, adUnitId, adUnitName, applovinRevenue", "write_disposition": "WRITE_APPEND"}, "schedule": "every day 01:20", "scheduleOptions": {"startTime": "2023-08-02T01:20:00Z"}, "state": "<generated on creation>", "updateTime": "<generated on creation>", "userId": "<generated on creation>"}