package com.justplayapps.proxybase.paypal

import com.google.gson.GsonBuilder
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.justplayapps.proxybase.PaymentAccounts
import com.justplayapps.proxybase.paypal.dto.PaypalRequestDto
import com.justplayapps.proxybase.paypal.dto.PaypalResponseDto
import com.justplayapps.proxybase.utils.ClassTypeAdapter
import com.paypal.http.HttpRequest
import com.paypal.http.HttpResponse
import io.ktor.client.*
import io.ktor.client.request.*
import io.ktor.http.*
import org.slf4j.LoggerFactory

class PayPalApiProxyFacade(private val httpClient: HttpClient) {
  companion object {
    val logger = LoggerFactory.getLogger(PayPalApiProxyFacade::class.java)
    val gson = GsonBuilder()
      .registerTypeAdapter(Class::class.java, ClassTypeAdapter())
      .create()
  }

  suspend fun <T> proxyCall(
    url: String,
    account: PaymentAccounts,
    request: HttpRequest<T>,
  ): HttpResponse<T> {
    val paypalResponse = httpClient.post<PaypalResponseDto>(url) {
      body = PaypalRequestDto(
        paymentAccounts = account,
        request = gson.toJson(request),
        bodyClass = if (request.requestBody() != null) gson.toJson(request.requestBody().javaClass) else null
      )
      header(HttpHeaders.ContentType, "application/json")
      header(HttpHeaders.Accept, "application/json")
    }
    if (paypalResponse.httpError != null) {
      handleError(account, paypalResponse.httpError)
    }
    val collectionType = TypeToken.getParameterized(HttpResponse::class.java, request.responseClass()).type
    return gson.fromJson(paypalResponse.response, collectionType)
  }

  private fun handleError(account: PaymentAccounts, errorJson: String) {
    logger.error("PayPal error: $errorJson")

    val detailMessage = parseJson(errorJson)["detailMessage"]?.asString
      ?: throw CustomHttpException("Missing detailMessage", rawResponse = errorJson)

    val detail = parseJson(detailMessage)
    val detailEntry = detail["details"]?.asJsonArray?.firstOrNull()?.asJsonObject

    throw CustomHttpException(
      message = detail["message"]?.asString ?: "Unknown PayPal error on account: $account",
      name = detail["name"]?.asString,
      debugId = detail["debug_id"]?.asString,
      issue = detailEntry?.get("issue")?.asString,
      field = detailEntry?.get("field")?.asString,
      href = detailEntry
        ?.get("link")?.asJsonArray
        ?.firstOrNull { it.asJsonObject["rel"]?.asString == "self" }
        ?.asJsonObject?.get("href")?.asString,
      rawResponse = errorJson
    )
  }

  private fun parseJson(json: String): JsonObject = try {
    gson.fromJson(json, JsonObject::class.java)
  } catch (e: Exception) {
    throw CustomHttpException(
      message = "Failed to parse PayPal error",
      rawResponse = json,
      cause = e
    )
  }

  data class CustomHttpException(
    override val message: String,
    val name: String? = null,
    val debugId: String? = null,
    val issue: String? = null,
    val field: String? = null,
    val href: String? = null,
    val rawResponse: String? = null,
    override val cause: Throwable? = null
  ) : RuntimeException(
    "PayPal error: name=$name, debug_id=$debugId, issue=$issue, field=$field, href=$href",
    cause
  )
}
