package com.justplayapps.service.orchestrator

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNotNull
import assertk.assertions.isNull
import com.google.common.net.HttpHeaders.AUTHORIZATION
import com.google.protobuf.util.JsonFormat
import com.justplayapps.games.common.userIds
import com.justplayapps.games.status.GameStatus.FirstLaunchRequest
import com.justplayapps.games.status.GameStatus.GameStatusResponse
import com.justplayapps.games.status.gameStatusRequest
import com.justplayapps.games.status.gameStatusResponse
import com.justplayapps.service.orchestrator.ApiManager.Companion.REDIRECT_BASIC_AUTH_USERNAME
import com.justplayapps.service.orchestrator.dto.GameExaminationDto
import com.justplayapps.service.orchestrator.dto.GameExaminationRequestApiDto
import com.justplayapps.service.orchestrator.dto.UserSearchResult
import com.justplayapps.service.orchestrator.iosgameattconsent.IosGameAttConsentService
import com.justplayapps.service.orchestrator.tracking.*
import com.justplayapps.service.orchestrator.util.OrchestratorGenericMessagePublisher
import com.justplayapps.service.orchestrator.util.OrchestratorSecrets
import com.justplayapps.service.orchestrator.util.RedirectUrlService
import com.justplayapps.service.orchestrator.util.getDefaultJsonConverter
import com.moregames.base.config.ServicesRegistry
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.GameExaminationRequestRouteDto
import com.moregames.base.dto.WayToFindUser
import com.moregames.base.http.MockedHandler
import com.moregames.base.http.mockUrl
import com.moregames.base.http.mockedHttpClient
import com.moregames.base.messaging.WebhookPublisher
import com.moregames.base.messaging.dto.WebAppUserAdditionalDataEventDto
import com.moregames.base.messaging.dto.WebAppUserJailBreakCheckEventDto
import com.moregames.base.messaging.dto.WebAppUserRegistrationEventDto
import com.moregames.base.secret.SecretService
import com.moregames.base.user.dto.WebAppExaminationChallengeApiDto
import com.moregames.base.user.dto.WebUserAdditionalData
import com.moregames.base.user.dto.WebUserStatusDto
import com.moregames.base.user.dto.WebUsersInGameVerification
import com.moregames.base.util.*
import com.moregames.base.util.Constants.AUTHENTICATION_TOKEN_PARAM_NAME
import com.moregames.base.vpn.SanityCheckRequestDto
import com.moregames.base.vpn.SanityCheckResponseApiDto
import io.ktor.application.*
import io.ktor.client.engine.mock.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.http.HttpMethod.Companion.Get
import io.ktor.http.HttpMethod.Companion.Post
import io.ktor.http.HttpStatusCode.Companion.Found
import io.ktor.http.HttpStatusCode.Companion.NotFound
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.routing.*
import io.ktor.serialization.*
import io.ktor.server.testing.*
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.*
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.*
import java.time.Instant
import java.util.*
import java.util.stream.Stream
import kotlin.test.assertEquals

class RedirectControllerTest {
  private val trackingService: TrackingService = mock()
  private val secretService: SecretService = mock()
  private val webhookPublisher: WebhookPublisher = mock()
  private val jsonConverter = getDefaultJsonConverter()
  private val userService: UserService = mock()
  private val iosGameAttConsentService: IosGameAttConsentService = mock()
  private val timeService: TimeService = mock()
  private val messagePublisher: OrchestratorGenericMessagePublisher = mock()

  private val httpClientHandler = mock<MockedHandler>()
  private val httpClient = mockedHttpClient(httpClientHandler, getDefaultJsonConverter())

  private companion object {
    const val USER_ID = "userIdValue"
    const val GOOGLE_AD_ID = "googleAdIdValue"
    const val IDFV = "idfvValue"
    const val IDFA = "idfaValue"
    const val MARKET = "marketValue"
    const val MARKET_SERVICE_ADDRESS = "marketServiceAddress"
    const val ORCHESTRATOR_AUTH_TOKEN_SALT = "authenticationToken"
    const val AUTHENTICATION_TOKEN_VALUE = "245efbb39cfdd5d7f8cefff6f13e1f96"
    const val PLAYTIME_PASSWORD = "playtimePassword"

    @JvmStatic
    fun platformProvider(): Stream<Arguments> = Stream.of(
      Arguments.of(AppPlatform.ANDROID, "android"),
      Arguments.of(IOS, "ios")
    )

    val sanityCheckRequestDto = SanityCheckRequestDto(
      ip = "ip",
      countryCode = "countryCode",
      vpnDetected = false,
      skipCache = true,
      packageId = "packageId",
      googleAdId = "googleAdId",
      idfv = "idfv",
      userId = "userId"
    )
  }

  private fun controller(): Application.() -> Unit = {
    install(StatusPages, orchestratorStatusPageConfig())
    this.install(ContentNegotiation) {
      json(
        json = jsonConverter
      )
    }
    this.initAuthenticationForRedirectController(password = PLAYTIME_PASSWORD)
    routing {
      RedirectController(
        trackingService = trackingService,
        webhookPublisher = webhookPublisher,
        jsonConverter = jsonConverter,
        userService = userService,
        redirectUrlService = RedirectUrlService(trackingService, secretService),
        iosGameAttConsentService = iosGameAttConsentService,
        timeService = timeService,
        httpClient = httpClient,
        messagePublisher = messagePublisher
      )()
    }
  }

  @BeforeEach
  fun before() {
    secretService.mock({ secretValue(OrchestratorSecrets.ORCHESTRATOR_AUTH_TOKEN_SALT) }, ORCHESTRATOR_AUTH_TOKEN_SALT)
    whenever(trackingService.getServiceAddressByMarket(eq(MARKET), any())).thenReturn(MARKET_SERVICE_ADDRESS)
  }

  @Test
  fun `SHOULD return 401 ON get WHEN basic auth is not provided`() = withTestApplication(controller()) {
    listOf(
      "/get/userStatus?userId=$USER_ID" to Get,
      "/get/banStatus?userId=$USER_ID" to Get,
      "/get/adUnitIdsByGoogleAdId?googleAdId=$GOOGLE_AD_ID" to Get,
      "/post/game-key?userId=$USER_ID" to Get,
      "/android/status?userId=$USER_ID" to Get,
      "/android/examination?userId=$USER_ID" to Get,
      "/android/examination?userId=$USER_ID" to Post,
      "/ios/status?userId=$USER_ID" to Get,
    ).forEach { (uri, method) ->
      val testCall: TestApplicationCall = handleRequest(
        method = method,
        uri = uri
      )

      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.Unauthorized)
    }
  }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD redirect to game progress ON post game-key`(platform: AppPlatform) = withTestApplication(controller()) {
    val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, platform)
    userService.mock({ findUser(UserId(USER_ID)) }, user)

    val testCall: TestApplicationCall = handleRequest(
      method = Get,
      uri = "/post/game-key?userId=$USER_ID&applicationId=com.gimica.solitaireverse&publicKey=MIIBI%2F%26%26j..AQAB"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }

    assertThat(testCall.response.status()).isEqualTo(Found)
    assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
      "https://$MARKET_SERVICE_ADDRESS/games/keys?applicationId=com.gimica.solitaireverse&JP_PLATFORM=${platform.name}&publicKey=MIIBI%2F%26%26j..AQAB&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    )
  }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD redirect to game progress by gaid  ON post game-key WHEN there is only gaid in query parameters`(platform: AppPlatform) =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, platform)
      userService.mock({ findUser(null, IDFA(GOOGLE_AD_ID), null) }, user)

      val testCall: TestApplicationCall = handleRequest(
        method = Get,
        uri = "/post/game-key?gaid=$GOOGLE_AD_ID&applicationId=com.gimica.solitaireverse&publicKey=MIIBI%2F%26%26j..AQAB"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }

      assertThat(testCall.response.status()).isEqualTo(Found)
      assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
        "https://$MARKET_SERVICE_ADDRESS/games/keys?applicationId=com.gimica.solitaireverse&JP_PLATFORM=${platform.name}"
          + "&publicKey=MIIBI%2F%26%26j..AQAB&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
      )
    }

  @ParameterizedTest
  @ValueSource(booleans = [false, true])
  fun `SHOULD fallback to googleAdId WHEN idfa is null or empty`(isNull: Boolean) = withTestApplication(controller()) {
    val idfa = if (isNull) null else ""

    val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, AppPlatform.ANDROID)
    userService.mock({ findUser(null, IDFA(GOOGLE_AD_ID), null) }, user)

    val idfaParam = if (idfa != null) "idfa=$idfa" else ""
    val testCall: TestApplicationCall = handleRequest(
      method = Get,
      uri = "/post/game-key?$idfaParam&googleAdId=$GOOGLE_AD_ID&applicationId=com.gimica.solitaireverse&publicKey=MIIBI%2F%26%26j..AQAB"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }

    assertThat(testCall.response.status()).isEqualTo(Found)
    assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
      "https://$MARKET_SERVICE_ADDRESS/games/keys?applicationId=com.gimica.solitaireverse&JP_PLATFORM=ANDROID"
        + "&publicKey=MIIBI%2F%26%26j..AQAB&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    )
  }

  @ParameterizedTest
  @CsvSource("null,null", "null,empty", "empty,null", "empty,empty")
  fun `SHOULD fallback to gaid WHEN idfa and googleAdId is null or empty`(idfaOption: String, googleAdIdOption: String) =
    withTestApplication(controller()) {
      val idfa = if (idfaOption == "null") null else ""
      val googleAdId = if (googleAdIdOption == "null") null else ""

      val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(null, IDFA("gaidValue"), null) }, user)

      val idfaParam = if (idfa != null) "idfa=$idfa" else ""
      val googleAdIdParam = if (googleAdId != null) "googleAdId=$googleAdId" else ""
      val testCall: TestApplicationCall = handleRequest(
        method = Get,
        uri = "/post/game-key?$idfaParam&$googleAdIdParam&gaid=gaidValue&applicationId=com.gimica.solitaireverse&publicKey=MIIBI%2F%26%26j..AQAB"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }

      assertThat(testCall.response.status()).isEqualTo(Found)
      assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
        "https://$MARKET_SERVICE_ADDRESS/games/keys?applicationId=com.gimica.solitaireverse&JP_PLATFORM=ANDROID"
          + "&publicKey=MIIBI%2F%26%26j..AQAB&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
      )
    }

  @ParameterizedTest
  @ValueSource(booleans = [false, true])
  fun `SHOULD fallback to empty or null gaid WHEN idfa and googleAdId are not defined`(isNull: Boolean) =
    withTestApplication(controller()) {
      val gaid = if (isNull) null else ""

      val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(null, IDFA(gaid), null) }, user)

      val gaidParam = if (gaid != null) "gaid=$gaid" else ""
      val testCall: TestApplicationCall = handleRequest(
        method = Get,
        uri = "/post/game-key?$gaidParam&applicationId=com.gimica.solitaireverse&publicKey=MIIBI%2F%26%26j..AQAB"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }

      assertThat(testCall.response.status()).isEqualTo(Found)
      assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
        "https://$MARKET_SERVICE_ADDRESS/games/keys?applicationId=com.gimica.solitaireverse&JP_PLATFORM=ANDROID"
          + "&publicKey=MIIBI%2F%26%26j..AQAB&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
      )
    }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD redirect to game progress by gaid ON post game-key WHEN there is only idfv in query parameters`(platform: AppPlatform) =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.IDFV, USER_ID, MARKET, platform)
      userService.mock({ findUser(null, null, IDFV(IDFV)) }, user)

      val testCall: TestApplicationCall = handleRequest(
        method = Get,
        uri = "/post/game-key?idfv=$IDFV&applicationId=com.gimica.solitaireverse&publicKey=MIIBI%2F%26%26j..AQAB"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }

      assertThat(testCall.response.status()).isEqualTo(Found)
      assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
        "https://$MARKET_SERVICE_ADDRESS/games/keys?applicationId=com.gimica.solitaireverse&JP_PLATFORM=${platform.name}"
          + "&publicKey=MIIBI%2F%26%26j..AQAB&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
      )
    }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD redirect to game progress by gaid ON post game-key WHEN there is an empty userId AND gaid in query parameters`(platform: AppPlatform) =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, platform)
      userService.mock({ findUser(UserId(""), IDFA(GOOGLE_AD_ID), null) }, user)

      val testCall: TestApplicationCall = handleRequest(
        method = Get,
        uri = "/post/game-key?gaid=$GOOGLE_AD_ID&userId=&applicationId=com.gimica.solitaireverse&publicKey=MIIBI%2F%26%26j..AQAB"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }

      assertThat(testCall.response.status()).isEqualTo(Found)
      assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
        "https://$MARKET_SERVICE_ADDRESS/games/keys?applicationId=com.gimica.solitaireverse&JP_PLATFORM=${platform.name}&publicKey=MIIBI%2F%26%26j..AQAB&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
      )
    }

  @ParameterizedTest
  @EnumSource(AppPlatform::class)
  fun `SHOULD redirect to game progress by googleAdId ON post game-key WHEN there is an empty userId and gaid and empty idfv in query parameters`(platform: AppPlatform) =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, platform)
      userService.mock({ findUser(UserId(""), IDFA(GOOGLE_AD_ID), IDFV("")) }, user)

      val testCall: TestApplicationCall = handleRequest(
        method = Get,
        uri = "/post/game-key?googleAdId=$GOOGLE_AD_ID&userId=&idfv=&applicationId=com.gimica.solitaireverse&publicKey=MIIBI%2F%26%26j..AQAB"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }

      assertThat(testCall.response.status()).isEqualTo(Found)
      assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
        "https://$MARKET_SERVICE_ADDRESS/games/keys?applicationId=com.gimica.solitaireverse&JP_PLATFORM=${platform.name}&publicKey=MIIBI%2F%26%26j..AQAB&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
      )
    }

  @ParameterizedTest
  @ValueSource(strings = ["gaid", "userId"])
  fun `SHOULD respond with not-found WHEN we failed to determine market`(option: String) = withTestApplication(controller()) {

    val testCall: TestApplicationCall = handleRequest(
      method = Get,
      uri = "/post/game-key?${if (option == "gaid") "gaid=googleAdIdValue" else "userId=userIdValue"}" +
        "&applicationId=com.gimica.solitaireverse&publicKey=MIIBI%2F%26%26j..AQAB"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }

    assertThat(testCall.response.status()).isEqualTo(NotFound)
  }

  @Test
  fun `SHOULD respond with not-found ON post game-key WHEN there is no id data in query params`() = withTestApplication(controller()) {
    val testCall = handleRequest(
      method = Get,
      uri = "/post/game-key?notgaid=$GOOGLE_AD_ID&applicationId=com.gimica.solitaireverse&publicKey=MIIBI%2F%26%26j..AQAB"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }
    assertThat(testCall.response.status()).isEqualTo(NotFound)
  }

  @Test
  fun `SHOULD respond with not-found ON post game-key WHEN there is an empty userId and gaid and idfv in query params`() = withTestApplication(controller()) {
    val testCall = handleRequest(
      method = Get,
      uri = "/post/game-key?gaid=&userId=&idfv=&applicationId=com.gimica.solitaireverse&publicKey=MIIBI%2F%26%26j..AQAB"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }
    assertThat(testCall.response.status()).isEqualTo(NotFound)
  }

  @ParameterizedTest
  @ValueSource(strings = ["no publicKey", "empty publicKey", "no appId", "empty appId"])
  fun `SHOULD throw errors ON post game-key WHEN there is an empty applicationId or publicKey in query params`(option: String) =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(null, IDFA(GOOGLE_AD_ID), null) }, user)
      val queryStringEnd = when (option) {
        "no publicKey" -> "&applicationId=com.gimica.solitaireverse"
        "empty publicKey" -> "&applicationId=com.gimica.solitaireverse&publicKey="
        "no appId" -> "&publicKey=MIIBI%2F%26%26j..AQAB"
        "empty appId" -> "&applicationId=&publicKey=MIIBI%2F%26%26j..AQAB"
        else -> "&applicationId=com.gimica.solitaireverse&publicKey=MIIBI%2F%26%26j..AQAB"
      }

      handleRequest(
        method = Get,
        uri = "/post/game-key?gaid=$GOOGLE_AD_ID$queryStringEnd"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let { call ->
        assertEquals(HttpStatusCode.BadRequest, call.response.status())
        println(call.response.content)
      }
    }

  @Test
  fun `SHOULD redirect to specific market ON get user status call`() = withTestApplication(controller()) {
    userService.mock({ findUser(UserId(USER_ID)) }, UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID))

    val testCall: TestApplicationCall = handleRequest(
      method = Get,
      uri = "/get/userStatus?userId=$USER_ID"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }

    assertThat(testCall.response.status()).isEqualTo(Found)
    assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
      "https://$MARKET_SERVICE_ADDRESS/ext/status?userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    )
  }

  @Test
  fun `SHOULD pass additional parameter ON get user status call`() = withTestApplication(controller()) {
    userService.mock({ findUser(UserId(USER_ID)) }, UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID))

    val testCall: TestApplicationCall = handleRequest(
      method = Get,
      uri = "/get/userStatus?userId=$USER_ID&userEmailBase64=***********************************************+"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }

    assertThat(testCall.response.status()).isEqualTo(Found)
    assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
      "https://$MARKET_SERVICE_ADDRESS/ext/status?userEmailBase64=***********************************************%2B&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    )
  }

  @Test
  fun `SHOULD redirect to specific market ON get ban status call`() = withTestApplication(controller()) {
    userService.mock({ findUser(UserId(USER_ID)) }, UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID))

    val testCall: TestApplicationCall = handleRequest(
      method = Get,
      uri = "/get/banStatus?userId=$USER_ID"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }

    assertThat(testCall.response.status()).isEqualTo(Found)
    assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
      "https://$MARKET_SERVICE_ADDRESS/ext/banStatus?userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    )
  }

  @Test
  fun `SHOULD response WHEN packageId is empty and isUsingVpn=true`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(
      method = Get,
      uri = "/get/sanityCheck?ip=${sanityCheckRequestDto.ip}&country=${sanityCheckRequestDto.countryCode}&skipCache=${sanityCheckRequestDto.skipCache}&gaid=${sanityCheckRequestDto.googleAdId}&idfv=${sanityCheckRequestDto.idfv}&user_id=${sanityCheckRequestDto.userId}"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      addHeader("vd", "true")
    }

    assertThat(testCall.response.status()).isEqualTo(OK)
    assertThat(testCall.response.headers[HttpHeaders.Location]).isNull()
    assertThat(jsonConverter.decodeFromString<SanityCheckResponseApiDto>(testCall.response.content!!))
      .isEqualTo(
        SanityCheckResponseApiDto(
          isValidUser = false,
          isFromAllowedCountry = true,
          isUsingVpn = true,
          fraudScore = 0
        )
      )
  }

  @Test
  fun `SHOULD redirect to specific market ON get sanityCheck call`() = withTestApplication(controller()) {
    val base64 = Base64.getEncoder().encodeToString(jsonConverter.encodeToString(sanityCheckRequestDto).toByteArray())
    userService.mock(
      { findUser(UserId(sanityCheckRequestDto.userId), IDFA(sanityCheckRequestDto.googleAdId), IDFV(sanityCheckRequestDto.idfv)) },
      UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID)
    )
    val testCall: TestApplicationCall = handleRequest(
      method = Get,
      uri = "/get/sanityCheck?ip=${sanityCheckRequestDto.ip}&country=${sanityCheckRequestDto.countryCode}&skipCache=${sanityCheckRequestDto.skipCache}&package=${sanityCheckRequestDto.packageId}&gaid=${sanityCheckRequestDto.googleAdId}&idfv=${sanityCheckRequestDto.idfv}&user_id=${sanityCheckRequestDto.userId}"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }

    assertThat(testCall.response.status()).isEqualTo(Found)
    assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
      "https://$MARKET_SERVICE_ADDRESS/ext/sanityCheck?sanityCheckRequest=${base64.encodeURLParameter()}&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    )
  }

  @Test
  fun `SHOULD return isValidUser=false ON get sanityCheck call when user isn't found and vpn detected`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(
      method = Get,
      uri = "/get/sanityCheck?ip=${sanityCheckRequestDto.ip}&country=${sanityCheckRequestDto.countryCode}&skipCache=${sanityCheckRequestDto.skipCache}&package=gimica&gaid=${sanityCheckRequestDto.googleAdId}&idfv=${sanityCheckRequestDto.idfv}&user_id=${sanityCheckRequestDto.userId}"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      addHeader("vd", "true")
    }

    assertThat(testCall.response.status()).isEqualTo(OK)
    assertThat(testCall.response.headers[HttpHeaders.Location]).isNull()
    assertThat(jsonConverter.decodeFromString<SanityCheckResponseApiDto>(testCall.response.content!!))
      .isEqualTo(
        SanityCheckResponseApiDto(
          isValidUser = false,
          isFromAllowedCountry = true,
          isUsingVpn = true,
          fraudScore = 0
        )
      )
  }

  @Test
  fun `SHOULD return isValidUser=true ON get sanityCheck call when user isn't found and vpn isn't detected`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(
      method = Get,
      uri = "/get/sanityCheck?ip=${sanityCheckRequestDto.ip}&country=${sanityCheckRequestDto.countryCode}&skipCache=${sanityCheckRequestDto.skipCache}&package=gimica&gaid=${sanityCheckRequestDto.googleAdId}&idfv=${sanityCheckRequestDto.idfv}&user_id=${sanityCheckRequestDto.userId}"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      addHeader("vd", "false")
    }

    assertThat(testCall.response.status()).isEqualTo(OK)
    assertThat(testCall.response.headers[HttpHeaders.Location]).isNull()
    assertThat(jsonConverter.decodeFromString<SanityCheckResponseApiDto>(testCall.response.content!!))
      .isEqualTo(
        SanityCheckResponseApiDto(
          isValidUser = true,
          isFromAllowedCountry = true,
          isUsingVpn = false,
          fraudScore = 0
        )
      )
  }

  @Test
  fun `SHOULD redirect to specific marketN get adUnitIdsByGoogleAdId call`() = withTestApplication(controller()) {
    userService.mock({ findUser(null, IDFA(GOOGLE_AD_ID), null) }, UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID))
    val packageId = "packageId"

    val testCall: TestApplicationCall = handleRequest(
      method = Get,
      uri = "/get/adUnitIdsByGoogleAdId?packageId=$packageId&googleAdId=$GOOGLE_AD_ID"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }

    assertThat(testCall.response.status()).isEqualTo(Found)
    assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
      "https://$MARKET_SERVICE_ADDRESS/ext/adUnitIds?packageId=$packageId&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
    )
  }

  @ParameterizedTest
  @ValueSource(strings = ["nothing", "googleAdId", "userId", "idfv"])
  fun `SHOULD redirect to specific market ON get adUnitIds call`(option: String) = withTestApplication(controller()) {
    when (option) {
      "nothing" -> userService.mock({ findUser(userId = null, idfa = null, idfv = null) }, null)

      "googleAdId" -> {
        val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, AppPlatform.ANDROID)
        userService.mock({ findUser(userId = null, idfa = IDFA(GOOGLE_AD_ID), idfv = null) }, user)
      }

      "userId" -> {
        val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID)
        userService.mock({ findUser(userId = UserId(USER_ID), idfa = null, idfv = null) }, user)
      }

      "idfv" -> {
        val user = UserSearchResult(WayToFindUser.IDFV, USER_ID, MARKET, IOS)
        userService.mock({ findUser(userId = null, idfa = null, idfv = IDFV(IDFV)) }, user)
      }
    }

    val packageId = "packageId"
    val testCall: TestApplicationCall
    when (option) {
      "nothing" -> {
        testCall = handleRequest(
          method = Get,
          uri = "/get/adUnitIds?packageId=$packageId"
        ) {
          addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
        }
        assertThat(testCall.response.status()).isEqualTo(NotFound)
      }

      "googleAdId" -> {
        testCall = handleRequest(
          method = Get,
          uri = "/get/adUnitIds?packageId=$packageId&googleAdId=$GOOGLE_AD_ID"
        ) {
          addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
        }

        assertThat(testCall.response.status()).isEqualTo(Found)
        assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
          "https://$MARKET_SERVICE_ADDRESS/ext/adUnitIds?packageId=$packageId&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
        )
      }

      "userId" -> {
        testCall = handleRequest(
          method = Get,
          uri = "/get/adUnitIds?packageId=$packageId&userId=$USER_ID"
        ) {
          addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
        }

        assertThat(testCall.response.status()).isEqualTo(Found)
        assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
          "https://$MARKET_SERVICE_ADDRESS/ext/adUnitIds?packageId=$packageId&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
        )
      }

      "idfv" -> {
        testCall = handleRequest(
          method = Get,
          uri = "/get/adUnitIds?packageId=$packageId&idfv=$IDFV"
        ) {
          addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
        }

        assertThat(testCall.response.status()).isEqualTo(Found)
        assertThat(testCall.response.headers[HttpHeaders.Location]).isEqualTo(
          "https://$MARKET_SERVICE_ADDRESS/ext/adUnitIds?packageId=$packageId&userId=$USER_ID&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
        )
      }
    }
  }

  @Test
  fun `SHOULD redirect to specific market ON get shouldShowEarlyInterstitial call WHEN userId defined`() =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID)

      userService.mock({ findUser(UserId(USER_ID), null, null) }, user)

      handleRequest(
        method = Get,
        uri = "/get/shouldShowEarlyInterstitial?userId=$USER_ID"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {

        assertThat(it.response.status()).isEqualTo(Found)
        assertThat(it.response.headers[HttpHeaders.Location])
          .isEqualTo(
            "https://$MARKET_SERVICE_ADDRESS/ext/shouldShowEarlyInterstitial?userId=$USER_ID" +
              "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
          )
      }
    }

  @ParameterizedTest
  @CsvSource(",", "userId=")
  fun `SHOULD redirect to specific market ON get shouldShowEarlyInterstitial call WHEN userId is empty or null and googleAdId defined`(
    userIdOption: String?
  ) =
    withTestApplication(controller()) {
      val userIdParam = if (userIdOption != null) "" else null
      val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, AppPlatform.ANDROID)

      userService.mock({ findUser(UserId(userIdParam), IDFA(GOOGLE_AD_ID), null) }, user)

      handleRequest(
        method = Get,
        uri = "/get/shouldShowEarlyInterstitial?$userIdOption&googleAdId=$GOOGLE_AD_ID"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }
        .let {
          assertThat(it.response.status()).isEqualTo(Found)
          assertThat(it.response.headers[HttpHeaders.Location])
            .isEqualTo(
              "https://$MARKET_SERVICE_ADDRESS/ext/shouldShowEarlyInterstitial?userId=$USER_ID" +
                "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
            )
        }
    }

  @ParameterizedTest
  @CsvSource(",", "userId=")
  fun `SHOULD redirect to specific market ON get shouldShowEarlyInterstitial call WHEN userId is empty or null and idfv defined`(userIdOption: String?) =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.IDFV, USER_ID, MARKET, AppPlatform.ANDROID)
      val userIdParam = if (userIdOption != null) "" else null
      userService.mock({ findUser(UserId(userIdParam), null, IDFV(IDFV)) }, user)

      handleRequest(
        method = Get,
        uri = "/get/shouldShowEarlyInterstitial?$userIdOption&idfv=$IDFV"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }
        .let {
          assertThat(it.response.status()).isEqualTo(Found)
          assertThat(it.response.headers[HttpHeaders.Location])
            .isEqualTo(
              "https://$MARKET_SERVICE_ADDRESS/ext/shouldShowEarlyInterstitial?userId=$USER_ID" +
                "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
            )
        }
    }

  @Test
  fun `SHOULD return NOT FOUND ON get shouldShowEarlyInterstitial call WHEN userId unknown`() = withTestApplication(controller()) {
    handleRequest(
      method = Get,
      uri = "/get/shouldShowEarlyInterstitial?userId=$USER_ID&googleAdId=$GOOGLE_AD_ID"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }.let {
      assertThat(it.response.status()).isEqualTo(NotFound)
    }
  }

  @Test
  fun `SHOULD return NOT FOUND ON get shouldShowEarlyInterstitial call WHEN googleAdId unknown`() = withTestApplication(controller()) {
    handleRequest(
      method = Get,
      uri = "/get/shouldShowEarlyInterstitial?googleAdId=$GOOGLE_AD_ID"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }
      .let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }
  }

  @Test
  fun `SHOULD return NOT FOUND ON get shouldShowEarlyInterstitial call WHEN idfv unknown`() =
    withTestApplication(controller()) {
      handleRequest(
        method = Get,
        uri = "/get/shouldShowEarlyInterstitial?idfv=$IDFV"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }
        .let {
          assertThat(it.response.status()).isEqualTo(NotFound)
        }
    }

  @ParameterizedTest
  @CsvSource("$USER_ID,googleAdId,idfv", "$USER_ID,googleAdId,", ",googleAdId,idfv", "$USER_ID,,idfv", "$USER_ID,,", ",googleAdID,", ",,idfv")
  fun `SHOULD resolve userIdAndMarket in accordance to parameters priorities ON get gdprState call`(userIdParam: String?, googleAdId: String?, idfv: String?) {
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(UserId(userIdParam ?: ""), IDFA(googleAdId), IDFV(idfv ?: "")) }, user)

      handleRequest(
        method = Get,
        uri = "/get/gdprState?userId=${userIdParam ?: ""}&googleAdId=${googleAdId ?: ""}&idfv=${idfv ?: ""}"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {

        assertThat(it.response.status()).isEqualTo(Found)
        assertThat(it.response.headers[HttpHeaders.Location])
          .isEqualTo(
            "https://$MARKET_SERVICE_ADDRESS/ext/gdprState?userId=$USER_ID" +
              "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
          )
      }
    }
  }

  @Test
  fun `SHOULD redirect to specific market ON get gdprState call WHEN user known`() =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(UserId(USER_ID)) }, user)

      handleRequest(
        method = Get,
        uri = "/get/gdprState?userId=$USER_ID"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {

        assertThat(it.response.status()).isEqualTo(Found)
        assertThat(it.response.headers[HttpHeaders.Location])
          .isEqualTo(
            "https://$MARKET_SERVICE_ADDRESS/ext/gdprState?userId=$USER_ID" +
              "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
          )
      }
    }

  @Test
  fun `SHOULD return NOT FOUND ON get gdprState call WHEN user not found`() =
    withTestApplication(controller()) {
      handleRequest(
        method = Get,
        uri = "/get/gdprState?userId=$USER_ID&googleAdId=$GOOGLE_AD_ID"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }
    }

  @Test
  fun `SHOULD redirect to specific market ON get _android_status call WHEN user found`() =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(UserId(USER_ID)) }, user)
      val packageId = "some-package-id"

      handleRequest(
        method = Get,
        uri = "/android/status?packageId=$packageId&userId=$USER_ID"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {

        assertThat(it.response.status()).isEqualTo(Found)
        assertThat(it.response.headers[HttpHeaders.Location])
          .isEqualTo(
            "https://$MARKET_SERVICE_ADDRESS/ext/games/android/status?packageId=$packageId&userId=$USER_ID" +
              "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
          )
      }
    }

  @Test
  fun `SHOULD throw parameter required exception ON get _android_status call WHEN packageId missing`() =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(UserId(USER_ID)) }, user)

      handleRequest(
        method = Get,
        uri = "/android/status?userId=$USER_ID"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }
        .let { call ->
          assertThat(call.response.status()).isEqualTo(HttpStatusCode.BadRequest)
          assertThat(call.response.content).isEqualTo("Required parameter 'packageId' not found for request /android/status?userId=userIdValue")
        }
    }

  @Test
  fun `SHOULD return NOT FOUND ON get _android_status call WHEN user not found`() =
    withTestApplication(controller()) {
      val packageId = "some-package-id"

      handleRequest(
        method = Get,
        uri = "/android/status?packageId=$packageId&userId=$USER_ID&idfa=$GOOGLE_AD_ID&idfv=$IDFV"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }
    }

  @Test
  fun `SHOULD redirect to specific market ON get _android_examination call WHEN user found`() =
    withTestApplication(controller()) {
      val packageId = "some-package-id"
      val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(UserId(USER_ID)) }, user)

      handleRequest(
        method = Get,
        uri = "/android/examination?packageId=$packageId&userId=$USER_ID"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {

        assertThat(it.response.status()).isEqualTo(Found)
        assertThat(it.response.headers[HttpHeaders.Location])
          .isEqualTo(
            "https://$MARKET_SERVICE_ADDRESS/ext/games/android/examination?packageId=$packageId&userId=$USER_ID" +
              "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
          )
      }
    }

  @Test
  fun `SHOULD throw parameter not found exception ON get _android_examination call WHEN packageId not defined`() = withTestApplication(controller()) {
    handleRequest(
      method = Get,
      uri = "/android/examination?userId=$USER_ID"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }.let { call ->
      assertThat(call.response.status()).isEqualTo(HttpStatusCode.BadRequest)
      assertThat(call.response.content).isEqualTo("Required parameter 'packageId' not found for request /android/examination?userId=userIdValue")
    }
  }

  @Test
  fun `SHOULD return NOT FOUND ON get _android_examination call WHEN user not found`() =
    withTestApplication(controller()) {
      val packageId = "some-package-id"

      handleRequest(
        method = Get,
        uri = "/android/examination?packageId=$packageId&userId=$USER_ID&idfa=$GOOGLE_AD_ID&idfv=$IDFV"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }
    }

  @Test
  fun `SHOULD publish event and response ok ON post _android_examination call WHEN user found`() =
    withTestApplication(controller()) {
      val examinationRequest = GameExaminationRequestApiDto(
        userId = USER_ID,
        idfa = GOOGLE_AD_ID,
        idfv = IDFV,
        packageId = "some-package-id",
        attestationStatement = "very long attestation statement",
      )
      val expected = GameExaminationRequestRouteDto(
        userId = USER_ID,
        packageId = examinationRequest.packageId,
        attestationStatement = examinationRequest.attestationStatement,
      )

      val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(UserId(USER_ID), IDFA(GOOGLE_AD_ID), IDFV(IDFV)) }, user)

      handleRequest(
        method = Post,
        uri = "/android/examination"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
        addHeader("Content-Type", "application/json")
        setBody(getDefaultJsonConverter().encodeToString(examinationRequest))
      }.let {
        assertThat(it.response.status()).isEqualTo(OK)
      }

      verifyBlocking(webhookPublisher) {
        publish(
          GameExaminationDto(expected, MARKET_SERVICE_ADDRESS, jsonConverter)
        )
      }
    }

  @Test
  fun `SHOULD publish event and response ok ON post _android_examination call WHEN user found AND some fields are not defined`() =
    withTestApplication(controller()) {
      val examinationRequest = GameExaminationRequestApiDto(
        userId = USER_ID,
        idfa = null,
        idfv = null,
        packageId = "some-package-id",
        attestationStatement = "very long attestation statement",
      )
      val expected = GameExaminationRequestRouteDto(
        userId = USER_ID,
        packageId = examinationRequest.packageId,
        attestationStatement = examinationRequest.attestationStatement,
      )

      val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(UserId(USER_ID), null, null) }, user)

      assertThat(getDefaultJsonConverter().encodeToString(examinationRequest)).isEqualTo(
        """{"userId":"$USER_ID","packageId":"some-package-id","attestationStatement":"very long attestation statement"}"""
      )

      handleRequest(
        method = Post,
        uri = "/android/examination"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
        addHeader("Content-Type", "application/json")
        setBody(getDefaultJsonConverter().encodeToString(examinationRequest))
      }.let {
        assertThat(it.response.status()).isEqualTo(OK)
      }

      verifyBlocking(webhookPublisher) {
        publish(
          GameExaminationDto(expected, MARKET_SERVICE_ADDRESS, jsonConverter)
        )
      }
    }

  @Test
  fun `SHOULD return NOT FOUND ON post _android_examination call WHEN user not found`() =
    withTestApplication(controller()) {
      val examinationRequest = GameExaminationRequestApiDto(
        userId = USER_ID,
        idfa = GOOGLE_AD_ID,
        idfv = IDFV,
        packageId = "some-package-id",
        attestationStatement = "very long attestation statement",
      )

      handleRequest(
        method = Post,
        uri = "/android/examination"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
        addHeader("Content-Type", "application/json")
        setBody(getDefaultJsonConverter().encodeToString(examinationRequest))
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }
    }

  @Test
  fun `SHOULD redirect to specific market ON get _ios-att-consent-configuration call WHEN user found`() =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.IDFV, USER_ID, MARKET, IOS)
      userService.mock({ findUser(idfv = IDFV("42")) }, user)

      handleRequest(
        method = Get,
        uri = "/ios/att-consent-configuration?idfv=42&packageId=some-package-id&gameVersion=1.32"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(Found)
        assertThat(it.response.headers[HttpHeaders.Location])
          .isEqualTo(
            "https://marketServiceAddress/ext/games/ios/att-consent-configuration?idfv=42&packageId=some-package-id&gameVersion=1.32" +
              "&userId=userIdValue" +
              "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
          )
      }
    }

  @Test
  fun `SHOULD throw parameter required exception ON get _ios-att-consent-configuration call WHEN required parameter missing`() =
    withTestApplication(controller()) {
      handleRequest(
        method = Get,
        uri = "/ios/att-consent-configuration?packageId=some-package-id&gameVersion=1.32"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }
        .let { call ->
          assertThat(call.response.status()).isEqualTo(HttpStatusCode.BadRequest)
          assertThat(call.response.content).isEqualTo("Required parameter 'idfv' not found for request /ios/att-consent-configuration?packageId=some-package-id&gameVersion=1.32")
        }
    }

  @Test
  fun `SHOULD return NOT FOUND ON get _ios-att-consent-configuration call WHEN user not found`() =
    withTestApplication(controller()) {
      userService.mock({ findUser(idfv = IDFV("42")) }, null)

      handleRequest(
        method = Get,
        uri = "/ios/att-consent-configuration?idfv=42&packageId=some-package-id&gameVersion=1.32"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }
    }

  @Test
  fun `SHOULD redirect to specific market ON get coins-booster-configuration call WHEN android and user found`() =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, IOS)
      userService.mock({ findUser(idfa = IDFA("42")) }, user)

      handleRequest(
        method = Get,
        uri = "/android/coins-booster-configuration?idfa=42&packageId=some-package-id&gameVersion=1.32"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(Found)
        assertThat(it.response.headers[HttpHeaders.Location])
          .isEqualTo(
            "https://marketServiceAddress/ext/games/android/coins-booster-configuration?packageId=some-package-id&gameVersion=1.32" +
              "&userId=userIdValue" +
              "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
          )
      }
    }

  @Test
  fun `SHOULD throw parameter required exception ON get coins-booster-configuration call WHEN android and required parameter missing`() =
    withTestApplication(controller()) {
      handleRequest(
        method = Get,
        uri = "/android/coins-booster-configuration?packageId=some-package-id"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }
        .let { call ->
          assertThat(call.response.status()).isEqualTo(HttpStatusCode.BadRequest)
          assertThat(call.response.content).isEqualTo("Required parameter 'gameVersion' not found for request /android/coins-booster-configuration?packageId=some-package-id")
        }
    }

  @Test
  fun `SHOULD redirect to specific market ON get coins-booster-configuration call WHEN ios and user found`() =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.IDFV, USER_ID, MARKET, IOS)
      userService.mock({ findUser(idfv = IDFV("42")) }, user)

      handleRequest(
        method = Get,
        uri = "/ios/coins-booster-configuration?idfv=42&packageId=some-package-id&gameVersion=1.32"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(Found)
        assertThat(it.response.headers[HttpHeaders.Location])
          .isEqualTo(
            "https://marketServiceAddress/ext/games/ios/coins-booster-configuration?idfv=42&packageId=some-package-id&gameVersion=1.32" +
              "&userId=userIdValue" +
              "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
          )
      }
    }

  @Test
  fun `SHOULD throw parameter required exception ON get coins-booster-configuration call WHEN ios and required parameter missing`() =
    withTestApplication(controller()) {
      handleRequest(
        method = Get,
        uri = "/ios/coins-booster-configuration?packageId=some-package-id&gameVersion=1.32"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }
        .let { call ->
          assertThat(call.response.status()).isEqualTo(HttpStatusCode.BadRequest)
          assertThat(call.response.content).isEqualTo("Required parameter 'idfv' not found for request /ios/coins-booster-configuration?packageId=some-package-id&gameVersion=1.32")
        }
    }

  @Test
  fun `SHOULD return NOT FOUND ON get coins-booster-configuration call WHEN uios and ser not found`() =
    withTestApplication(controller()) {
      userService.mock({ findUser(idfv = IDFV("42")) }, null)

      handleRequest(
        method = Get,
        uri = "/ios/coins-booster-configuration?idfv=42&packageId=some-package-id&gameVersion=1.32"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }
    }

  @Test
  fun `SHOULD return NOT FOUND ON get coins-booster-configuration call WHEN android and user not found`() =
    withTestApplication(controller()) {
      userService.mock({ findUser(idfv = IDFV("42")) }, null)

      handleRequest(
        method = Get,
        uri = "/ios/coins-booster-configuration?idfv=42&packageId=some-package-id&gameVersion=1.32"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }
    }

  @Test
  fun `SHOULD call service layer properly ON _ios-track-consent WHEN valid request`() =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.IDFV, USER_ID, MARKET, IOS)
      userService.mock({ findUser(idfv = IDFV("42")) }, user)
      timeService.mock({ now() }, Instant.parse("2024-06-01T11:22:33.44Z"))

      handleRequest(
        method = Post,
        uri = "/ios/track-consent?idfv=42&idfa=43&packageId=some-package-id&gameVersion=1.32"
      ) {
        setBody("""{"status":"AUTHORIZED"}""")
        addHeader(HttpHeaders.ContentType, "application/json")
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(OK)
      }

      verifyBlocking(iosGameAttConsentService, times(1)) { trackConsentStatus(iosGameAttConsentTrackingDataStub) }
    }

  @Test
  fun `SHOULD  throw parameter required exception ON _ios-track-consent WHEN not valid request`() =
    withTestApplication(controller()) {
      handleRequest(
        method = Post,
        uri = "/ios/track-consent?idfa=43&packageId=some-package-id&gameVersion=1.32"
      ) {
        setBody("""{"status":"accepted"}""")
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(HttpStatusCode.BadRequest)
        assertThat(it.response.content).isEqualTo("Required parameter 'idfv' not found for request /ios/track-consent?idfa=43&packageId=some-package-id&gameVersion=1.32")
      }

      verifyNoInteractions(iosGameAttConsentService)
    }

  @Test
  fun `SHOULD respond 404 ON _ios-track-consent WHEN not valid request`() =
    withTestApplication(controller()) {
      userService.mock({ findUser(idfv = IDFV("42")) }, null)

      handleRequest(
        method = Post,
        uri = "/ios/track-consent?idfv=42&idfa=43&packageId=some-package-id&gameVersion=1.32"
      ) {
        setBody("""{"status":"AUTHORIZED"}""")
        addHeader(HttpHeaders.ContentType, "application/json")
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }

      verifyNoInteractions(iosGameAttConsentService)
    }

  @Test
  fun `SHOULD redirect to specific market ON get _ios_status call WHEN user found`() =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, IOS)
      userService.mock({ findUser(UserId(USER_ID)) }, user)
      val packageId = "some-package-id"

      handleRequest(
        method = Get,
        uri = "/ios/status?packageId=$packageId&userId=$USER_ID"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {

        assertThat(it.response.status()).isEqualTo(Found)
        assertThat(it.response.headers[HttpHeaders.Location])
          .isEqualTo(
            "https://$MARKET_SERVICE_ADDRESS/ext/games/ios/status?packageId=$packageId&userId=$USER_ID" +
              "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
          )
      }
    }

  @Test
  fun `SHOULD throw parameter required exception ON get _ios_status call WHEN packageId missing`() =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, IOS)
      userService.mock({ findUser(UserId(USER_ID)) }, user)

      handleRequest(
        method = Get,
        uri = "/ios/status?userId=$USER_ID"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }
        .let { call ->
          assertThat(call.response.status()).isEqualTo(HttpStatusCode.BadRequest)
          assertThat(call.response.content).isEqualTo("Required parameter 'packageId' not found for request /ios/status?userId=userIdValue")
        }
    }

  @Test
  fun `SHOULD return NOT FOUND ON get _ios_status call WHEN user not found`() =
    withTestApplication(controller()) {
      val packageId = "some-package-id"

      handleRequest(
        method = Get,
        uri = "/ios/status?packageId=$packageId&userId=$USER_ID&idfa=$IDFA&idfv=$IDFV"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }
    }

  @ParameterizedTest
  @ValueSource(strings = ["status", "token", "refresh"])
  fun `SHOULD redirect to correct market ON unifiedid endpoints WHEN user found`(method: String) {
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(UserId(USER_ID)) }, user)

      handleRequest(
        method = Get,
        uri = "/unifiedid/$method?userId=$USER_ID&packageId=some-package-id"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(Found)
        assertThat(it.response.headers[HttpHeaders.Location])
          .isEqualTo(
            "https://$MARKET_SERVICE_ADDRESS/ext/unifiedid/$method?userId=$USER_ID" +
              "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
          )
      }
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["status", "token", "refresh"])
  fun `SHOULD return 404 ON unifiedid endpoints WHEN user not found`(method: String) {
    withTestApplication(controller()) {
      userService.mock({ findUser(UserId(USER_ID)) }, null)

      handleRequest(
        method = Get,
        uri = "/unifiedid/$method?userId=$USER_ID&packageId=some-package-id"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["status", "emailHash"])
  fun `SHOULD redirect to correct market ON rampid endpoints WHEN user found`(path: String) {
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(UserId(USER_ID)) }, user)

      handleRequest(
        method = Get,
        uri = "/rampid/$path?userId=$USER_ID"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(Found)
        assertThat(it.response.headers[HttpHeaders.Location])
          .isEqualTo(
            "https://$MARKET_SERVICE_ADDRESS/ext/rampid/$path?userId=$USER_ID" +
              "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
          )
      }
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["status", "emailHash"])
  fun `SHOULD return 404 ON rampid endpoints WHEN user not found`(path: String) {
    withTestApplication(controller()) {
      userService.mock({ findUser(UserId(USER_ID)) }, null)

      handleRequest(
        method = Get,
        uri = "/rampid/$path?userId=$USER_ID"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }
    }
  }

  @Test
  fun `SHOULD redirect to specific market ON get celebration-configuration call WHEN android and user found`() =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, IOS)
      userService.mock({ findUser(idfa = IDFA("42")) }, user)

      handleRequest(
        method = Get,
        uri = "/android/celebration-configuration?idfa=42&packageId=some-package-id&gameVersion=1.32"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(Found)
        assertThat(it.response.headers[HttpHeaders.Location])
          .isEqualTo(
            "https://marketServiceAddress/ext/games/android/celebration-configuration?packageId=some-package-id&gameVersion=1.32" +
              "&userId=userIdValue" +
              "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
          )
      }
    }

  @Test
  fun `SHOULD throw parameter required exception ON get celebration-configuration call WHEN android and required parameter missing`() =
    withTestApplication(controller()) {
      handleRequest(
        method = Get,
        uri = "/android/celebration-configuration?packageId=some-package-id"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }
        .let { call ->
          assertThat(call.response.status()).isEqualTo(HttpStatusCode.BadRequest)
          assertThat(call.response.content).isEqualTo("Required parameter 'gameVersion' not found for request /android/celebration-configuration?packageId=some-package-id")
        }
    }

  @Test
  fun `SHOULD redirect to specific market ON get celebration-configuration call WHEN ios and user found`() =
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.IDFV, USER_ID, MARKET, IOS)
      userService.mock({ findUser(idfv = IDFV("42")) }, user)

      handleRequest(
        method = Get,
        uri = "/ios/celebration-configuration?idfv=42&packageId=some-package-id&gameVersion=1.32"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(Found)
        assertThat(it.response.headers[HttpHeaders.Location])
          .isEqualTo(
            "https://marketServiceAddress/ext/games/ios/celebration-configuration?idfv=42&packageId=some-package-id&gameVersion=1.32" +
              "&userId=userIdValue" +
              "&$AUTHENTICATION_TOKEN_PARAM_NAME=$AUTHENTICATION_TOKEN_VALUE"
          )
      }
    }

  @Test
  fun `SHOULD throw parameter required exception ON get celebration-configuration call WHEN ios and required parameter missing`() =
    withTestApplication(controller()) {
      handleRequest(
        method = Get,
        uri = "/ios/celebration-configuration?packageId=some-package-id&gameVersion=1.32"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }
        .let { call ->
          assertThat(call.response.status()).isEqualTo(HttpStatusCode.BadRequest)
          assertThat(call.response.content).isEqualTo("Required parameter 'idfv' not found for request /ios/celebration-configuration?packageId=some-package-id&gameVersion=1.32")
        }
    }

  @Test
  fun `SHOULD return NOT FOUND ON get celebration-configuration call WHEN ios and user not found`() =
    withTestApplication(controller()) {
      userService.mock({ findUser(idfv = IDFV("42")) }, null)

      handleRequest(
        method = Get,
        uri = "/ios/celebration-configuration?idfv=42&packageId=some-package-id&gameVersion=1.32"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }
    }

  @Test
  fun `SHOULD return NOT FOUND ON get celebration-configuration call WHEN android and user not found`() =
    withTestApplication(controller()) {
      userService.mock({ findUser(idfv = IDFV("42")) }, null)

      handleRequest(
        method = Get,
        uri = "/ios/celebration-configuration?idfv=42&packageId=some-package-id&gameVersion=1.32"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
      }
    }

  @Test
  fun `SHOULD respond with user id market and app platform ON webclient get-userid endpoint WHEN user found`() {
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.IDFV, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(idfv = IDFV(IDFV)) }, user)

      handleRequest(
        method = Get,
        uri = "/webclient/get-userid?idfv=$IDFV"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(OK)
        assertThat(it.response.content).isEqualTo("""{"userId":"userIdValue","market":"marketValue","platform":"ANDROID"}""")

      }
    }
  }

  @Test
  fun `SHOULD push pub message ON webclient register-user endpoint call WHEN user found`() {
    withTestApplication(controller()) {
      val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.ANDROID)
      userService.mock({ findUser(userId = UserId("userId"), idfa = IDFA(null), idfv = IDFV(null)) }, user)
      trackingService.mock({ getProjectNameByMarket(user.market) }, "projectNameN")

      handleRequest(
        method = Post,
        uri = "/webclient/register-user?userId=userId"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
        addHeader(HttpHeaders.ContentType, "application/json")
        setBody("""{"idfv":"idfv","firebaseDeviceToken":"firebaseDeviceToken","notificationEnabled":false}""")
      }.let {
        assertThat(it.response.status()).isEqualTo(OK)
        verifyBlocking(messagePublisher) {
          publishCrossProject(
            WebAppUserRegistrationEventDto(
              userId = user.userId,
              idfv = "idfv",
              deviceToken = "firebaseDeviceToken",
              notificationEnabled = false,
              projectName = "projectNameN"
            )
          )
        }
      }
    }
  }

  @Test
  fun `SHOULD return 404 not found ON webclient register-user endpoint call WHEN user was not found`() {
    withTestApplication(controller()) {
      userService.mock({ findUser(userId = UserId("userId"), idfa = IDFA(null), idfv = IDFV(null)) }, null)

      handleRequest(
        method = Post,
        uri = "/webclient/register-user?userId=userId"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
        addHeader(HttpHeaders.ContentType, "application/json")
        setBody("""{"idfv":"idfv","deviceToken":"deviceToken","notificationEnabled":false}""")
      }.let {
        assertThat(it.response.status()).isEqualTo(NotFound)
        verifyNoInteractions(messagePublisher, trackingService)
      }
    }
  }

  @Test
  fun `SHOULD return 200 OK but with empty JSON ON webclient get-userid endpoints WHEN user not found`() {
    withTestApplication(controller()) {
      userService.mock({ findUser(idfv = IDFV(USER_ID)) }, null)

      handleRequest(
        method = Get,
        uri = "/webclient/get-userid?idfv=$USER_ID"
      ) {
        addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      }.let {
        assertThat(it.response.status()).isEqualTo(OK)
        assertThat(it.response.content).isEqualTo("{}")
      }
    }
  }

  @Test
  fun `SHOULD get missing fields and verifications data from playtime ON get user-status`(): Unit = withTestApplication(controller()) {
    val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.IOS_WEB)
    userService.mock({ findUser(userId = UserId(USER_ID), idfa = IDFA(null), idfv = IDFV(null)) }, user)
    trackingService.mock({ getServiceAddressByMarket(MARKET, ServicesRegistry.PLAYTIME) }, "playtime")
    val expected = WebUserStatusDto(
      requiredDataFields = listOf(WebUserAdditionalData.IDFA),
      verifications = listOf(WebUsersInGameVerification.JAIL_BREAK)
    )

    httpClientHandler.mockUrl("https://playtime/service/ext/games/webclient/$USER_ID/status") { _, scope ->
      scope.respond(
        content = """{"requiredDataFields":["IDFA"], "verifications":["JAIL_BREAK"]}""",
        headers = headersOf("Content-Type" to listOf("application/json"))
      )
    }

    val resp = handleRequest(
      method = Get,
      uri = "/webclient/user-status?userId=$USER_ID"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }

    assertThat(resp.response.status()).isEqualTo(OK)
    assertThat(resp.response.content).isEqualTo(Json.encodeToString(expected))
  }

  @Test
  fun `SHOULD provide attestation challenge ON get examination-challenge get call`(): Unit = withTestApplication(controller()) {
    val applicationId = "com.gimica.treasuremaster"
    val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.IOS_WEB)
    userService.mock({ findUser(userId = UserId(USER_ID), idfa = IDFA(null), idfv = IDFV(null)) }, user)
    trackingService.mock({ getServiceAddressByMarket(MARKET, ServicesRegistry.PLAYTIME) }, "playtime")

    val expected = WebAppExaminationChallengeApiDto(
      challenge = "some random uuid here"
    )

    httpClientHandler.mockUrl("https://playtime/service/ext/games/webclient/$USER_ID/examination-challenge?applicationId=$applicationId") { _, scope ->
      scope.respond(
        content = """{"challenge":"some random uuid here"}""",
        headers = headersOf("Content-Type" to listOf("application/json"))
      )
    }

    val resp = handleRequest(
      method = Get,
      uri = "/webclient/examination-challenge?userId=$USER_ID&packageId=$applicationId"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
    }

    assertThat(resp.response.status()).isEqualTo(OK)
    assertThat(resp.response.content).isEqualTo(Json.encodeToString(expected))
  }

  @Test
  fun `SHOULD post gps location check data  ON post gps location`(): Unit = withTestApplication(controller()) {
    val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.IOS_WEB)
    userService.mock({ findUser(userId = UserId(USER_ID), idfa = IDFA(null), idfv = IDFV(null)) }, user)
    trackingService.mock({ getServiceAddressByMarket(MARKET, ServicesRegistry.PLAYTIME) }, "playtime")
    httpClientHandler.mockUrl("https://playtime/service/ext/games/webclient/$USER_ID/gps-location") { _, scope ->
      scope.respondOk()
    }

    val resp = handleRequest(
      method = Post,
      uri = "/webclient/gps-location?userId=$USER_ID&packageId=com.gimica.bubblepop&verificationSessionId=someSessionId"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      addHeader(HttpHeaders.ContentType, "application/json")
      setBody("""{"provided":true, "location": "US", "isMocked": false}""")
    }

    assertThat(resp.response.status()).isEqualTo(OK)

  }

  @Test
  fun `SHOULD publish cross service pub sub message ON post user data request`(): Unit = withTestApplication(controller()) {
    val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.IOS_WEB)
    userService.mock({ findUser(userId = UserId(USER_ID), idfa = IDFA(null), idfv = IDFV(null)) }, user)
    trackingService.mock({ getServiceAddressByMarket(MARKET, ServicesRegistry.PLAYTIME) }, "https://playtime")
    trackingService.mock({ getProjectNameByMarket(user.market) }, "projectNameN")
    val expected = WebAppUserAdditionalDataEventDto(
      userId = USER_ID,
      projectName = "projectNameN",
      idfv = "idfv",
      idfa = null,
      deviceToken = "deviceToken",
      firebaseAppInstanceId = null,
      notificationEnabled = false,
      adjustId = null,
    )

    handleRequest(
      method = Post,
      uri = "/webclient/user-data?userId=$USER_ID"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      addHeader(HttpHeaders.ContentType, "application/json")
      setBody("""{"idfv":"idfv","firebaseDeviceToken":"deviceToken","notificationEnabled":false}""")
    }.let {
      assertThat(it.response.status()).isEqualTo(OK)
      verifyBlocking(messagePublisher) {
        publishCrossProject(expected)
      }
    }
  }

  @Test
  fun `SHOULD publish cross service pub sub message ON post jail break check data request`(): Unit = withTestApplication(controller()) {
    val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.IOS_WEB)
    userService.mock({ findUser(userId = UserId(USER_ID), idfa = IDFA(null), idfv = IDFV(null)) }, user)
    trackingService.mock({ getServiceAddressByMarket(MARKET, ServicesRegistry.PLAYTIME) }, "https://playtime")
    trackingService.mock({ getProjectNameByMarket(user.market) }, "projectNameN")
    val expected = WebAppUserJailBreakCheckEventDto(
      userId = USER_ID,
      projectName = "projectNameN",
      jailBreak = true
    )

    handleRequest(
      method = Post,
      uri = "/webclient//jail-break?userId=$USER_ID"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      addHeader(HttpHeaders.ContentType, "application/json")
      setBody("""{"jailBreak":true}""")
    }.let {
      assertThat(it.response.status()).isEqualTo(OK)
      verifyBlocking(messagePublisher) {
        publishCrossProject(expected)
      }
    }
  }

  @Test
  fun `SHOULD publish cross service pub sub message ON post user data request WHEN more data provided`(): Unit = withTestApplication(controller()) {
    val user = UserSearchResult(WayToFindUser.USER_ID, USER_ID, MARKET, AppPlatform.IOS_WEB)
    userService.mock({ findUser(userId = UserId(USER_ID), idfa = IDFA(null), idfv = IDFV(null)) }, user)
    trackingService.mock({ getServiceAddressByMarket(MARKET, ServicesRegistry.PLAYTIME) }, "https://playtime")
    trackingService.mock({ getProjectNameByMarket(user.market) }, "projectNameN")
    val expected = WebAppUserAdditionalDataEventDto(
      userId = USER_ID,
      projectName = "projectNameN",
      idfv = "idfv",
      idfa = "idfa",
      deviceToken = "deviceToken",
      firebaseAppInstanceId = "firebaseAppInstanceId",
      notificationEnabled = false,
      adjustId = "adjustId",
    )

    handleRequest(
      method = Post,
      uri = "/webclient/user-data?userId=$USER_ID"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      addHeader(HttpHeaders.ContentType, "application/json")
      setBody("""{"adjustId":"adjustId","idfv":"idfv","idfa":"idfa","firebaseDeviceToken":"deviceToken","notificationEnabled":false,"firebaseAppInstanceId":"firebaseAppInstanceId"}""")
    }.let {
      assertThat(it.response.status()).isEqualTo(OK)
      verifyBlocking(messagePublisher) {
        publishCrossProject(expected)
      }
    }
  }

  @Test
  fun `SHOULD return 200 and body WHEN android status v2 AND user found`(): Unit = withTestApplication(controller()) {
    val request = gameStatusRequest {
      this.user = userIds {
        idfa = GOOGLE_AD_ID.toStringValue()
      }
    }.let { JsonFormat.printer().print(it) }

    val expectedResponse = gameStatusResponse {
      userId = USER_ID
    }

    val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, AppPlatform.ANDROID)
    userService.mock({ findUser(idfa = IDFA(GOOGLE_AD_ID)) }, user)
    trackingService.mock({ getServiceAddressByMarket(MARKET, ServicesRegistry.PLAYTIME) }, "playtime")

    httpClientHandler.mockUrl("https://playtime/service/ext/games/android/$USER_ID/status") { _, scope ->
      scope.respond("""{"payload":"${expectedResponse.encodeToBase64()}"}""", headers = headersOf("Content-Type" to listOf("application/json")))
    }

    val resp = handleRequest(
      method = Post,
      uri = "/android/status/v2"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      addHeader("Content-Type", "application/json")
      setBody(request)
    }

    assertThat(resp.response.status()).isEqualTo(OK)
    assertThat(resp.response.content).isNotNull().transform { resp -> GameStatusResponse.newBuilder().also { JsonFormat.parser().merge(resp, it) }.build() }
      .isEqualTo(expectedResponse)
  }

  @Test
  fun `SHOULD return 200 and body WHEN ios status v2 AND user found`(): Unit = withTestApplication(controller()) {
    val request = gameStatusRequest {
      this.user = userIds {
        idfa = GOOGLE_AD_ID.toStringValue()
      }
    }.let { JsonFormat.printer().print(it) }

    val expectedResponse = gameStatusResponse {
      userId = USER_ID
    }

    val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, AppPlatform.ANDROID)
    userService.mock({ findUser(idfa = IDFA(GOOGLE_AD_ID)) }, user)
    trackingService.mock({ getServiceAddressByMarket(MARKET, ServicesRegistry.PLAYTIME) }, "playtime")

    httpClientHandler.mockUrl("https://playtime/service/ext/games/ios/$USER_ID/status") { _, scope ->
      scope.respond("""{"payload":"${expectedResponse.encodeToBase64()}"}""", headers = headersOf("Content-Type" to listOf("application/json")))
    }

    val resp = handleRequest(
      method = Post,
      uri = "/ios/status/v2"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      addHeader("Content-Type", "application/json")
      setBody(request)
    }

    assertThat(resp.response.status()).isEqualTo(OK)
    assertThat(resp.response.content).isNotNull().transform { resp -> GameStatusResponse.newBuilder().also { JsonFormat.parser().merge(resp, it) }.build() }
      .isEqualTo(expectedResponse)
  }

  @ParameterizedTest
  @MethodSource("platformProvider")
  fun `SHOULD return not_found ON POST first-launch WHEN user not found`(platform: AppPlatform, platformPath: String) = withTestApplication(controller()) {
    userService.mock({ findUser(any(), any(), any()) }, null)

    val request = FirstLaunchRequest.newBuilder().apply {
      user = userIds {
        idfa = GOOGLE_AD_ID.toStringValue()
      }
      packageId = "some-package-id"
      gameVersion = "1.0.0"
    }.build().let { JsonFormat.printer().print(it) }

    val resp = handleRequest(
      method = Post,
      uri = "/$platformPath/first-launch"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      addHeader("Content-Type", "application/json")
      setBody(request)
    }

    assertThat(resp.response.status()).isEqualTo(NotFound)
  }

  @ParameterizedTest
  @MethodSource("platformProvider")
  fun `SHOULD return OK ON POST first-launch WHEN user found`(platform: AppPlatform, platformPath: String) = withTestApplication(controller()) {
    val request = FirstLaunchRequest.newBuilder().apply {
      user = userIds {
        idfa = GOOGLE_AD_ID.toStringValue()
      }
      packageId = "some-package-id"
      gameVersion = "1.0.0"
    }.build().let { JsonFormat.printer().print(it) }

    val user = UserSearchResult(WayToFindUser.IDFA, USER_ID, MARKET, platform)
    userService.mock({ findUser(UserId(null), IDFA(GOOGLE_AD_ID), IDFV(null)) }, user)
    trackingService.mock({ getServiceAddressByMarket(MARKET, ServicesRegistry.PLAYTIME) }, "playtime")

    httpClientHandler.mockUrl("https://playtime/service/ext/games/$platformPath/$USER_ID/first-launch") { _, scope ->
      scope.respond("", headers = headersOf("Content-Type" to listOf("application/json")))
    }

    val resp = handleRequest(
      method = Post,
      uri = "/$platformPath/first-launch"
    ) {
      addHeader(AUTHORIZATION, "Basic " + "$REDIRECT_BASIC_AUTH_USERNAME:$PLAYTIME_PASSWORD".base64Encoded())
      addHeader("Content-Type", "application/json")
      setBody(request)
    }

    assertThat(resp.response.status()).isEqualTo(OK)
  }
}
